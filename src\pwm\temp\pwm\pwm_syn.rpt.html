<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>synthesis Report</title>
<style type="text/css">
body { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
div#main_wrapper{ width: 100%; }
div#content { margin-left: 350px; margin-right: 30px; }
div#catalog_wrapper {position: fixed; top: 30px; width: 350px; float: left; }
div#catalog ul { list-style-type: none; }
div#catalog li { text-align: left; list-style-type:circle; color: #0084ff; margin-top: 3px; margin-bottom: 3px; }
div#catalog a { display:inline-block; text-decoration: none; color: #0084ff; font-weight: bold; padding: 3px; }
div#catalog a:visited { color: #0084ff; }
div#catalog a:hover { color: #fff; background: #0084ff; }
hr { margin-top: 30px; margin-bottom: 30px; }
h1, h3 { text-align: center; }
h1 {margin-top: 50px; }
table, th, td { border: 1px solid #aaa; }
table { border-collapse:collapse; margin-top: 10px; margin-bottom: 20px; width: 100%; }
th, td { padding: 5px 5px 5px 5px; }
th { color: #fff; font-weight: bold; background-color: #0084ff; }
table.summary_table td.label { width: 24%; min-width: 200px; background-color: #dee8f4; }
table.detail_table td.label { min-width: 100px; width: 8%;}
</style>
</head>
<body>
<div id="main_wrapper">
<div id="catalog_wrapper">
<div id="catalog">
<ul>
<li><a href="#about" style=" font-size: 16px;">Synthesis Messages</a></li>
<li><a href="#summary" style=" font-size: 16px;">Synthesis Details</a></li>
<li><a href="#resource" style=" font-size: 16px;">Resource</a>
<ul>
<li><a href="#usage" style=" font-size: 14px;">Resource Usage Summary</a></li>
<li><a href="#utilization" style=" font-size: 14px;">Resource Utilization Summary</a></li>
</ul>
</li>
<li><a href="#timing" style=" font-size: 16px;">Timing</a>
<ul>
<li><a href="#clock" style=" font-size: 14px;">Clock Summary</a></li>
<li><a href="#performance" style=" font-size: 14px;">Max Frequency Summary</a></li>
<li><a href="#detail timing" style=" font-size: 14px;">Detail Timing Paths Informations</a></li>
</ul>
</li>
</ul>
</div><!-- catalog -->
</div><!-- catalog_wrapper -->
<div id="content">
<h1><a name="about">Synthesis Messages</a></h1>
<table class="summary_table">
<tr>
<td class="label">Report Title</td>
<td>GowinSynthesis Report</td>
</tr>
<tr>
<td class="label">Design File</td>
<td>G:\Gowin\Gowin_V1.9.11.03_Education_x64_win\Gowin\Gowin_V1.9.11.03_Education_x64\IDE\ipcore\PWM\data\pwm_wrapper.v<br>
G:\Gowin\Gowin_V1.9.11.03_Education_x64_win\Gowin\Gowin_V1.9.11.03_Education_x64\IDE\ipcore\PWM\data\pwm.v<br>
</td>
</tr>
<tr>
<td class="label">GowinSynthesis Constraints File</td>
<td>---</td>
</tr>
<tr>
<td class="label">Tool Version</td>
<td>V1.9.11.03 Education</td>
</tr>
<tr>
<td class="label">Part Number</td>
<td>GW2A-LV18PG256C8/I7</td>
</tr>
<tr>
<td class="label">Device</td>
<td>GW2A-18</td>
</tr>
<tr>
<td class="label">Device Version</td>
<td>C</td>
</tr>
<tr>
<td class="label">Created Time</td>
<td>Wed Jul 30 10:55:41 2025
</td>
</tr>
<tr>
<td class="label">Legal Announcement</td>
<td>Copyright (C)2014-2025 Gowin Semiconductor Corporation. ALL rights reserved.</td>
</tr>
</table>
<h1><a name="summary">Synthesis Details</a></h1>
<table class="summary_table">
<tr>
<td class="label">Top Level Module</td>
<td>PWM_Top</td>
</tr>
<tr>
<td class="label">Synthesis Process</td>
<td>Running parser:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.047s, Peak memory usage = 24.191MB<br/>Running netlist conversion:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0s, Peak memory usage = 24.191MB<br/>Running device independent optimization:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 0: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.001s, Peak memory usage = 24.191MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 1: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0s, Peak memory usage = 24.191MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 2: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.002s, Peak memory usage = 24.191MB<br/>Running inference:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 0: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0s, Peak memory usage = 24.191MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 1: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0s, Peak memory usage = 24.191MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 2: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0s, Peak memory usage = 24.191MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 3: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0s, Peak memory usage = 24.191MB<br/>Running technical mapping:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 0: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.001s, Peak memory usage = 24.191MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 1: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0s, Peak memory usage = 24.191MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 2: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0s, Peak memory usage = 24.191MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 3: CPU time = 0h 0m 0.281s, Elapsed time = 0h 0m 0.297s, Peak memory usage = 52.328MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 4: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.001s, Peak memory usage = 52.328MB<br/>Generate output files:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.007s, Peak memory usage = 52.328MB<br/></td>
</tr>
<tr>
<td class="label">Total Time and Memory Usage</td>
<td>CPU time = 0h 0m 0.311s, Elapsed time = 0h 0m 0.356s, Peak memory usage = 52.328MB</td>
</tr>
</table>
<h1><a name="resource">Resource</a></h1>
<h2><a name="usage">Resource Usage Summary</a></h2>
<table class="summary_table">
<tr>
<td class="label"><b>Resource</b></td>
<td><b>Usage</b></td>
</tr>
<tr>
<td class="label"><b>I/O Port </b></td>
<td>32</td>
</tr>
<tr>
<td class="label"><b>I/O Buf </b></td>
<td>29</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspIBUF</td>
<td>28</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspOBUF</td>
<td>1</td>
</tr>
<tr>
<td class="label"><b>Register </b></td>
<td>49</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFF</td>
<td>9</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFE</td>
<td>26</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFR</td>
<td>14</td>
</tr>
<tr>
<td class="label"><b>LUT </b></td>
<td>62</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT2</td>
<td>7</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT3</td>
<td>12</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspLUT4</td>
<td>43</td>
</tr>
<tr>
<td class="label"><b>ALU </b></td>
<td>13</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspALU</td>
<td>13</td>
</tr>
<tr>
<td class="label"><b>INV </b></td>
<td>4</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspINV</td>
<td>4</td>
</tr>
<tr>
<td class="label"><b>IOLOGIC </b></td>
<td>1</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspOSER8</td>
<td>1</td>
</tr>
</table>
<h2><a name="utilization">Resource Utilization Summary</a></h2>
<table class="summary_table">
<tr>
<td class="label"><b>Resource</b></td>
<td><b>Usage</b></td>
<td><b>Utilization</b></td>
</tr>
<tr>
<td class="label">Logic</td>
<td>79(66 LUT, 13 ALU) / 20736</td>
<td><1%</td>
</tr>
<tr>
<td class="label">Register</td>
<td>49 / 16173</td>
<td><1%</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp--Register as Latch</td>
<td>0 / 16173</td>
<td>0%</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp--Register as FF</td>
<td>49 / 16173</td>
<td><1%</td>
</tr>
<tr>
<td class="label">BSRAM</td>
<td>0 / 46</td>
<td>0%</td>
</tr>
</table>
<h1><a name="timing">Timing</a></h1>
<h2><a name="clock">Clock Summary:</a></h2>
<table class="summary_table">
<tr>
<th>NO.</th>
<th>Clock Name</th>
<th>Type</th>
<th>Period</th>
<th>Frequency(MHz)</th>
<th>Rise</th>
<th>Fall</th>
<th>Source</th>
<th>Master</th>
<th>Object</th>
</tr>
<tr>
<td>1</td>
<td>pclk</td>
<td>Base</td>
<td>10.000</td>
<td>100.000</td>
<td>0.000</td>
<td>5.000</td>
<td> </td>
<td> </td>
<td>pclk_ibuf/I </td>
</tr>
<tr>
<td>2</td>
<td>fclk</td>
<td>Base</td>
<td>10.000</td>
<td>100.000</td>
<td>0.000</td>
<td>5.000</td>
<td> </td>
<td> </td>
<td>fclk_ibuf/I </td>
</tr>
</table>
<h2><a name="performance">Max Frequency Summary:</a></h2>
<table class="summary_table">
<tr>
<th>NO.</th>
<th>Clock Name</th>
<th>Constraint</th>
<th>Actual Fmax</th>
<th>Logic Level</th>
<th>Entity</th>
</tr>
<tr>
<td>1</td>
<td>pclk</td>
<td>100.000(MHz)</td>
<td>178.069(MHz)</td>
<td>7</td>
<td>TOP</td>
</tr>
</table>
<h2><a name="detail timing">Detail Timing Paths Information</a></h2>
<h3>Path&nbsp1</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>4.384</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.941</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_pwm/oserx8_duty_cycle_all_one_0_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_pwm/d_0_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>pclk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>pclk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>pclk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>pclk_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>50</td>
<td>pclk_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_pwm/oserx8_duty_cycle_all_one_0_s0/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>2</td>
<td>u_pwm/oserx8_duty_cycle_all_one_0_s0/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_pwm/n241_s0/I1</td>
</tr>
<tr>
<td>1.636</td>
<td>0.570</td>
<td>tINS</td>
<td>FR</td>
<td>1</td>
<td>u_pwm/n241_s0/COUT</td>
</tr>
<tr>
<td>1.636</td>
<td>0.000</td>
<td>tNET</td>
<td>RR</td>
<td>2</td>
<td>u_pwm/n242_s0/CIN</td>
</tr>
<tr>
<td>1.671</td>
<td>0.035</td>
<td>tINS</td>
<td>RF</td>
<td>1</td>
<td>u_pwm/n242_s0/COUT</td>
</tr>
<tr>
<td>1.671</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_pwm/n243_s0/CIN</td>
</tr>
<tr>
<td>1.706</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n243_s0/COUT</td>
</tr>
<tr>
<td>1.706</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_pwm/n244_s0/CIN</td>
</tr>
<tr>
<td>1.742</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n244_s0/COUT</td>
</tr>
<tr>
<td>1.742</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_pwm/n245_s0/CIN</td>
</tr>
<tr>
<td>1.777</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>6</td>
<td>u_pwm/n245_s0/COUT</td>
</tr>
<tr>
<td>2.251</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n292_s2/I0</td>
</tr>
<tr>
<td>2.768</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>u_pwm/n292_s2/F</td>
</tr>
<tr>
<td>3.242</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n295_s2/I3</td>
</tr>
<tr>
<td>3.613</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>3</td>
<td>u_pwm/n295_s2/F</td>
</tr>
<tr>
<td>4.087</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n297_s1/I2</td>
</tr>
<tr>
<td>4.540</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>2</td>
<td>u_pwm/n297_s1/F</td>
</tr>
<tr>
<td>5.014</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n298_s1/I2</td>
</tr>
<tr>
<td>5.467</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n298_s1/F</td>
</tr>
<tr>
<td>5.941</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/d_0_s0/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>pclk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>pclk_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>50</td>
<td>pclk_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_pwm/d_0_s0/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_pwm/d_0_s0</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>7</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 2.505, 44.882%; route: 2.844, 50.961%; tC2Q: 0.232, 4.157%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp2</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>5.219</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.106</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_pwm/up_d0_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_pwm/duty_cycle_int_2_s1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>pclk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>pclk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>pclk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>pclk_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>50</td>
<td>pclk_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_pwm/up_d0_s0/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>6</td>
<td>u_pwm/up_d0_s0/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n55_s3/I1</td>
</tr>
<tr>
<td>1.621</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>10</td>
<td>u_pwm/n55_s3/F</td>
</tr>
<tr>
<td>2.095</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n60_s2/I0</td>
</tr>
<tr>
<td>2.612</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n60_s2/F</td>
</tr>
<tr>
<td>3.086</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n60_s1/I1</td>
</tr>
<tr>
<td>3.641</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n60_s1/F</td>
</tr>
<tr>
<td>4.115</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n60_s3/I0</td>
</tr>
<tr>
<td>4.632</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n60_s3/F</td>
</tr>
<tr>
<td>5.106</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/duty_cycle_int_2_s1/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>pclk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>pclk_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>50</td>
<td>pclk_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_pwm/duty_cycle_int_2_s1/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_pwm/duty_cycle_int_2_s1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 2.144, 45.175%; route: 2.370, 49.937%; tC2Q: 0.232, 4.888%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp3</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>5.219</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.106</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_pwm/duty_cycle_int_1_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_pwm/duty_cycle_int_3_s1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>pclk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>pclk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>pclk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>pclk_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>50</td>
<td>pclk_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_pwm/duty_cycle_int_1_s1/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>7</td>
<td>u_pwm/duty_cycle_int_1_s1/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n59_s4/I1</td>
</tr>
<tr>
<td>1.621</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n59_s4/F</td>
</tr>
<tr>
<td>2.095</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n59_s2/I1</td>
</tr>
<tr>
<td>2.650</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n59_s2/F</td>
</tr>
<tr>
<td>3.124</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n59_s1/I0</td>
</tr>
<tr>
<td>3.641</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n59_s1/F</td>
</tr>
<tr>
<td>4.115</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n59_s5/I0</td>
</tr>
<tr>
<td>4.632</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n59_s5/F</td>
</tr>
<tr>
<td>5.106</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/duty_cycle_int_3_s1/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>pclk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>pclk_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>50</td>
<td>pclk_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_pwm/duty_cycle_int_3_s1/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_pwm/duty_cycle_int_3_s1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 2.144, 45.175%; route: 2.370, 49.937%; tC2Q: 0.232, 4.888%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp4</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>5.219</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.106</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_pwm/duty_cycle_int_1_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_pwm/duty_cycle_int_5_s1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>pclk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>pclk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>pclk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>pclk_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>50</td>
<td>pclk_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_pwm/duty_cycle_int_1_s1/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>7</td>
<td>u_pwm/duty_cycle_int_1_s1/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n55_s8/I1</td>
</tr>
<tr>
<td>1.621</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>u_pwm/n55_s8/F</td>
</tr>
<tr>
<td>2.095</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n57_s3/I1</td>
</tr>
<tr>
<td>2.650</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n57_s3/F</td>
</tr>
<tr>
<td>3.124</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n57_s1/I0</td>
</tr>
<tr>
<td>3.641</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n57_s1/F</td>
</tr>
<tr>
<td>4.115</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n57_s0/I0</td>
</tr>
<tr>
<td>4.632</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n57_s0/F</td>
</tr>
<tr>
<td>5.106</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/duty_cycle_int_5_s1/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>pclk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>pclk_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>50</td>
<td>pclk_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_pwm/duty_cycle_int_5_s1/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_pwm/duty_cycle_int_5_s1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 2.144, 45.175%; route: 2.370, 49.937%; tC2Q: 0.232, 4.888%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp5</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>5.245</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>5.080</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_pwm/duty_cycle_int_1_s1</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_pwm/duty_cycle_int_6_s1</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>pclk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>pclk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>pclk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>pclk_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>50</td>
<td>pclk_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_pwm/duty_cycle_int_1_s1/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>7</td>
<td>u_pwm/duty_cycle_int_1_s1/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n55_s8/I1</td>
</tr>
<tr>
<td>1.621</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>4</td>
<td>u_pwm/n55_s8/F</td>
</tr>
<tr>
<td>2.095</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n56_s5/I2</td>
</tr>
<tr>
<td>2.548</td>
<td>0.453</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n56_s5/F</td>
</tr>
<tr>
<td>3.022</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n56_s2/I1</td>
</tr>
<tr>
<td>3.577</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n56_s2/F</td>
</tr>
<tr>
<td>4.051</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n56_s0/I1</td>
</tr>
<tr>
<td>4.606</td>
<td>0.555</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/n56_s0/F</td>
</tr>
<tr>
<td>5.080</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_pwm/duty_cycle_int_6_s1/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>pclk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>pclk_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>50</td>
<td>pclk_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_pwm/duty_cycle_int_6_s1/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_pwm/duty_cycle_int_6_s1</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 2.118, 44.873%; route: 2.370, 50.212%; tC2Q: 0.232, 4.915%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
</div><!-- content -->
</div><!-- main_wrapper -->
</body>
</html>
