<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE gowin-synthesis-project>
<Project>
    <Version>beta</Version>
    <Device id="GW2A-18C" package="PBGA256" speed="8" partNumber="GW2A-LV18PG256C8/I7"/>
    <FileList>
        <File path="G:/Gowin/Gowin_V1.9.11.03_Education_x64_win/Gowin/Gowin_V1.9.11.03_Education_x64/IDE/ipcore/CORDIC/data/cordic.v" type="verilog"/>
        <File path="G:/Gowin/Gowin_V1.9.11.03_Education_x64_win/Gowin/Gowin_V1.9.11.03_Education_x64/IDE/ipcore/CORDIC/data/cordic_wrap.v" type="verilog"/>
    </FileList>
    <OptionList>
        <Option type="disable_insert_pad" value="1"/>
        <Option type="include_path" value="G:/Gowin/Gowin_V1.9.11.03_Education_x64_win/Gowin/Gowin_V1.9.11.03_Education_x64/IDE/ipcore/CORDIC/data"/>
        <Option type="include_path" value="G:/Gowin/workspace/fpga_project/src/cordic/temp/Cordic"/>
        <Option type="output_file" value="cordic.vg"/>
        <Option type="output_template" value="cordic_tmp.v"/>
        <Option type="ram_balance" value="1"/>
        <Option type="ram_rw_check" value="1"/>
        <Option type="vcc" value="1.0"/>
        <Option type="vccx" value="3.3"/>
        <Option type="verilog_language" value="sysv-2017"/>
    </OptionList>
</Project>
