//Copyright (C)2014-2025 Gowin Semiconductor Corporation.
//All rights reserved.
//File Title: Post-PnR Verilog Simulation Model file
//Tool Version: V1.9.11.03 Education
//Created Time: Wed Jul 30 11:11:27 2025

`timescale 100 ps/100 ps
module CORDIC_Top(
	clk,
	rst,
	x_i,
	y_i,
	theta_i,
	x_o,
	y_o,
	theta_o
);
input clk;
input rst;
input [16:0] x_i;
input [16:0] y_i;
input [16:0] theta_i;
output [16:0] x_o;
output [16:0] y_o;
output [16:0] theta_o;
wire GND;
wire VCC;
wire clk;
wire rst;
wire [16:0] theta_i;
wire \theta_i_d[16]_7 ;
wire [16:0] theta_o;
wire [16:0] x_i;
wire [16:0] x_o;
wire [16:0] y_i;
wire [16:0] y_o;
wire \u_cordic/z[1][16]_1_5 ;
wire \u_cordic/z[2][16]_1_5 ;
wire \u_cordic/z[3][16]_1_5 ;
wire \u_cordic/z[4][16]_1_5 ;
wire \u_cordic/z[5][16]_1_5 ;
wire \u_cordic/z[6][16]_1_5 ;
wire \u_cordic/z[7][16]_1_5 ;
wire \u_cordic/z[8][16]_1_5 ;
wire \u_cordic/z[9][16]_1_5 ;
wire \u_cordic/z[10][16]_1_5 ;
wire \u_cordic/z[11][16]_1_5 ;
wire \u_cordic/z[12][16]_1_5 ;
wire \u_cordic/z[13][16]_1_5 ;
wire \u_cordic/z[14][16]_1_5 ;
wire \u_cordic/z[15][16]_1_5 ;
wire [16:0] \u_cordic/x[1] ;
wire [16:0] \u_cordic/y[1] ;
wire [16:0] \u_cordic/z[1] ;
wire [16:0] \u_cordic/x[2] ;
wire [16:0] \u_cordic/y[2] ;
wire [16:0] \u_cordic/z[2] ;
wire [16:0] \u_cordic/x[3] ;
wire [16:0] \u_cordic/y[3] ;
wire [16:0] \u_cordic/z[3] ;
wire [16:0] \u_cordic/x[4] ;
wire [16:0] \u_cordic/y[4] ;
wire [16:0] \u_cordic/z[4] ;
wire [16:0] \u_cordic/x[5] ;
wire [16:0] \u_cordic/y[5] ;
wire [16:0] \u_cordic/z[5] ;
wire [16:0] \u_cordic/x[6] ;
wire [16:0] \u_cordic/y[6] ;
wire [16:0] \u_cordic/z[6] ;
wire [16:0] \u_cordic/x[7] ;
wire [16:0] \u_cordic/y[7] ;
wire [16:0] \u_cordic/z[7] ;
wire [16:0] \u_cordic/x[8] ;
wire [16:0] \u_cordic/y[8] ;
wire [16:0] \u_cordic/z[8] ;
wire [16:0] \u_cordic/x[9] ;
wire [16:0] \u_cordic/y[9] ;
wire [16:0] \u_cordic/z[9] ;
wire [16:0] \u_cordic/x[10] ;
wire [16:0] \u_cordic/y[10] ;
wire [16:0] \u_cordic/z[10] ;
wire [16:0] \u_cordic/x[11] ;
wire [16:0] \u_cordic/y[11] ;
wire [16:0] \u_cordic/z[11] ;
wire [16:0] \u_cordic/x[12] ;
wire [16:0] \u_cordic/y[12] ;
wire [16:0] \u_cordic/z[12] ;
wire [16:0] \u_cordic/x[13] ;
wire [16:0] \u_cordic/y[13] ;
wire [16:0] \u_cordic/z[13] ;
wire [16:0] \u_cordic/x[14] ;
wire [16:0] \u_cordic/y[14] ;
wire [16:0] \u_cordic/z[14] ;
wire [16:0] \u_cordic/x[15] ;
wire [16:0] \u_cordic/y[15] ;
wire [16:0] \u_cordic/z[15] ;
wire \u_cordic/[0].U/n192_2 ;
wire \u_cordic/[0].U/n192_1_1 ;
wire \u_cordic/[0].U/n191_2 ;
wire \u_cordic/[0].U/n191_1_1 ;
wire \u_cordic/[0].U/n190_2 ;
wire \u_cordic/[0].U/n190_1_1 ;
wire \u_cordic/[0].U/n189_2 ;
wire \u_cordic/[0].U/n189_1_1 ;
wire \u_cordic/[0].U/n188_2 ;
wire \u_cordic/[0].U/n188_1_1 ;
wire \u_cordic/[0].U/n187_2 ;
wire \u_cordic/[0].U/n187_1_1 ;
wire \u_cordic/[0].U/n186_2 ;
wire \u_cordic/[0].U/n186_1_1 ;
wire \u_cordic/[0].U/n185_2 ;
wire \u_cordic/[0].U/n185_1_1 ;
wire \u_cordic/[0].U/n184_2 ;
wire \u_cordic/[0].U/n184_1_1 ;
wire \u_cordic/[0].U/n183_2 ;
wire \u_cordic/[0].U/n183_1_1 ;
wire \u_cordic/[0].U/n182_2 ;
wire \u_cordic/[0].U/n182_1_1 ;
wire \u_cordic/[0].U/n181_2 ;
wire \u_cordic/[0].U/n181_1_1 ;
wire \u_cordic/[0].U/n180_2 ;
wire \u_cordic/[0].U/n180_1_1 ;
wire \u_cordic/[0].U/n179_2 ;
wire \u_cordic/[0].U/n179_1_1 ;
wire \u_cordic/[0].U/n178_2 ;
wire \u_cordic/[0].U/n178_1_1 ;
wire \u_cordic/[0].U/n177_2 ;
wire \u_cordic/[0].U/n177_1_1 ;
wire \u_cordic/[0].U/n176_2 ;
wire \u_cordic/[0].U/n176_1_0_COUT ;
wire \u_cordic/[0].U/n223_2 ;
wire \u_cordic/[0].U/n223_1_1 ;
wire \u_cordic/[0].U/n222_2 ;
wire \u_cordic/[0].U/n222_1_1 ;
wire \u_cordic/[0].U/n221_2 ;
wire \u_cordic/[0].U/n221_1_1 ;
wire \u_cordic/[0].U/n220_2 ;
wire \u_cordic/[0].U/n220_1_1 ;
wire \u_cordic/[0].U/n219_2 ;
wire \u_cordic/[0].U/n219_1_1 ;
wire \u_cordic/[0].U/n218_2 ;
wire \u_cordic/[0].U/n218_1_1 ;
wire \u_cordic/[0].U/n217_2 ;
wire \u_cordic/[0].U/n217_1_1 ;
wire \u_cordic/[0].U/n216_2 ;
wire \u_cordic/[0].U/n216_1_1 ;
wire \u_cordic/[0].U/n215_2 ;
wire \u_cordic/[0].U/n215_1_1 ;
wire \u_cordic/[0].U/n214_2 ;
wire \u_cordic/[0].U/n214_1_1 ;
wire \u_cordic/[0].U/n213_2 ;
wire \u_cordic/[0].U/n213_1_1 ;
wire \u_cordic/[0].U/n212_2 ;
wire \u_cordic/[0].U/n212_1_1 ;
wire \u_cordic/[0].U/n211_2 ;
wire \u_cordic/[0].U/n211_1_1 ;
wire \u_cordic/[0].U/n210_2 ;
wire \u_cordic/[0].U/n210_1_0_COUT ;
wire \u_cordic/[0].U/n209_2 ;
wire \u_cordic/[0].U/n209_1_1 ;
wire \u_cordic/[0].U/n208_2 ;
wire \u_cordic/[0].U/n208_1_1 ;
wire \u_cordic/[0].U/n207_2 ;
wire \u_cordic/[0].U/n207_1_1 ;
wire \u_cordic/[0].U/n206_2 ;
wire \u_cordic/[0].U/n206_1_1 ;
wire \u_cordic/[0].U/n205_2 ;
wire \u_cordic/[0].U/n205_1_1 ;
wire \u_cordic/[0].U/n204_2 ;
wire \u_cordic/[0].U/n204_1_1 ;
wire \u_cordic/[0].U/n203_2 ;
wire \u_cordic/[0].U/n203_1_1 ;
wire \u_cordic/[0].U/n202_2 ;
wire \u_cordic/[0].U/n202_1_1 ;
wire \u_cordic/[0].U/n201_2 ;
wire \u_cordic/[0].U/n201_1_1 ;
wire \u_cordic/[0].U/n200_2 ;
wire \u_cordic/[0].U/n200_1_1 ;
wire \u_cordic/[0].U/n199_2 ;
wire \u_cordic/[0].U/n199_1_1 ;
wire \u_cordic/[0].U/n198_2 ;
wire \u_cordic/[0].U/n198_1_1 ;
wire \u_cordic/[0].U/n197_2 ;
wire \u_cordic/[0].U/n197_1_1 ;
wire \u_cordic/[0].U/n196_2 ;
wire \u_cordic/[0].U/n196_1_1 ;
wire \u_cordic/[0].U/n195_2 ;
wire \u_cordic/[0].U/n195_1_1 ;
wire \u_cordic/[0].U/n194_2 ;
wire \u_cordic/[0].U/n194_1_1 ;
wire \u_cordic/[0].U/n193_2 ;
wire \u_cordic/[0].U/n193_1_0_COUT ;
wire \u_cordic/[1].U/n198_2 ;
wire \u_cordic/[1].U/n198_1_1 ;
wire \u_cordic/[1].U/n197_2 ;
wire \u_cordic/[1].U/n197_1_1 ;
wire \u_cordic/[1].U/n196_2 ;
wire \u_cordic/[1].U/n196_1_1 ;
wire \u_cordic/[1].U/n195_2 ;
wire \u_cordic/[1].U/n195_1_1 ;
wire \u_cordic/[1].U/n194_2 ;
wire \u_cordic/[1].U/n194_1_1 ;
wire \u_cordic/[1].U/n193_2 ;
wire \u_cordic/[1].U/n193_1_1 ;
wire \u_cordic/[1].U/n192_2 ;
wire \u_cordic/[1].U/n192_1_1 ;
wire \u_cordic/[1].U/n191_2 ;
wire \u_cordic/[1].U/n191_1_1 ;
wire \u_cordic/[1].U/n190_2 ;
wire \u_cordic/[1].U/n190_1_1 ;
wire \u_cordic/[1].U/n189_2 ;
wire \u_cordic/[1].U/n189_1_1 ;
wire \u_cordic/[1].U/n188_2 ;
wire \u_cordic/[1].U/n188_1_1 ;
wire \u_cordic/[1].U/n187_2 ;
wire \u_cordic/[1].U/n187_1_1 ;
wire \u_cordic/[1].U/n186_2 ;
wire \u_cordic/[1].U/n186_1_1 ;
wire \u_cordic/[1].U/n185_2 ;
wire \u_cordic/[1].U/n185_1_1 ;
wire \u_cordic/[1].U/n184_2 ;
wire \u_cordic/[1].U/n184_1_1 ;
wire \u_cordic/[1].U/n183_2 ;
wire \u_cordic/[1].U/n183_1_1 ;
wire \u_cordic/[1].U/n182_2 ;
wire \u_cordic/[1].U/n182_1_0_COUT ;
wire \u_cordic/[1].U/n232_2 ;
wire \u_cordic/[1].U/n232_1_1 ;
wire \u_cordic/[1].U/n231_2 ;
wire \u_cordic/[1].U/n231_1_1 ;
wire \u_cordic/[1].U/n230_2 ;
wire \u_cordic/[1].U/n230_1_1 ;
wire \u_cordic/[1].U/n229_2 ;
wire \u_cordic/[1].U/n229_1_1 ;
wire \u_cordic/[1].U/n228_2 ;
wire \u_cordic/[1].U/n228_1_1 ;
wire \u_cordic/[1].U/n227_2 ;
wire \u_cordic/[1].U/n227_1_1 ;
wire \u_cordic/[1].U/n226_2 ;
wire \u_cordic/[1].U/n226_1_1 ;
wire \u_cordic/[1].U/n225_2 ;
wire \u_cordic/[1].U/n225_1_1 ;
wire \u_cordic/[1].U/n224_2 ;
wire \u_cordic/[1].U/n224_1_1 ;
wire \u_cordic/[1].U/n223_2 ;
wire \u_cordic/[1].U/n223_1_1 ;
wire \u_cordic/[1].U/n222_2 ;
wire \u_cordic/[1].U/n222_1_1 ;
wire \u_cordic/[1].U/n221_2 ;
wire \u_cordic/[1].U/n221_1_1 ;
wire \u_cordic/[1].U/n220_2 ;
wire \u_cordic/[1].U/n220_1_1 ;
wire \u_cordic/[1].U/n219_2 ;
wire \u_cordic/[1].U/n219_1_1 ;
wire \u_cordic/[1].U/n218_2 ;
wire \u_cordic/[1].U/n218_1_1 ;
wire \u_cordic/[1].U/n217_2 ;
wire \u_cordic/[1].U/n217_1_1 ;
wire \u_cordic/[1].U/n216_2 ;
wire \u_cordic/[1].U/n216_1_0_COUT ;
wire \u_cordic/[1].U/n215_2 ;
wire \u_cordic/[1].U/n215_1_1 ;
wire \u_cordic/[1].U/n214_2 ;
wire \u_cordic/[1].U/n214_1_1 ;
wire \u_cordic/[1].U/n213_2 ;
wire \u_cordic/[1].U/n213_1_1 ;
wire \u_cordic/[1].U/n212_2 ;
wire \u_cordic/[1].U/n212_1_1 ;
wire \u_cordic/[1].U/n211_2 ;
wire \u_cordic/[1].U/n211_1_1 ;
wire \u_cordic/[1].U/n210_2 ;
wire \u_cordic/[1].U/n210_1_1 ;
wire \u_cordic/[1].U/n209_2 ;
wire \u_cordic/[1].U/n209_1_1 ;
wire \u_cordic/[1].U/n208_2 ;
wire \u_cordic/[1].U/n208_1_1 ;
wire \u_cordic/[1].U/n207_2 ;
wire \u_cordic/[1].U/n207_1_1 ;
wire \u_cordic/[1].U/n206_2 ;
wire \u_cordic/[1].U/n206_1_1 ;
wire \u_cordic/[1].U/n205_2 ;
wire \u_cordic/[1].U/n205_1_1 ;
wire \u_cordic/[1].U/n204_2 ;
wire \u_cordic/[1].U/n204_1_1 ;
wire \u_cordic/[1].U/n203_2 ;
wire \u_cordic/[1].U/n203_1_1 ;
wire \u_cordic/[1].U/n202_2 ;
wire \u_cordic/[1].U/n202_1_1 ;
wire \u_cordic/[1].U/n201_2 ;
wire \u_cordic/[1].U/n201_1_1 ;
wire \u_cordic/[1].U/n200_2 ;
wire \u_cordic/[1].U/n200_1_1 ;
wire \u_cordic/[1].U/n199_2 ;
wire \u_cordic/[1].U/n199_1_0_COUT ;
wire \u_cordic/[2].U/n198_2 ;
wire \u_cordic/[2].U/n198_1_1 ;
wire \u_cordic/[2].U/n197_2 ;
wire \u_cordic/[2].U/n197_1_1 ;
wire \u_cordic/[2].U/n196_2 ;
wire \u_cordic/[2].U/n196_1_1 ;
wire \u_cordic/[2].U/n195_2 ;
wire \u_cordic/[2].U/n195_1_1 ;
wire \u_cordic/[2].U/n194_2 ;
wire \u_cordic/[2].U/n194_1_1 ;
wire \u_cordic/[2].U/n193_2 ;
wire \u_cordic/[2].U/n193_1_1 ;
wire \u_cordic/[2].U/n192_2 ;
wire \u_cordic/[2].U/n192_1_1 ;
wire \u_cordic/[2].U/n191_2 ;
wire \u_cordic/[2].U/n191_1_1 ;
wire \u_cordic/[2].U/n190_2 ;
wire \u_cordic/[2].U/n190_1_1 ;
wire \u_cordic/[2].U/n189_2 ;
wire \u_cordic/[2].U/n189_1_1 ;
wire \u_cordic/[2].U/n188_2 ;
wire \u_cordic/[2].U/n188_1_1 ;
wire \u_cordic/[2].U/n187_2 ;
wire \u_cordic/[2].U/n187_1_1 ;
wire \u_cordic/[2].U/n186_2 ;
wire \u_cordic/[2].U/n186_1_1 ;
wire \u_cordic/[2].U/n185_2 ;
wire \u_cordic/[2].U/n185_1_1 ;
wire \u_cordic/[2].U/n184_2 ;
wire \u_cordic/[2].U/n184_1_1 ;
wire \u_cordic/[2].U/n183_2 ;
wire \u_cordic/[2].U/n183_1_1 ;
wire \u_cordic/[2].U/n182_2 ;
wire \u_cordic/[2].U/n182_1_0_COUT ;
wire \u_cordic/[2].U/n232_2 ;
wire \u_cordic/[2].U/n232_1_1 ;
wire \u_cordic/[2].U/n231_2 ;
wire \u_cordic/[2].U/n231_1_1 ;
wire \u_cordic/[2].U/n230_2 ;
wire \u_cordic/[2].U/n230_1_1 ;
wire \u_cordic/[2].U/n229_2 ;
wire \u_cordic/[2].U/n229_1_1 ;
wire \u_cordic/[2].U/n228_2 ;
wire \u_cordic/[2].U/n228_1_1 ;
wire \u_cordic/[2].U/n227_2 ;
wire \u_cordic/[2].U/n227_1_1 ;
wire \u_cordic/[2].U/n226_2 ;
wire \u_cordic/[2].U/n226_1_1 ;
wire \u_cordic/[2].U/n225_2 ;
wire \u_cordic/[2].U/n225_1_1 ;
wire \u_cordic/[2].U/n224_2 ;
wire \u_cordic/[2].U/n224_1_1 ;
wire \u_cordic/[2].U/n223_2 ;
wire \u_cordic/[2].U/n223_1_1 ;
wire \u_cordic/[2].U/n222_2 ;
wire \u_cordic/[2].U/n222_1_1 ;
wire \u_cordic/[2].U/n221_2 ;
wire \u_cordic/[2].U/n221_1_1 ;
wire \u_cordic/[2].U/n220_2 ;
wire \u_cordic/[2].U/n220_1_1 ;
wire \u_cordic/[2].U/n219_2 ;
wire \u_cordic/[2].U/n219_1_1 ;
wire \u_cordic/[2].U/n218_2 ;
wire \u_cordic/[2].U/n218_1_1 ;
wire \u_cordic/[2].U/n217_2 ;
wire \u_cordic/[2].U/n217_1_1 ;
wire \u_cordic/[2].U/n216_2 ;
wire \u_cordic/[2].U/n216_1_0_COUT ;
wire \u_cordic/[2].U/n215_2 ;
wire \u_cordic/[2].U/n215_1_1 ;
wire \u_cordic/[2].U/n214_2 ;
wire \u_cordic/[2].U/n214_1_1 ;
wire \u_cordic/[2].U/n213_2 ;
wire \u_cordic/[2].U/n213_1_1 ;
wire \u_cordic/[2].U/n212_2 ;
wire \u_cordic/[2].U/n212_1_1 ;
wire \u_cordic/[2].U/n211_2 ;
wire \u_cordic/[2].U/n211_1_1 ;
wire \u_cordic/[2].U/n210_2 ;
wire \u_cordic/[2].U/n210_1_1 ;
wire \u_cordic/[2].U/n209_2 ;
wire \u_cordic/[2].U/n209_1_1 ;
wire \u_cordic/[2].U/n208_2 ;
wire \u_cordic/[2].U/n208_1_1 ;
wire \u_cordic/[2].U/n207_2 ;
wire \u_cordic/[2].U/n207_1_1 ;
wire \u_cordic/[2].U/n206_2 ;
wire \u_cordic/[2].U/n206_1_1 ;
wire \u_cordic/[2].U/n205_2 ;
wire \u_cordic/[2].U/n205_1_1 ;
wire \u_cordic/[2].U/n204_2 ;
wire \u_cordic/[2].U/n204_1_1 ;
wire \u_cordic/[2].U/n203_2 ;
wire \u_cordic/[2].U/n203_1_1 ;
wire \u_cordic/[2].U/n202_2 ;
wire \u_cordic/[2].U/n202_1_1 ;
wire \u_cordic/[2].U/n201_2 ;
wire \u_cordic/[2].U/n201_1_1 ;
wire \u_cordic/[2].U/n200_2 ;
wire \u_cordic/[2].U/n200_1_1 ;
wire \u_cordic/[2].U/n199_2 ;
wire \u_cordic/[2].U/n199_1_0_COUT ;
wire \u_cordic/[3].U/n198_2 ;
wire \u_cordic/[3].U/n198_1_1 ;
wire \u_cordic/[3].U/n197_2 ;
wire \u_cordic/[3].U/n197_1_1 ;
wire \u_cordic/[3].U/n196_2 ;
wire \u_cordic/[3].U/n196_1_1 ;
wire \u_cordic/[3].U/n195_2 ;
wire \u_cordic/[3].U/n195_1_1 ;
wire \u_cordic/[3].U/n194_2 ;
wire \u_cordic/[3].U/n194_1_1 ;
wire \u_cordic/[3].U/n193_2 ;
wire \u_cordic/[3].U/n193_1_1 ;
wire \u_cordic/[3].U/n192_2 ;
wire \u_cordic/[3].U/n192_1_1 ;
wire \u_cordic/[3].U/n191_2 ;
wire \u_cordic/[3].U/n191_1_1 ;
wire \u_cordic/[3].U/n190_2 ;
wire \u_cordic/[3].U/n190_1_1 ;
wire \u_cordic/[3].U/n189_2 ;
wire \u_cordic/[3].U/n189_1_1 ;
wire \u_cordic/[3].U/n188_2 ;
wire \u_cordic/[3].U/n188_1_1 ;
wire \u_cordic/[3].U/n187_2 ;
wire \u_cordic/[3].U/n187_1_1 ;
wire \u_cordic/[3].U/n186_2 ;
wire \u_cordic/[3].U/n186_1_1 ;
wire \u_cordic/[3].U/n185_2 ;
wire \u_cordic/[3].U/n185_1_1 ;
wire \u_cordic/[3].U/n184_2 ;
wire \u_cordic/[3].U/n184_1_1 ;
wire \u_cordic/[3].U/n183_2 ;
wire \u_cordic/[3].U/n183_1_1 ;
wire \u_cordic/[3].U/n182_2 ;
wire \u_cordic/[3].U/n182_1_0_COUT ;
wire \u_cordic/[3].U/n232_2 ;
wire \u_cordic/[3].U/n232_1_1 ;
wire \u_cordic/[3].U/n231_2 ;
wire \u_cordic/[3].U/n231_1_1 ;
wire \u_cordic/[3].U/n230_2 ;
wire \u_cordic/[3].U/n230_1_1 ;
wire \u_cordic/[3].U/n229_2 ;
wire \u_cordic/[3].U/n229_1_1 ;
wire \u_cordic/[3].U/n228_2 ;
wire \u_cordic/[3].U/n228_1_1 ;
wire \u_cordic/[3].U/n227_2 ;
wire \u_cordic/[3].U/n227_1_1 ;
wire \u_cordic/[3].U/n226_2 ;
wire \u_cordic/[3].U/n226_1_1 ;
wire \u_cordic/[3].U/n225_2 ;
wire \u_cordic/[3].U/n225_1_1 ;
wire \u_cordic/[3].U/n224_2 ;
wire \u_cordic/[3].U/n224_1_1 ;
wire \u_cordic/[3].U/n223_2 ;
wire \u_cordic/[3].U/n223_1_1 ;
wire \u_cordic/[3].U/n222_2 ;
wire \u_cordic/[3].U/n222_1_1 ;
wire \u_cordic/[3].U/n221_2 ;
wire \u_cordic/[3].U/n221_1_1 ;
wire \u_cordic/[3].U/n220_2 ;
wire \u_cordic/[3].U/n220_1_1 ;
wire \u_cordic/[3].U/n219_2 ;
wire \u_cordic/[3].U/n219_1_1 ;
wire \u_cordic/[3].U/n218_2 ;
wire \u_cordic/[3].U/n218_1_1 ;
wire \u_cordic/[3].U/n217_2 ;
wire \u_cordic/[3].U/n217_1_1 ;
wire \u_cordic/[3].U/n216_2 ;
wire \u_cordic/[3].U/n216_1_0_COUT ;
wire \u_cordic/[3].U/n215_2 ;
wire \u_cordic/[3].U/n215_1_1 ;
wire \u_cordic/[3].U/n214_2 ;
wire \u_cordic/[3].U/n214_1_1 ;
wire \u_cordic/[3].U/n213_2 ;
wire \u_cordic/[3].U/n213_1_1 ;
wire \u_cordic/[3].U/n212_2 ;
wire \u_cordic/[3].U/n212_1_1 ;
wire \u_cordic/[3].U/n211_2 ;
wire \u_cordic/[3].U/n211_1_1 ;
wire \u_cordic/[3].U/n210_2 ;
wire \u_cordic/[3].U/n210_1_1 ;
wire \u_cordic/[3].U/n209_2 ;
wire \u_cordic/[3].U/n209_1_1 ;
wire \u_cordic/[3].U/n208_2 ;
wire \u_cordic/[3].U/n208_1_1 ;
wire \u_cordic/[3].U/n207_2 ;
wire \u_cordic/[3].U/n207_1_1 ;
wire \u_cordic/[3].U/n206_2 ;
wire \u_cordic/[3].U/n206_1_1 ;
wire \u_cordic/[3].U/n205_2 ;
wire \u_cordic/[3].U/n205_1_1 ;
wire \u_cordic/[3].U/n204_2 ;
wire \u_cordic/[3].U/n204_1_1 ;
wire \u_cordic/[3].U/n203_2 ;
wire \u_cordic/[3].U/n203_1_1 ;
wire \u_cordic/[3].U/n202_2 ;
wire \u_cordic/[3].U/n202_1_1 ;
wire \u_cordic/[3].U/n201_2 ;
wire \u_cordic/[3].U/n201_1_1 ;
wire \u_cordic/[3].U/n200_2 ;
wire \u_cordic/[3].U/n200_1_1 ;
wire \u_cordic/[3].U/n199_2 ;
wire \u_cordic/[3].U/n199_1_0_COUT ;
wire \u_cordic/[4].U/n198_2 ;
wire \u_cordic/[4].U/n198_1_1 ;
wire \u_cordic/[4].U/n197_2 ;
wire \u_cordic/[4].U/n197_1_1 ;
wire \u_cordic/[4].U/n196_2 ;
wire \u_cordic/[4].U/n196_1_1 ;
wire \u_cordic/[4].U/n195_2 ;
wire \u_cordic/[4].U/n195_1_1 ;
wire \u_cordic/[4].U/n194_2 ;
wire \u_cordic/[4].U/n194_1_1 ;
wire \u_cordic/[4].U/n193_2 ;
wire \u_cordic/[4].U/n193_1_1 ;
wire \u_cordic/[4].U/n192_2 ;
wire \u_cordic/[4].U/n192_1_1 ;
wire \u_cordic/[4].U/n191_2 ;
wire \u_cordic/[4].U/n191_1_1 ;
wire \u_cordic/[4].U/n190_2 ;
wire \u_cordic/[4].U/n190_1_1 ;
wire \u_cordic/[4].U/n189_2 ;
wire \u_cordic/[4].U/n189_1_1 ;
wire \u_cordic/[4].U/n188_2 ;
wire \u_cordic/[4].U/n188_1_1 ;
wire \u_cordic/[4].U/n187_2 ;
wire \u_cordic/[4].U/n187_1_1 ;
wire \u_cordic/[4].U/n186_2 ;
wire \u_cordic/[4].U/n186_1_1 ;
wire \u_cordic/[4].U/n185_2 ;
wire \u_cordic/[4].U/n185_1_1 ;
wire \u_cordic/[4].U/n184_2 ;
wire \u_cordic/[4].U/n184_1_1 ;
wire \u_cordic/[4].U/n183_2 ;
wire \u_cordic/[4].U/n183_1_1 ;
wire \u_cordic/[4].U/n182_2 ;
wire \u_cordic/[4].U/n182_1_0_COUT ;
wire \u_cordic/[4].U/n232_2 ;
wire \u_cordic/[4].U/n232_1_1 ;
wire \u_cordic/[4].U/n231_2 ;
wire \u_cordic/[4].U/n231_1_1 ;
wire \u_cordic/[4].U/n230_2 ;
wire \u_cordic/[4].U/n230_1_1 ;
wire \u_cordic/[4].U/n229_2 ;
wire \u_cordic/[4].U/n229_1_1 ;
wire \u_cordic/[4].U/n228_2 ;
wire \u_cordic/[4].U/n228_1_1 ;
wire \u_cordic/[4].U/n227_2 ;
wire \u_cordic/[4].U/n227_1_1 ;
wire \u_cordic/[4].U/n226_2 ;
wire \u_cordic/[4].U/n226_1_1 ;
wire \u_cordic/[4].U/n225_2 ;
wire \u_cordic/[4].U/n225_1_1 ;
wire \u_cordic/[4].U/n224_2 ;
wire \u_cordic/[4].U/n224_1_1 ;
wire \u_cordic/[4].U/n223_2 ;
wire \u_cordic/[4].U/n223_1_1 ;
wire \u_cordic/[4].U/n222_2 ;
wire \u_cordic/[4].U/n222_1_1 ;
wire \u_cordic/[4].U/n221_2 ;
wire \u_cordic/[4].U/n221_1_1 ;
wire \u_cordic/[4].U/n220_2 ;
wire \u_cordic/[4].U/n220_1_1 ;
wire \u_cordic/[4].U/n219_2 ;
wire \u_cordic/[4].U/n219_1_1 ;
wire \u_cordic/[4].U/n218_2 ;
wire \u_cordic/[4].U/n218_1_1 ;
wire \u_cordic/[4].U/n217_2 ;
wire \u_cordic/[4].U/n217_1_1 ;
wire \u_cordic/[4].U/n216_2 ;
wire \u_cordic/[4].U/n216_1_0_COUT ;
wire \u_cordic/[4].U/n215_2 ;
wire \u_cordic/[4].U/n215_1_1 ;
wire \u_cordic/[4].U/n214_2 ;
wire \u_cordic/[4].U/n214_1_1 ;
wire \u_cordic/[4].U/n213_2 ;
wire \u_cordic/[4].U/n213_1_1 ;
wire \u_cordic/[4].U/n212_2 ;
wire \u_cordic/[4].U/n212_1_1 ;
wire \u_cordic/[4].U/n211_2 ;
wire \u_cordic/[4].U/n211_1_1 ;
wire \u_cordic/[4].U/n210_2 ;
wire \u_cordic/[4].U/n210_1_1 ;
wire \u_cordic/[4].U/n209_2 ;
wire \u_cordic/[4].U/n209_1_1 ;
wire \u_cordic/[4].U/n208_2 ;
wire \u_cordic/[4].U/n208_1_1 ;
wire \u_cordic/[4].U/n207_2 ;
wire \u_cordic/[4].U/n207_1_1 ;
wire \u_cordic/[4].U/n206_2 ;
wire \u_cordic/[4].U/n206_1_1 ;
wire \u_cordic/[4].U/n205_2 ;
wire \u_cordic/[4].U/n205_1_1 ;
wire \u_cordic/[4].U/n204_2 ;
wire \u_cordic/[4].U/n204_1_1 ;
wire \u_cordic/[4].U/n203_2 ;
wire \u_cordic/[4].U/n203_1_1 ;
wire \u_cordic/[4].U/n202_2 ;
wire \u_cordic/[4].U/n202_1_1 ;
wire \u_cordic/[4].U/n201_2 ;
wire \u_cordic/[4].U/n201_1_1 ;
wire \u_cordic/[4].U/n200_2 ;
wire \u_cordic/[4].U/n200_1_1 ;
wire \u_cordic/[4].U/n199_2 ;
wire \u_cordic/[4].U/n199_1_0_COUT ;
wire \u_cordic/[5].U/n178_2 ;
wire \u_cordic/[5].U/n178_1_1 ;
wire \u_cordic/[5].U/n177_2 ;
wire \u_cordic/[5].U/n177_1_1 ;
wire \u_cordic/[5].U/n176_2 ;
wire \u_cordic/[5].U/n176_1_1 ;
wire \u_cordic/[5].U/n175_2 ;
wire \u_cordic/[5].U/n175_1_1 ;
wire \u_cordic/[5].U/n174_2 ;
wire \u_cordic/[5].U/n174_1_1 ;
wire \u_cordic/[5].U/n173_2 ;
wire \u_cordic/[5].U/n173_1_1 ;
wire \u_cordic/[5].U/n172_2 ;
wire \u_cordic/[5].U/n172_1_1 ;
wire \u_cordic/[5].U/n171_2 ;
wire \u_cordic/[5].U/n171_1_1 ;
wire \u_cordic/[5].U/n170_2 ;
wire \u_cordic/[5].U/n170_1_1 ;
wire \u_cordic/[5].U/n169_2 ;
wire \u_cordic/[5].U/n169_1_1 ;
wire \u_cordic/[5].U/n168_2 ;
wire \u_cordic/[5].U/n168_1_1 ;
wire \u_cordic/[5].U/n167_2 ;
wire \u_cordic/[5].U/n167_1_1 ;
wire \u_cordic/[5].U/n166_2 ;
wire \u_cordic/[5].U/n166_1_1 ;
wire \u_cordic/[5].U/n165_2 ;
wire \u_cordic/[5].U/n165_1_1 ;
wire \u_cordic/[5].U/n164_2 ;
wire \u_cordic/[5].U/n164_1_1 ;
wire \u_cordic/[5].U/n163_2 ;
wire \u_cordic/[5].U/n163_1_1 ;
wire \u_cordic/[5].U/n162_2 ;
wire \u_cordic/[5].U/n162_1_0_COUT ;
wire \u_cordic/[5].U/n202_2 ;
wire \u_cordic/[5].U/n202_1_1 ;
wire \u_cordic/[5].U/n201_2 ;
wire \u_cordic/[5].U/n201_1_1 ;
wire \u_cordic/[5].U/n200_2 ;
wire \u_cordic/[5].U/n200_1_1 ;
wire \u_cordic/[5].U/n199_2 ;
wire \u_cordic/[5].U/n199_1_1 ;
wire \u_cordic/[5].U/n198_2 ;
wire \u_cordic/[5].U/n198_1_1 ;
wire \u_cordic/[5].U/n197_2 ;
wire \u_cordic/[5].U/n197_1_1 ;
wire \u_cordic/[5].U/n196_2 ;
wire \u_cordic/[5].U/n196_1_0_COUT ;
wire \u_cordic/[5].U/n195_2 ;
wire \u_cordic/[5].U/n195_1_1 ;
wire \u_cordic/[5].U/n194_2 ;
wire \u_cordic/[5].U/n194_1_1 ;
wire \u_cordic/[5].U/n193_2 ;
wire \u_cordic/[5].U/n193_1_1 ;
wire \u_cordic/[5].U/n192_2 ;
wire \u_cordic/[5].U/n192_1_1 ;
wire \u_cordic/[5].U/n191_2 ;
wire \u_cordic/[5].U/n191_1_1 ;
wire \u_cordic/[5].U/n190_2 ;
wire \u_cordic/[5].U/n190_1_1 ;
wire \u_cordic/[5].U/n189_2 ;
wire \u_cordic/[5].U/n189_1_1 ;
wire \u_cordic/[5].U/n188_2 ;
wire \u_cordic/[5].U/n188_1_1 ;
wire \u_cordic/[5].U/n187_2 ;
wire \u_cordic/[5].U/n187_1_1 ;
wire \u_cordic/[5].U/n186_2 ;
wire \u_cordic/[5].U/n186_1_1 ;
wire \u_cordic/[5].U/n185_2 ;
wire \u_cordic/[5].U/n185_1_1 ;
wire \u_cordic/[5].U/n184_2 ;
wire \u_cordic/[5].U/n184_1_1 ;
wire \u_cordic/[5].U/n183_2 ;
wire \u_cordic/[5].U/n183_1_1 ;
wire \u_cordic/[5].U/n182_2 ;
wire \u_cordic/[5].U/n182_1_1 ;
wire \u_cordic/[5].U/n181_2 ;
wire \u_cordic/[5].U/n181_1_1 ;
wire \u_cordic/[5].U/n180_2 ;
wire \u_cordic/[5].U/n180_1_1 ;
wire \u_cordic/[5].U/n179_2 ;
wire \u_cordic/[5].U/n179_1_0_COUT ;
wire \u_cordic/[6].U/n180_2 ;
wire \u_cordic/[6].U/n180_1_1 ;
wire \u_cordic/[6].U/n179_2 ;
wire \u_cordic/[6].U/n179_1_1 ;
wire \u_cordic/[6].U/n178_2 ;
wire \u_cordic/[6].U/n178_1_1 ;
wire \u_cordic/[6].U/n177_2 ;
wire \u_cordic/[6].U/n177_1_1 ;
wire \u_cordic/[6].U/n176_2 ;
wire \u_cordic/[6].U/n176_1_1 ;
wire \u_cordic/[6].U/n175_2 ;
wire \u_cordic/[6].U/n175_1_1 ;
wire \u_cordic/[6].U/n174_2 ;
wire \u_cordic/[6].U/n174_1_1 ;
wire \u_cordic/[6].U/n173_2 ;
wire \u_cordic/[6].U/n173_1_1 ;
wire \u_cordic/[6].U/n172_2 ;
wire \u_cordic/[6].U/n172_1_1 ;
wire \u_cordic/[6].U/n171_2 ;
wire \u_cordic/[6].U/n171_1_1 ;
wire \u_cordic/[6].U/n170_2 ;
wire \u_cordic/[6].U/n170_1_1 ;
wire \u_cordic/[6].U/n169_2 ;
wire \u_cordic/[6].U/n169_1_1 ;
wire \u_cordic/[6].U/n168_2 ;
wire \u_cordic/[6].U/n168_1_1 ;
wire \u_cordic/[6].U/n167_2 ;
wire \u_cordic/[6].U/n167_1_1 ;
wire \u_cordic/[6].U/n166_2 ;
wire \u_cordic/[6].U/n166_1_1 ;
wire \u_cordic/[6].U/n165_2 ;
wire \u_cordic/[6].U/n165_1_1 ;
wire \u_cordic/[6].U/n164_2 ;
wire \u_cordic/[6].U/n164_1_0_COUT ;
wire \u_cordic/[6].U/n205_2 ;
wire \u_cordic/[6].U/n205_1_1 ;
wire \u_cordic/[6].U/n204_2 ;
wire \u_cordic/[6].U/n204_1_1 ;
wire \u_cordic/[6].U/n203_2 ;
wire \u_cordic/[6].U/n203_1_1 ;
wire \u_cordic/[6].U/n202_2 ;
wire \u_cordic/[6].U/n202_1_1 ;
wire \u_cordic/[6].U/n201_2 ;
wire \u_cordic/[6].U/n201_1_1 ;
wire \u_cordic/[6].U/n200_2 ;
wire \u_cordic/[6].U/n200_1_1 ;
wire \u_cordic/[6].U/n199_2 ;
wire \u_cordic/[6].U/n199_1_1 ;
wire \u_cordic/[6].U/n198_2 ;
wire \u_cordic/[6].U/n198_1_0_COUT ;
wire \u_cordic/[6].U/n197_2 ;
wire \u_cordic/[6].U/n197_1_1 ;
wire \u_cordic/[6].U/n196_2 ;
wire \u_cordic/[6].U/n196_1_1 ;
wire \u_cordic/[6].U/n195_2 ;
wire \u_cordic/[6].U/n195_1_1 ;
wire \u_cordic/[6].U/n194_2 ;
wire \u_cordic/[6].U/n194_1_1 ;
wire \u_cordic/[6].U/n193_2 ;
wire \u_cordic/[6].U/n193_1_1 ;
wire \u_cordic/[6].U/n192_2 ;
wire \u_cordic/[6].U/n192_1_1 ;
wire \u_cordic/[6].U/n191_2 ;
wire \u_cordic/[6].U/n191_1_1 ;
wire \u_cordic/[6].U/n190_2 ;
wire \u_cordic/[6].U/n190_1_1 ;
wire \u_cordic/[6].U/n189_2 ;
wire \u_cordic/[6].U/n189_1_1 ;
wire \u_cordic/[6].U/n188_2 ;
wire \u_cordic/[6].U/n188_1_1 ;
wire \u_cordic/[6].U/n187_2 ;
wire \u_cordic/[6].U/n187_1_1 ;
wire \u_cordic/[6].U/n186_2 ;
wire \u_cordic/[6].U/n186_1_1 ;
wire \u_cordic/[6].U/n185_2 ;
wire \u_cordic/[6].U/n185_1_1 ;
wire \u_cordic/[6].U/n184_2 ;
wire \u_cordic/[6].U/n184_1_1 ;
wire \u_cordic/[6].U/n183_2 ;
wire \u_cordic/[6].U/n183_1_1 ;
wire \u_cordic/[6].U/n182_2 ;
wire \u_cordic/[6].U/n182_1_1 ;
wire \u_cordic/[6].U/n181_2 ;
wire \u_cordic/[6].U/n181_1_0_COUT ;
wire \u_cordic/[7].U/n182_2 ;
wire \u_cordic/[7].U/n182_1_1 ;
wire \u_cordic/[7].U/n181_2 ;
wire \u_cordic/[7].U/n181_1_1 ;
wire \u_cordic/[7].U/n180_2 ;
wire \u_cordic/[7].U/n180_1_1 ;
wire \u_cordic/[7].U/n179_2 ;
wire \u_cordic/[7].U/n179_1_1 ;
wire \u_cordic/[7].U/n178_2 ;
wire \u_cordic/[7].U/n178_1_1 ;
wire \u_cordic/[7].U/n177_2 ;
wire \u_cordic/[7].U/n177_1_1 ;
wire \u_cordic/[7].U/n176_2 ;
wire \u_cordic/[7].U/n176_1_1 ;
wire \u_cordic/[7].U/n175_2 ;
wire \u_cordic/[7].U/n175_1_1 ;
wire \u_cordic/[7].U/n174_2 ;
wire \u_cordic/[7].U/n174_1_1 ;
wire \u_cordic/[7].U/n173_2 ;
wire \u_cordic/[7].U/n173_1_1 ;
wire \u_cordic/[7].U/n172_2 ;
wire \u_cordic/[7].U/n172_1_1 ;
wire \u_cordic/[7].U/n171_2 ;
wire \u_cordic/[7].U/n171_1_1 ;
wire \u_cordic/[7].U/n170_2 ;
wire \u_cordic/[7].U/n170_1_1 ;
wire \u_cordic/[7].U/n169_2 ;
wire \u_cordic/[7].U/n169_1_1 ;
wire \u_cordic/[7].U/n168_2 ;
wire \u_cordic/[7].U/n168_1_1 ;
wire \u_cordic/[7].U/n167_2 ;
wire \u_cordic/[7].U/n167_1_1 ;
wire \u_cordic/[7].U/n166_2 ;
wire \u_cordic/[7].U/n166_1_0_COUT ;
wire \u_cordic/[7].U/n208_2 ;
wire \u_cordic/[7].U/n208_1_1 ;
wire \u_cordic/[7].U/n207_2 ;
wire \u_cordic/[7].U/n207_1_1 ;
wire \u_cordic/[7].U/n206_2 ;
wire \u_cordic/[7].U/n206_1_1 ;
wire \u_cordic/[7].U/n205_2 ;
wire \u_cordic/[7].U/n205_1_1 ;
wire \u_cordic/[7].U/n204_2 ;
wire \u_cordic/[7].U/n204_1_1 ;
wire \u_cordic/[7].U/n203_2 ;
wire \u_cordic/[7].U/n203_1_1 ;
wire \u_cordic/[7].U/n202_2 ;
wire \u_cordic/[7].U/n202_1_1 ;
wire \u_cordic/[7].U/n201_2 ;
wire \u_cordic/[7].U/n201_1_1 ;
wire \u_cordic/[7].U/n200_2 ;
wire \u_cordic/[7].U/n200_1_0_COUT ;
wire \u_cordic/[7].U/n199_2 ;
wire \u_cordic/[7].U/n199_1_1 ;
wire \u_cordic/[7].U/n198_2 ;
wire \u_cordic/[7].U/n198_1_1 ;
wire \u_cordic/[7].U/n197_2 ;
wire \u_cordic/[7].U/n197_1_1 ;
wire \u_cordic/[7].U/n196_2 ;
wire \u_cordic/[7].U/n196_1_1 ;
wire \u_cordic/[7].U/n195_2 ;
wire \u_cordic/[7].U/n195_1_1 ;
wire \u_cordic/[7].U/n194_2 ;
wire \u_cordic/[7].U/n194_1_1 ;
wire \u_cordic/[7].U/n193_2 ;
wire \u_cordic/[7].U/n193_1_1 ;
wire \u_cordic/[7].U/n192_2 ;
wire \u_cordic/[7].U/n192_1_1 ;
wire \u_cordic/[7].U/n191_2 ;
wire \u_cordic/[7].U/n191_1_1 ;
wire \u_cordic/[7].U/n190_2 ;
wire \u_cordic/[7].U/n190_1_1 ;
wire \u_cordic/[7].U/n189_2 ;
wire \u_cordic/[7].U/n189_1_1 ;
wire \u_cordic/[7].U/n188_2 ;
wire \u_cordic/[7].U/n188_1_1 ;
wire \u_cordic/[7].U/n187_2 ;
wire \u_cordic/[7].U/n187_1_1 ;
wire \u_cordic/[7].U/n186_2 ;
wire \u_cordic/[7].U/n186_1_1 ;
wire \u_cordic/[7].U/n185_2 ;
wire \u_cordic/[7].U/n185_1_1 ;
wire \u_cordic/[7].U/n184_2 ;
wire \u_cordic/[7].U/n184_1_1 ;
wire \u_cordic/[7].U/n183_2 ;
wire \u_cordic/[7].U/n183_1_0_COUT ;
wire \u_cordic/[8].U/n184_2 ;
wire \u_cordic/[8].U/n184_1_1 ;
wire \u_cordic/[8].U/n183_2 ;
wire \u_cordic/[8].U/n183_1_1 ;
wire \u_cordic/[8].U/n182_2 ;
wire \u_cordic/[8].U/n182_1_1 ;
wire \u_cordic/[8].U/n181_2 ;
wire \u_cordic/[8].U/n181_1_1 ;
wire \u_cordic/[8].U/n180_2 ;
wire \u_cordic/[8].U/n180_1_1 ;
wire \u_cordic/[8].U/n179_2 ;
wire \u_cordic/[8].U/n179_1_1 ;
wire \u_cordic/[8].U/n178_2 ;
wire \u_cordic/[8].U/n178_1_1 ;
wire \u_cordic/[8].U/n177_2 ;
wire \u_cordic/[8].U/n177_1_1 ;
wire \u_cordic/[8].U/n176_2 ;
wire \u_cordic/[8].U/n176_1_1 ;
wire \u_cordic/[8].U/n175_2 ;
wire \u_cordic/[8].U/n175_1_1 ;
wire \u_cordic/[8].U/n174_2 ;
wire \u_cordic/[8].U/n174_1_1 ;
wire \u_cordic/[8].U/n173_2 ;
wire \u_cordic/[8].U/n173_1_1 ;
wire \u_cordic/[8].U/n172_2 ;
wire \u_cordic/[8].U/n172_1_1 ;
wire \u_cordic/[8].U/n171_2 ;
wire \u_cordic/[8].U/n171_1_1 ;
wire \u_cordic/[8].U/n170_2 ;
wire \u_cordic/[8].U/n170_1_1 ;
wire \u_cordic/[8].U/n169_2 ;
wire \u_cordic/[8].U/n169_1_1 ;
wire \u_cordic/[8].U/n168_2 ;
wire \u_cordic/[8].U/n168_1_0_COUT ;
wire \u_cordic/[8].U/n211_2 ;
wire \u_cordic/[8].U/n211_1_1 ;
wire \u_cordic/[8].U/n210_2 ;
wire \u_cordic/[8].U/n210_1_1 ;
wire \u_cordic/[8].U/n209_2 ;
wire \u_cordic/[8].U/n209_1_1 ;
wire \u_cordic/[8].U/n208_2 ;
wire \u_cordic/[8].U/n208_1_1 ;
wire \u_cordic/[8].U/n207_2 ;
wire \u_cordic/[8].U/n207_1_1 ;
wire \u_cordic/[8].U/n206_2 ;
wire \u_cordic/[8].U/n206_1_1 ;
wire \u_cordic/[8].U/n205_2 ;
wire \u_cordic/[8].U/n205_1_1 ;
wire \u_cordic/[8].U/n204_2 ;
wire \u_cordic/[8].U/n204_1_1 ;
wire \u_cordic/[8].U/n203_2 ;
wire \u_cordic/[8].U/n203_1_1 ;
wire \u_cordic/[8].U/n202_2 ;
wire \u_cordic/[8].U/n202_1_0_COUT ;
wire \u_cordic/[8].U/n201_2 ;
wire \u_cordic/[8].U/n201_1_1 ;
wire \u_cordic/[8].U/n200_2 ;
wire \u_cordic/[8].U/n200_1_1 ;
wire \u_cordic/[8].U/n199_2 ;
wire \u_cordic/[8].U/n199_1_1 ;
wire \u_cordic/[8].U/n198_2 ;
wire \u_cordic/[8].U/n198_1_1 ;
wire \u_cordic/[8].U/n197_2 ;
wire \u_cordic/[8].U/n197_1_1 ;
wire \u_cordic/[8].U/n196_2 ;
wire \u_cordic/[8].U/n196_1_1 ;
wire \u_cordic/[8].U/n195_2 ;
wire \u_cordic/[8].U/n195_1_1 ;
wire \u_cordic/[8].U/n194_2 ;
wire \u_cordic/[8].U/n194_1_1 ;
wire \u_cordic/[8].U/n193_2 ;
wire \u_cordic/[8].U/n193_1_1 ;
wire \u_cordic/[8].U/n192_2 ;
wire \u_cordic/[8].U/n192_1_1 ;
wire \u_cordic/[8].U/n191_2 ;
wire \u_cordic/[8].U/n191_1_1 ;
wire \u_cordic/[8].U/n190_2 ;
wire \u_cordic/[8].U/n190_1_1 ;
wire \u_cordic/[8].U/n189_2 ;
wire \u_cordic/[8].U/n189_1_1 ;
wire \u_cordic/[8].U/n188_2 ;
wire \u_cordic/[8].U/n188_1_1 ;
wire \u_cordic/[8].U/n187_2 ;
wire \u_cordic/[8].U/n187_1_1 ;
wire \u_cordic/[8].U/n186_2 ;
wire \u_cordic/[8].U/n186_1_1 ;
wire \u_cordic/[8].U/n185_2 ;
wire \u_cordic/[8].U/n185_1_0_COUT ;
wire \u_cordic/[9].U/n186_2 ;
wire \u_cordic/[9].U/n186_1_1 ;
wire \u_cordic/[9].U/n185_2 ;
wire \u_cordic/[9].U/n185_1_1 ;
wire \u_cordic/[9].U/n184_2 ;
wire \u_cordic/[9].U/n184_1_1 ;
wire \u_cordic/[9].U/n183_2 ;
wire \u_cordic/[9].U/n183_1_1 ;
wire \u_cordic/[9].U/n182_2 ;
wire \u_cordic/[9].U/n182_1_1 ;
wire \u_cordic/[9].U/n181_2 ;
wire \u_cordic/[9].U/n181_1_1 ;
wire \u_cordic/[9].U/n180_2 ;
wire \u_cordic/[9].U/n180_1_1 ;
wire \u_cordic/[9].U/n179_2 ;
wire \u_cordic/[9].U/n179_1_1 ;
wire \u_cordic/[9].U/n178_2 ;
wire \u_cordic/[9].U/n178_1_1 ;
wire \u_cordic/[9].U/n177_2 ;
wire \u_cordic/[9].U/n177_1_1 ;
wire \u_cordic/[9].U/n176_2 ;
wire \u_cordic/[9].U/n176_1_1 ;
wire \u_cordic/[9].U/n175_2 ;
wire \u_cordic/[9].U/n175_1_1 ;
wire \u_cordic/[9].U/n174_2 ;
wire \u_cordic/[9].U/n174_1_1 ;
wire \u_cordic/[9].U/n173_2 ;
wire \u_cordic/[9].U/n173_1_1 ;
wire \u_cordic/[9].U/n172_2 ;
wire \u_cordic/[9].U/n172_1_1 ;
wire \u_cordic/[9].U/n171_2 ;
wire \u_cordic/[9].U/n171_1_1 ;
wire \u_cordic/[9].U/n170_2 ;
wire \u_cordic/[9].U/n170_1_0_COUT ;
wire \u_cordic/[9].U/n214_2 ;
wire \u_cordic/[9].U/n214_1_1 ;
wire \u_cordic/[9].U/n213_2 ;
wire \u_cordic/[9].U/n213_1_1 ;
wire \u_cordic/[9].U/n212_2 ;
wire \u_cordic/[9].U/n212_1_1 ;
wire \u_cordic/[9].U/n211_2 ;
wire \u_cordic/[9].U/n211_1_1 ;
wire \u_cordic/[9].U/n210_2 ;
wire \u_cordic/[9].U/n210_1_1 ;
wire \u_cordic/[9].U/n209_2 ;
wire \u_cordic/[9].U/n209_1_1 ;
wire \u_cordic/[9].U/n208_2 ;
wire \u_cordic/[9].U/n208_1_1 ;
wire \u_cordic/[9].U/n207_2 ;
wire \u_cordic/[9].U/n207_1_1 ;
wire \u_cordic/[9].U/n206_2 ;
wire \u_cordic/[9].U/n206_1_1 ;
wire \u_cordic/[9].U/n205_2 ;
wire \u_cordic/[9].U/n205_1_1 ;
wire \u_cordic/[9].U/n204_2 ;
wire \u_cordic/[9].U/n204_1_0_COUT ;
wire \u_cordic/[9].U/n203_2 ;
wire \u_cordic/[9].U/n203_1_1 ;
wire \u_cordic/[9].U/n202_2 ;
wire \u_cordic/[9].U/n202_1_1 ;
wire \u_cordic/[9].U/n201_2 ;
wire \u_cordic/[9].U/n201_1_1 ;
wire \u_cordic/[9].U/n200_2 ;
wire \u_cordic/[9].U/n200_1_1 ;
wire \u_cordic/[9].U/n199_2 ;
wire \u_cordic/[9].U/n199_1_1 ;
wire \u_cordic/[9].U/n198_2 ;
wire \u_cordic/[9].U/n198_1_1 ;
wire \u_cordic/[9].U/n197_2 ;
wire \u_cordic/[9].U/n197_1_1 ;
wire \u_cordic/[9].U/n196_2 ;
wire \u_cordic/[9].U/n196_1_1 ;
wire \u_cordic/[9].U/n195_2 ;
wire \u_cordic/[9].U/n195_1_1 ;
wire \u_cordic/[9].U/n194_2 ;
wire \u_cordic/[9].U/n194_1_1 ;
wire \u_cordic/[9].U/n193_2 ;
wire \u_cordic/[9].U/n193_1_1 ;
wire \u_cordic/[9].U/n192_2 ;
wire \u_cordic/[9].U/n192_1_1 ;
wire \u_cordic/[9].U/n191_2 ;
wire \u_cordic/[9].U/n191_1_1 ;
wire \u_cordic/[9].U/n190_2 ;
wire \u_cordic/[9].U/n190_1_1 ;
wire \u_cordic/[9].U/n189_2 ;
wire \u_cordic/[9].U/n189_1_1 ;
wire \u_cordic/[9].U/n188_2 ;
wire \u_cordic/[9].U/n188_1_1 ;
wire \u_cordic/[9].U/n187_2 ;
wire \u_cordic/[9].U/n187_1_0_COUT ;
wire \u_cordic/[10].U/n188_2 ;
wire \u_cordic/[10].U/n188_1_1 ;
wire \u_cordic/[10].U/n187_2 ;
wire \u_cordic/[10].U/n187_1_1 ;
wire \u_cordic/[10].U/n186_2 ;
wire \u_cordic/[10].U/n186_1_1 ;
wire \u_cordic/[10].U/n185_2 ;
wire \u_cordic/[10].U/n185_1_1 ;
wire \u_cordic/[10].U/n184_2 ;
wire \u_cordic/[10].U/n184_1_1 ;
wire \u_cordic/[10].U/n183_2 ;
wire \u_cordic/[10].U/n183_1_1 ;
wire \u_cordic/[10].U/n182_2 ;
wire \u_cordic/[10].U/n182_1_1 ;
wire \u_cordic/[10].U/n181_2 ;
wire \u_cordic/[10].U/n181_1_1 ;
wire \u_cordic/[10].U/n180_2 ;
wire \u_cordic/[10].U/n180_1_1 ;
wire \u_cordic/[10].U/n179_2 ;
wire \u_cordic/[10].U/n179_1_1 ;
wire \u_cordic/[10].U/n178_2 ;
wire \u_cordic/[10].U/n178_1_1 ;
wire \u_cordic/[10].U/n177_2 ;
wire \u_cordic/[10].U/n177_1_1 ;
wire \u_cordic/[10].U/n176_2 ;
wire \u_cordic/[10].U/n176_1_1 ;
wire \u_cordic/[10].U/n175_2 ;
wire \u_cordic/[10].U/n175_1_1 ;
wire \u_cordic/[10].U/n174_2 ;
wire \u_cordic/[10].U/n174_1_1 ;
wire \u_cordic/[10].U/n173_2 ;
wire \u_cordic/[10].U/n173_1_1 ;
wire \u_cordic/[10].U/n172_2 ;
wire \u_cordic/[10].U/n172_1_0_COUT ;
wire \u_cordic/[10].U/n217_2 ;
wire \u_cordic/[10].U/n217_1_1 ;
wire \u_cordic/[10].U/n216_2 ;
wire \u_cordic/[10].U/n216_1_1 ;
wire \u_cordic/[10].U/n215_2 ;
wire \u_cordic/[10].U/n215_1_1 ;
wire \u_cordic/[10].U/n214_2 ;
wire \u_cordic/[10].U/n214_1_1 ;
wire \u_cordic/[10].U/n213_2 ;
wire \u_cordic/[10].U/n213_1_1 ;
wire \u_cordic/[10].U/n212_2 ;
wire \u_cordic/[10].U/n212_1_1 ;
wire \u_cordic/[10].U/n211_2 ;
wire \u_cordic/[10].U/n211_1_1 ;
wire \u_cordic/[10].U/n210_2 ;
wire \u_cordic/[10].U/n210_1_1 ;
wire \u_cordic/[10].U/n209_2 ;
wire \u_cordic/[10].U/n209_1_1 ;
wire \u_cordic/[10].U/n208_2 ;
wire \u_cordic/[10].U/n208_1_1 ;
wire \u_cordic/[10].U/n207_2 ;
wire \u_cordic/[10].U/n207_1_1 ;
wire \u_cordic/[10].U/n206_2 ;
wire \u_cordic/[10].U/n206_1_0_COUT ;
wire \u_cordic/[10].U/n205_2 ;
wire \u_cordic/[10].U/n205_1_1 ;
wire \u_cordic/[10].U/n204_2 ;
wire \u_cordic/[10].U/n204_1_1 ;
wire \u_cordic/[10].U/n203_2 ;
wire \u_cordic/[10].U/n203_1_1 ;
wire \u_cordic/[10].U/n202_2 ;
wire \u_cordic/[10].U/n202_1_1 ;
wire \u_cordic/[10].U/n201_2 ;
wire \u_cordic/[10].U/n201_1_1 ;
wire \u_cordic/[10].U/n200_2 ;
wire \u_cordic/[10].U/n200_1_1 ;
wire \u_cordic/[10].U/n199_2 ;
wire \u_cordic/[10].U/n199_1_1 ;
wire \u_cordic/[10].U/n198_2 ;
wire \u_cordic/[10].U/n198_1_1 ;
wire \u_cordic/[10].U/n197_2 ;
wire \u_cordic/[10].U/n197_1_1 ;
wire \u_cordic/[10].U/n196_2 ;
wire \u_cordic/[10].U/n196_1_1 ;
wire \u_cordic/[10].U/n195_2 ;
wire \u_cordic/[10].U/n195_1_1 ;
wire \u_cordic/[10].U/n194_2 ;
wire \u_cordic/[10].U/n194_1_1 ;
wire \u_cordic/[10].U/n193_2 ;
wire \u_cordic/[10].U/n193_1_1 ;
wire \u_cordic/[10].U/n192_2 ;
wire \u_cordic/[10].U/n192_1_1 ;
wire \u_cordic/[10].U/n191_2 ;
wire \u_cordic/[10].U/n191_1_1 ;
wire \u_cordic/[10].U/n190_2 ;
wire \u_cordic/[10].U/n190_1_1 ;
wire \u_cordic/[10].U/n189_2 ;
wire \u_cordic/[10].U/n189_1_0_COUT ;
wire \u_cordic/[11].U/n190_2 ;
wire \u_cordic/[11].U/n190_1_1 ;
wire \u_cordic/[11].U/n189_2 ;
wire \u_cordic/[11].U/n189_1_1 ;
wire \u_cordic/[11].U/n188_2 ;
wire \u_cordic/[11].U/n188_1_1 ;
wire \u_cordic/[11].U/n187_2 ;
wire \u_cordic/[11].U/n187_1_1 ;
wire \u_cordic/[11].U/n186_2 ;
wire \u_cordic/[11].U/n186_1_1 ;
wire \u_cordic/[11].U/n185_2 ;
wire \u_cordic/[11].U/n185_1_1 ;
wire \u_cordic/[11].U/n184_2 ;
wire \u_cordic/[11].U/n184_1_1 ;
wire \u_cordic/[11].U/n183_2 ;
wire \u_cordic/[11].U/n183_1_1 ;
wire \u_cordic/[11].U/n182_2 ;
wire \u_cordic/[11].U/n182_1_1 ;
wire \u_cordic/[11].U/n181_2 ;
wire \u_cordic/[11].U/n181_1_1 ;
wire \u_cordic/[11].U/n180_2 ;
wire \u_cordic/[11].U/n180_1_1 ;
wire \u_cordic/[11].U/n179_2 ;
wire \u_cordic/[11].U/n179_1_1 ;
wire \u_cordic/[11].U/n178_2 ;
wire \u_cordic/[11].U/n178_1_1 ;
wire \u_cordic/[11].U/n177_2 ;
wire \u_cordic/[11].U/n177_1_1 ;
wire \u_cordic/[11].U/n176_2 ;
wire \u_cordic/[11].U/n176_1_1 ;
wire \u_cordic/[11].U/n175_2 ;
wire \u_cordic/[11].U/n175_1_1 ;
wire \u_cordic/[11].U/n174_2 ;
wire \u_cordic/[11].U/n174_1_0_COUT ;
wire \u_cordic/[11].U/n220_2 ;
wire \u_cordic/[11].U/n220_1_1 ;
wire \u_cordic/[11].U/n219_2 ;
wire \u_cordic/[11].U/n219_1_1 ;
wire \u_cordic/[11].U/n218_2 ;
wire \u_cordic/[11].U/n218_1_1 ;
wire \u_cordic/[11].U/n217_2 ;
wire \u_cordic/[11].U/n217_1_1 ;
wire \u_cordic/[11].U/n216_2 ;
wire \u_cordic/[11].U/n216_1_1 ;
wire \u_cordic/[11].U/n215_2 ;
wire \u_cordic/[11].U/n215_1_1 ;
wire \u_cordic/[11].U/n214_2 ;
wire \u_cordic/[11].U/n214_1_1 ;
wire \u_cordic/[11].U/n213_2 ;
wire \u_cordic/[11].U/n213_1_1 ;
wire \u_cordic/[11].U/n212_2 ;
wire \u_cordic/[11].U/n212_1_1 ;
wire \u_cordic/[11].U/n211_2 ;
wire \u_cordic/[11].U/n211_1_1 ;
wire \u_cordic/[11].U/n210_2 ;
wire \u_cordic/[11].U/n210_1_1 ;
wire \u_cordic/[11].U/n209_2 ;
wire \u_cordic/[11].U/n209_1_1 ;
wire \u_cordic/[11].U/n208_2 ;
wire \u_cordic/[11].U/n208_1_0_COUT ;
wire \u_cordic/[11].U/n207_2 ;
wire \u_cordic/[11].U/n207_1_1 ;
wire \u_cordic/[11].U/n206_2 ;
wire \u_cordic/[11].U/n206_1_1 ;
wire \u_cordic/[11].U/n205_2 ;
wire \u_cordic/[11].U/n205_1_1 ;
wire \u_cordic/[11].U/n204_2 ;
wire \u_cordic/[11].U/n204_1_1 ;
wire \u_cordic/[11].U/n203_2 ;
wire \u_cordic/[11].U/n203_1_1 ;
wire \u_cordic/[11].U/n202_2 ;
wire \u_cordic/[11].U/n202_1_1 ;
wire \u_cordic/[11].U/n201_2 ;
wire \u_cordic/[11].U/n201_1_1 ;
wire \u_cordic/[11].U/n200_2 ;
wire \u_cordic/[11].U/n200_1_1 ;
wire \u_cordic/[11].U/n199_2 ;
wire \u_cordic/[11].U/n199_1_1 ;
wire \u_cordic/[11].U/n198_2 ;
wire \u_cordic/[11].U/n198_1_1 ;
wire \u_cordic/[11].U/n197_2 ;
wire \u_cordic/[11].U/n197_1_1 ;
wire \u_cordic/[11].U/n196_2 ;
wire \u_cordic/[11].U/n196_1_1 ;
wire \u_cordic/[11].U/n195_2 ;
wire \u_cordic/[11].U/n195_1_1 ;
wire \u_cordic/[11].U/n194_2 ;
wire \u_cordic/[11].U/n194_1_1 ;
wire \u_cordic/[11].U/n193_2 ;
wire \u_cordic/[11].U/n193_1_1 ;
wire \u_cordic/[11].U/n192_2 ;
wire \u_cordic/[11].U/n192_1_1 ;
wire \u_cordic/[11].U/n191_2 ;
wire \u_cordic/[11].U/n191_1_0_COUT ;
wire \u_cordic/[12].U/n192_2 ;
wire \u_cordic/[12].U/n192_1_1 ;
wire \u_cordic/[12].U/n191_2 ;
wire \u_cordic/[12].U/n191_1_1 ;
wire \u_cordic/[12].U/n190_2 ;
wire \u_cordic/[12].U/n190_1_1 ;
wire \u_cordic/[12].U/n189_2 ;
wire \u_cordic/[12].U/n189_1_1 ;
wire \u_cordic/[12].U/n188_2 ;
wire \u_cordic/[12].U/n188_1_1 ;
wire \u_cordic/[12].U/n187_2 ;
wire \u_cordic/[12].U/n187_1_1 ;
wire \u_cordic/[12].U/n186_2 ;
wire \u_cordic/[12].U/n186_1_1 ;
wire \u_cordic/[12].U/n185_2 ;
wire \u_cordic/[12].U/n185_1_1 ;
wire \u_cordic/[12].U/n184_2 ;
wire \u_cordic/[12].U/n184_1_1 ;
wire \u_cordic/[12].U/n183_2 ;
wire \u_cordic/[12].U/n183_1_1 ;
wire \u_cordic/[12].U/n182_2 ;
wire \u_cordic/[12].U/n182_1_1 ;
wire \u_cordic/[12].U/n181_2 ;
wire \u_cordic/[12].U/n181_1_1 ;
wire \u_cordic/[12].U/n180_2 ;
wire \u_cordic/[12].U/n180_1_1 ;
wire \u_cordic/[12].U/n179_2 ;
wire \u_cordic/[12].U/n179_1_1 ;
wire \u_cordic/[12].U/n178_2 ;
wire \u_cordic/[12].U/n178_1_1 ;
wire \u_cordic/[12].U/n177_2 ;
wire \u_cordic/[12].U/n177_1_1 ;
wire \u_cordic/[12].U/n176_2 ;
wire \u_cordic/[12].U/n176_1_0_COUT ;
wire \u_cordic/[12].U/n223_2 ;
wire \u_cordic/[12].U/n223_1_1 ;
wire \u_cordic/[12].U/n222_2 ;
wire \u_cordic/[12].U/n222_1_1 ;
wire \u_cordic/[12].U/n221_2 ;
wire \u_cordic/[12].U/n221_1_1 ;
wire \u_cordic/[12].U/n220_2 ;
wire \u_cordic/[12].U/n220_1_1 ;
wire \u_cordic/[12].U/n219_2 ;
wire \u_cordic/[12].U/n219_1_1 ;
wire \u_cordic/[12].U/n218_2 ;
wire \u_cordic/[12].U/n218_1_1 ;
wire \u_cordic/[12].U/n217_2 ;
wire \u_cordic/[12].U/n217_1_1 ;
wire \u_cordic/[12].U/n216_2 ;
wire \u_cordic/[12].U/n216_1_1 ;
wire \u_cordic/[12].U/n215_2 ;
wire \u_cordic/[12].U/n215_1_1 ;
wire \u_cordic/[12].U/n214_2 ;
wire \u_cordic/[12].U/n214_1_1 ;
wire \u_cordic/[12].U/n213_2 ;
wire \u_cordic/[12].U/n213_1_1 ;
wire \u_cordic/[12].U/n212_2 ;
wire \u_cordic/[12].U/n212_1_1 ;
wire \u_cordic/[12].U/n211_2 ;
wire \u_cordic/[12].U/n211_1_1 ;
wire \u_cordic/[12].U/n210_2 ;
wire \u_cordic/[12].U/n210_1_0_COUT ;
wire \u_cordic/[12].U/n209_2 ;
wire \u_cordic/[12].U/n209_1_1 ;
wire \u_cordic/[12].U/n208_2 ;
wire \u_cordic/[12].U/n208_1_1 ;
wire \u_cordic/[12].U/n207_2 ;
wire \u_cordic/[12].U/n207_1_1 ;
wire \u_cordic/[12].U/n206_2 ;
wire \u_cordic/[12].U/n206_1_1 ;
wire \u_cordic/[12].U/n205_2 ;
wire \u_cordic/[12].U/n205_1_1 ;
wire \u_cordic/[12].U/n204_2 ;
wire \u_cordic/[12].U/n204_1_1 ;
wire \u_cordic/[12].U/n203_2 ;
wire \u_cordic/[12].U/n203_1_1 ;
wire \u_cordic/[12].U/n202_2 ;
wire \u_cordic/[12].U/n202_1_1 ;
wire \u_cordic/[12].U/n201_2 ;
wire \u_cordic/[12].U/n201_1_1 ;
wire \u_cordic/[12].U/n200_2 ;
wire \u_cordic/[12].U/n200_1_1 ;
wire \u_cordic/[12].U/n199_2 ;
wire \u_cordic/[12].U/n199_1_1 ;
wire \u_cordic/[12].U/n198_2 ;
wire \u_cordic/[12].U/n198_1_1 ;
wire \u_cordic/[12].U/n197_2 ;
wire \u_cordic/[12].U/n197_1_1 ;
wire \u_cordic/[12].U/n196_2 ;
wire \u_cordic/[12].U/n196_1_1 ;
wire \u_cordic/[12].U/n195_2 ;
wire \u_cordic/[12].U/n195_1_1 ;
wire \u_cordic/[12].U/n194_2 ;
wire \u_cordic/[12].U/n194_1_1 ;
wire \u_cordic/[12].U/n193_2 ;
wire \u_cordic/[12].U/n193_1_0_COUT ;
wire \u_cordic/[13].U/n194_2 ;
wire \u_cordic/[13].U/n194_1_1 ;
wire \u_cordic/[13].U/n193_2 ;
wire \u_cordic/[13].U/n193_1_1 ;
wire \u_cordic/[13].U/n192_2 ;
wire \u_cordic/[13].U/n192_1_1 ;
wire \u_cordic/[13].U/n191_2 ;
wire \u_cordic/[13].U/n191_1_1 ;
wire \u_cordic/[13].U/n190_2 ;
wire \u_cordic/[13].U/n190_1_1 ;
wire \u_cordic/[13].U/n189_2 ;
wire \u_cordic/[13].U/n189_1_1 ;
wire \u_cordic/[13].U/n188_2 ;
wire \u_cordic/[13].U/n188_1_1 ;
wire \u_cordic/[13].U/n187_2 ;
wire \u_cordic/[13].U/n187_1_1 ;
wire \u_cordic/[13].U/n186_2 ;
wire \u_cordic/[13].U/n186_1_1 ;
wire \u_cordic/[13].U/n185_2 ;
wire \u_cordic/[13].U/n185_1_1 ;
wire \u_cordic/[13].U/n184_2 ;
wire \u_cordic/[13].U/n184_1_1 ;
wire \u_cordic/[13].U/n183_2 ;
wire \u_cordic/[13].U/n183_1_1 ;
wire \u_cordic/[13].U/n182_2 ;
wire \u_cordic/[13].U/n182_1_1 ;
wire \u_cordic/[13].U/n181_2 ;
wire \u_cordic/[13].U/n181_1_1 ;
wire \u_cordic/[13].U/n180_2 ;
wire \u_cordic/[13].U/n180_1_1 ;
wire \u_cordic/[13].U/n179_2 ;
wire \u_cordic/[13].U/n179_1_1 ;
wire \u_cordic/[13].U/n178_2 ;
wire \u_cordic/[13].U/n178_1_0_COUT ;
wire \u_cordic/[13].U/n226_2 ;
wire \u_cordic/[13].U/n226_1_1 ;
wire \u_cordic/[13].U/n225_2 ;
wire \u_cordic/[13].U/n225_1_1 ;
wire \u_cordic/[13].U/n224_2 ;
wire \u_cordic/[13].U/n224_1_1 ;
wire \u_cordic/[13].U/n223_2 ;
wire \u_cordic/[13].U/n223_1_1 ;
wire \u_cordic/[13].U/n222_2 ;
wire \u_cordic/[13].U/n222_1_1 ;
wire \u_cordic/[13].U/n221_2 ;
wire \u_cordic/[13].U/n221_1_1 ;
wire \u_cordic/[13].U/n220_2 ;
wire \u_cordic/[13].U/n220_1_1 ;
wire \u_cordic/[13].U/n219_2 ;
wire \u_cordic/[13].U/n219_1_1 ;
wire \u_cordic/[13].U/n218_2 ;
wire \u_cordic/[13].U/n218_1_1 ;
wire \u_cordic/[13].U/n217_2 ;
wire \u_cordic/[13].U/n217_1_1 ;
wire \u_cordic/[13].U/n216_2 ;
wire \u_cordic/[13].U/n216_1_1 ;
wire \u_cordic/[13].U/n215_2 ;
wire \u_cordic/[13].U/n215_1_1 ;
wire \u_cordic/[13].U/n214_2 ;
wire \u_cordic/[13].U/n214_1_1 ;
wire \u_cordic/[13].U/n213_2 ;
wire \u_cordic/[13].U/n213_1_1 ;
wire \u_cordic/[13].U/n212_2 ;
wire \u_cordic/[13].U/n212_1_0_COUT ;
wire \u_cordic/[13].U/n211_2 ;
wire \u_cordic/[13].U/n211_1_1 ;
wire \u_cordic/[13].U/n210_2 ;
wire \u_cordic/[13].U/n210_1_1 ;
wire \u_cordic/[13].U/n209_2 ;
wire \u_cordic/[13].U/n209_1_1 ;
wire \u_cordic/[13].U/n208_2 ;
wire \u_cordic/[13].U/n208_1_1 ;
wire \u_cordic/[13].U/n207_2 ;
wire \u_cordic/[13].U/n207_1_1 ;
wire \u_cordic/[13].U/n206_2 ;
wire \u_cordic/[13].U/n206_1_1 ;
wire \u_cordic/[13].U/n205_2 ;
wire \u_cordic/[13].U/n205_1_1 ;
wire \u_cordic/[13].U/n204_2 ;
wire \u_cordic/[13].U/n204_1_1 ;
wire \u_cordic/[13].U/n203_2 ;
wire \u_cordic/[13].U/n203_1_1 ;
wire \u_cordic/[13].U/n202_2 ;
wire \u_cordic/[13].U/n202_1_1 ;
wire \u_cordic/[13].U/n201_2 ;
wire \u_cordic/[13].U/n201_1_1 ;
wire \u_cordic/[13].U/n200_2 ;
wire \u_cordic/[13].U/n200_1_1 ;
wire \u_cordic/[13].U/n199_2 ;
wire \u_cordic/[13].U/n199_1_1 ;
wire \u_cordic/[13].U/n198_2 ;
wire \u_cordic/[13].U/n198_1_1 ;
wire \u_cordic/[13].U/n197_2 ;
wire \u_cordic/[13].U/n197_1_1 ;
wire \u_cordic/[13].U/n196_2 ;
wire \u_cordic/[13].U/n196_1_1 ;
wire \u_cordic/[13].U/n195_2 ;
wire \u_cordic/[13].U/n195_1_0_COUT ;
wire \u_cordic/[14].U/n196_2 ;
wire \u_cordic/[14].U/n196_1_1 ;
wire \u_cordic/[14].U/n195_2 ;
wire \u_cordic/[14].U/n195_1_1 ;
wire \u_cordic/[14].U/n194_2 ;
wire \u_cordic/[14].U/n194_1_1 ;
wire \u_cordic/[14].U/n193_2 ;
wire \u_cordic/[14].U/n193_1_1 ;
wire \u_cordic/[14].U/n192_2 ;
wire \u_cordic/[14].U/n192_1_1 ;
wire \u_cordic/[14].U/n191_2 ;
wire \u_cordic/[14].U/n191_1_1 ;
wire \u_cordic/[14].U/n190_2 ;
wire \u_cordic/[14].U/n190_1_1 ;
wire \u_cordic/[14].U/n189_2 ;
wire \u_cordic/[14].U/n189_1_1 ;
wire \u_cordic/[14].U/n188_2 ;
wire \u_cordic/[14].U/n188_1_1 ;
wire \u_cordic/[14].U/n187_2 ;
wire \u_cordic/[14].U/n187_1_1 ;
wire \u_cordic/[14].U/n186_2 ;
wire \u_cordic/[14].U/n186_1_1 ;
wire \u_cordic/[14].U/n185_2 ;
wire \u_cordic/[14].U/n185_1_1 ;
wire \u_cordic/[14].U/n184_2 ;
wire \u_cordic/[14].U/n184_1_1 ;
wire \u_cordic/[14].U/n183_2 ;
wire \u_cordic/[14].U/n183_1_1 ;
wire \u_cordic/[14].U/n182_2 ;
wire \u_cordic/[14].U/n182_1_1 ;
wire \u_cordic/[14].U/n181_2 ;
wire \u_cordic/[14].U/n181_1_1 ;
wire \u_cordic/[14].U/n180_2 ;
wire \u_cordic/[14].U/n180_1_0_COUT ;
wire \u_cordic/[14].U/n229_2 ;
wire \u_cordic/[14].U/n229_1_1 ;
wire \u_cordic/[14].U/n228_2 ;
wire \u_cordic/[14].U/n228_1_1 ;
wire \u_cordic/[14].U/n227_2 ;
wire \u_cordic/[14].U/n227_1_1 ;
wire \u_cordic/[14].U/n226_2 ;
wire \u_cordic/[14].U/n226_1_1 ;
wire \u_cordic/[14].U/n225_2 ;
wire \u_cordic/[14].U/n225_1_1 ;
wire \u_cordic/[14].U/n224_2 ;
wire \u_cordic/[14].U/n224_1_1 ;
wire \u_cordic/[14].U/n223_2 ;
wire \u_cordic/[14].U/n223_1_1 ;
wire \u_cordic/[14].U/n222_2 ;
wire \u_cordic/[14].U/n222_1_1 ;
wire \u_cordic/[14].U/n221_2 ;
wire \u_cordic/[14].U/n221_1_1 ;
wire \u_cordic/[14].U/n220_2 ;
wire \u_cordic/[14].U/n220_1_1 ;
wire \u_cordic/[14].U/n219_2 ;
wire \u_cordic/[14].U/n219_1_1 ;
wire \u_cordic/[14].U/n218_2 ;
wire \u_cordic/[14].U/n218_1_1 ;
wire \u_cordic/[14].U/n217_2 ;
wire \u_cordic/[14].U/n217_1_1 ;
wire \u_cordic/[14].U/n216_2 ;
wire \u_cordic/[14].U/n216_1_1 ;
wire \u_cordic/[14].U/n215_2 ;
wire \u_cordic/[14].U/n215_1_1 ;
wire \u_cordic/[14].U/n214_2 ;
wire \u_cordic/[14].U/n214_1_0_COUT ;
wire \u_cordic/[14].U/n213_2 ;
wire \u_cordic/[14].U/n213_1_1 ;
wire \u_cordic/[14].U/n212_2 ;
wire \u_cordic/[14].U/n212_1_1 ;
wire \u_cordic/[14].U/n211_2 ;
wire \u_cordic/[14].U/n211_1_1 ;
wire \u_cordic/[14].U/n210_2 ;
wire \u_cordic/[14].U/n210_1_1 ;
wire \u_cordic/[14].U/n209_2 ;
wire \u_cordic/[14].U/n209_1_1 ;
wire \u_cordic/[14].U/n208_2 ;
wire \u_cordic/[14].U/n208_1_1 ;
wire \u_cordic/[14].U/n207_2 ;
wire \u_cordic/[14].U/n207_1_1 ;
wire \u_cordic/[14].U/n206_2 ;
wire \u_cordic/[14].U/n206_1_1 ;
wire \u_cordic/[14].U/n205_2 ;
wire \u_cordic/[14].U/n205_1_1 ;
wire \u_cordic/[14].U/n204_2 ;
wire \u_cordic/[14].U/n204_1_1 ;
wire \u_cordic/[14].U/n203_2 ;
wire \u_cordic/[14].U/n203_1_1 ;
wire \u_cordic/[14].U/n202_2 ;
wire \u_cordic/[14].U/n202_1_1 ;
wire \u_cordic/[14].U/n201_2 ;
wire \u_cordic/[14].U/n201_1_1 ;
wire \u_cordic/[14].U/n200_2 ;
wire \u_cordic/[14].U/n200_1_1 ;
wire \u_cordic/[14].U/n199_2 ;
wire \u_cordic/[14].U/n199_1_1 ;
wire \u_cordic/[14].U/n198_2 ;
wire \u_cordic/[14].U/n198_1_1 ;
wire \u_cordic/[14].U/n197_2 ;
wire \u_cordic/[14].U/n197_1_0_COUT ;
wire \u_cordic/[15].U/n198_2 ;
wire \u_cordic/[15].U/n198_1_1 ;
wire \u_cordic/[15].U/n197_2 ;
wire \u_cordic/[15].U/n197_1_1 ;
wire \u_cordic/[15].U/n196_2 ;
wire \u_cordic/[15].U/n196_1_1 ;
wire \u_cordic/[15].U/n195_2 ;
wire \u_cordic/[15].U/n195_1_1 ;
wire \u_cordic/[15].U/n194_2 ;
wire \u_cordic/[15].U/n194_1_1 ;
wire \u_cordic/[15].U/n193_2 ;
wire \u_cordic/[15].U/n193_1_1 ;
wire \u_cordic/[15].U/n192_2 ;
wire \u_cordic/[15].U/n192_1_1 ;
wire \u_cordic/[15].U/n191_2 ;
wire \u_cordic/[15].U/n191_1_1 ;
wire \u_cordic/[15].U/n190_2 ;
wire \u_cordic/[15].U/n190_1_1 ;
wire \u_cordic/[15].U/n189_2 ;
wire \u_cordic/[15].U/n189_1_1 ;
wire \u_cordic/[15].U/n188_2 ;
wire \u_cordic/[15].U/n188_1_1 ;
wire \u_cordic/[15].U/n187_2 ;
wire \u_cordic/[15].U/n187_1_1 ;
wire \u_cordic/[15].U/n186_2 ;
wire \u_cordic/[15].U/n186_1_1 ;
wire \u_cordic/[15].U/n185_2 ;
wire \u_cordic/[15].U/n185_1_1 ;
wire \u_cordic/[15].U/n184_2 ;
wire \u_cordic/[15].U/n184_1_1 ;
wire \u_cordic/[15].U/n183_2 ;
wire \u_cordic/[15].U/n183_1_1 ;
wire \u_cordic/[15].U/n182_2 ;
wire \u_cordic/[15].U/n182_1_0_COUT ;
wire \u_cordic/[15].U/n232_2 ;
wire \u_cordic/[15].U/n232_1_1 ;
wire \u_cordic/[15].U/n231_2 ;
wire \u_cordic/[15].U/n231_1_1 ;
wire \u_cordic/[15].U/n230_2 ;
wire \u_cordic/[15].U/n230_1_1 ;
wire \u_cordic/[15].U/n229_2 ;
wire \u_cordic/[15].U/n229_1_1 ;
wire \u_cordic/[15].U/n228_2 ;
wire \u_cordic/[15].U/n228_1_1 ;
wire \u_cordic/[15].U/n227_2 ;
wire \u_cordic/[15].U/n227_1_1 ;
wire \u_cordic/[15].U/n226_2 ;
wire \u_cordic/[15].U/n226_1_1 ;
wire \u_cordic/[15].U/n225_2 ;
wire \u_cordic/[15].U/n225_1_1 ;
wire \u_cordic/[15].U/n224_2 ;
wire \u_cordic/[15].U/n224_1_1 ;
wire \u_cordic/[15].U/n223_2 ;
wire \u_cordic/[15].U/n223_1_1 ;
wire \u_cordic/[15].U/n222_2 ;
wire \u_cordic/[15].U/n222_1_1 ;
wire \u_cordic/[15].U/n221_2 ;
wire \u_cordic/[15].U/n221_1_1 ;
wire \u_cordic/[15].U/n220_2 ;
wire \u_cordic/[15].U/n220_1_1 ;
wire \u_cordic/[15].U/n219_2 ;
wire \u_cordic/[15].U/n219_1_1 ;
wire \u_cordic/[15].U/n218_2 ;
wire \u_cordic/[15].U/n218_1_1 ;
wire \u_cordic/[15].U/n217_2 ;
wire \u_cordic/[15].U/n217_1_1 ;
wire \u_cordic/[15].U/n216_2 ;
wire \u_cordic/[15].U/n216_1_0_COUT ;
wire \u_cordic/[15].U/n215_2 ;
wire \u_cordic/[15].U/n215_1_1 ;
wire \u_cordic/[15].U/n214_2 ;
wire \u_cordic/[15].U/n214_1_1 ;
wire \u_cordic/[15].U/n213_2 ;
wire \u_cordic/[15].U/n213_1_1 ;
wire \u_cordic/[15].U/n212_2 ;
wire \u_cordic/[15].U/n212_1_1 ;
wire \u_cordic/[15].U/n211_2 ;
wire \u_cordic/[15].U/n211_1_1 ;
wire \u_cordic/[15].U/n210_2 ;
wire \u_cordic/[15].U/n210_1_1 ;
wire \u_cordic/[15].U/n209_2 ;
wire \u_cordic/[15].U/n209_1_1 ;
wire \u_cordic/[15].U/n208_2 ;
wire \u_cordic/[15].U/n208_1_1 ;
wire \u_cordic/[15].U/n207_2 ;
wire \u_cordic/[15].U/n207_1_1 ;
wire \u_cordic/[15].U/n206_2 ;
wire \u_cordic/[15].U/n206_1_1 ;
wire \u_cordic/[15].U/n205_2 ;
wire \u_cordic/[15].U/n205_1_1 ;
wire \u_cordic/[15].U/n204_2 ;
wire \u_cordic/[15].U/n204_1_1 ;
wire \u_cordic/[15].U/n203_2 ;
wire \u_cordic/[15].U/n203_1_1 ;
wire \u_cordic/[15].U/n202_2 ;
wire \u_cordic/[15].U/n202_1_1 ;
wire \u_cordic/[15].U/n201_2 ;
wire \u_cordic/[15].U/n201_1_1 ;
wire \u_cordic/[15].U/n200_2 ;
wire \u_cordic/[15].U/n200_1_1 ;
wire \u_cordic/[15].U/n199_2 ;
wire \u_cordic/[15].U/n199_1_0_COUT ;
VCC VCC_cZ (
  .V(VCC)
);
GND GND_cZ (
  .G(GND)
);
GSR GSR (
	.GSRI(VCC)
);
LUT1 \theta_i_d[16]_s3  (
	.I0(theta_i[16]),
	.F(\theta_i_d[16]_7 )
);
defparam \theta_i_d[16]_s3 .INIT=2'h1;
DFFR \u_cordic/[0].U/x_1_15_s0  (
	.D(\u_cordic/[0].U/n177_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [15])
);
defparam \u_cordic/[0].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_14_s0  (
	.D(\u_cordic/[0].U/n178_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [14])
);
defparam \u_cordic/[0].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_13_s0  (
	.D(\u_cordic/[0].U/n179_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [13])
);
defparam \u_cordic/[0].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_12_s0  (
	.D(\u_cordic/[0].U/n180_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [12])
);
defparam \u_cordic/[0].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_11_s0  (
	.D(\u_cordic/[0].U/n181_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [11])
);
defparam \u_cordic/[0].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_10_s0  (
	.D(\u_cordic/[0].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [10])
);
defparam \u_cordic/[0].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_9_s0  (
	.D(\u_cordic/[0].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [9])
);
defparam \u_cordic/[0].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_8_s0  (
	.D(\u_cordic/[0].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [8])
);
defparam \u_cordic/[0].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_7_s0  (
	.D(\u_cordic/[0].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [7])
);
defparam \u_cordic/[0].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_6_s0  (
	.D(\u_cordic/[0].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [6])
);
defparam \u_cordic/[0].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_5_s0  (
	.D(\u_cordic/[0].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [5])
);
defparam \u_cordic/[0].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_4_s0  (
	.D(\u_cordic/[0].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [4])
);
defparam \u_cordic/[0].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_3_s0  (
	.D(\u_cordic/[0].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [3])
);
defparam \u_cordic/[0].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_2_s0  (
	.D(\u_cordic/[0].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [2])
);
defparam \u_cordic/[0].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_1_s0  (
	.D(\u_cordic/[0].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [1])
);
defparam \u_cordic/[0].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_0_s0  (
	.D(\u_cordic/[0].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [0])
);
defparam \u_cordic/[0].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_16_s0  (
	.D(\u_cordic/[0].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [16])
);
defparam \u_cordic/[0].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_15_s0  (
	.D(\u_cordic/[0].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [15])
);
defparam \u_cordic/[0].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_14_s0  (
	.D(\u_cordic/[0].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [14])
);
defparam \u_cordic/[0].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_13_s0  (
	.D(\u_cordic/[0].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [13])
);
defparam \u_cordic/[0].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_12_s0  (
	.D(\u_cordic/[0].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [12])
);
defparam \u_cordic/[0].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_11_s0  (
	.D(\u_cordic/[0].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [11])
);
defparam \u_cordic/[0].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_10_s0  (
	.D(\u_cordic/[0].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [10])
);
defparam \u_cordic/[0].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_9_s0  (
	.D(\u_cordic/[0].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [9])
);
defparam \u_cordic/[0].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_8_s0  (
	.D(\u_cordic/[0].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [8])
);
defparam \u_cordic/[0].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_7_s0  (
	.D(\u_cordic/[0].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [7])
);
defparam \u_cordic/[0].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_6_s0  (
	.D(\u_cordic/[0].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [6])
);
defparam \u_cordic/[0].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_5_s0  (
	.D(\u_cordic/[0].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [5])
);
defparam \u_cordic/[0].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_4_s0  (
	.D(\u_cordic/[0].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [4])
);
defparam \u_cordic/[0].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_3_s0  (
	.D(\u_cordic/[0].U/n206_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [3])
);
defparam \u_cordic/[0].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_2_s0  (
	.D(\u_cordic/[0].U/n207_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [2])
);
defparam \u_cordic/[0].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_1_s0  (
	.D(\u_cordic/[0].U/n208_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [1])
);
defparam \u_cordic/[0].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/y_1_0_s0  (
	.D(\u_cordic/[0].U/n209_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[1] [0])
);
defparam \u_cordic/[0].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_16_s0  (
	.D(\u_cordic/[0].U/n210_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [16])
);
defparam \u_cordic/[0].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_15_s0  (
	.D(\u_cordic/[0].U/n211_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [15])
);
defparam \u_cordic/[0].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_14_s0  (
	.D(\u_cordic/[0].U/n212_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [14])
);
defparam \u_cordic/[0].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_13_s0  (
	.D(\u_cordic/[0].U/n213_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [13])
);
defparam \u_cordic/[0].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_12_s0  (
	.D(\u_cordic/[0].U/n214_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [12])
);
defparam \u_cordic/[0].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_11_s0  (
	.D(\u_cordic/[0].U/n215_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [11])
);
defparam \u_cordic/[0].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_10_s0  (
	.D(\u_cordic/[0].U/n216_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [10])
);
defparam \u_cordic/[0].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_9_s0  (
	.D(\u_cordic/[0].U/n217_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [9])
);
defparam \u_cordic/[0].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_8_s0  (
	.D(\u_cordic/[0].U/n218_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [8])
);
defparam \u_cordic/[0].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_7_s0  (
	.D(\u_cordic/[0].U/n219_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [7])
);
defparam \u_cordic/[0].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_6_s0  (
	.D(\u_cordic/[0].U/n220_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [6])
);
defparam \u_cordic/[0].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_5_s0  (
	.D(\u_cordic/[0].U/n221_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [5])
);
defparam \u_cordic/[0].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_4_s0  (
	.D(\u_cordic/[0].U/n222_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [4])
);
defparam \u_cordic/[0].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_3_s0  (
	.D(\u_cordic/[0].U/n223_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [3])
);
defparam \u_cordic/[0].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_2_s0  (
	.D(theta_i[2]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [2])
);
defparam \u_cordic/[0].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_1_s0  (
	.D(theta_i[1]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [1])
);
defparam \u_cordic/[0].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/z_1_0_s0  (
	.D(theta_i[0]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[1] [0])
);
defparam \u_cordic/[0].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[0].U/x_1_16_s0  (
	.D(\u_cordic/[0].U/n176_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[1] [16])
);
defparam \u_cordic/[0].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[0].U/n192_1_s  (
	.I0(x_i[0]),
	.I1(y_i[0]),
	.I3(theta_i[16]),
	.CIN(\theta_i_d[16]_7 ),
	.COUT(\u_cordic/[0].U/n192_1_1 ),
	.SUM(\u_cordic/[0].U/n192_2 )
);
defparam \u_cordic/[0].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n191_1_s  (
	.I0(x_i[1]),
	.I1(y_i[1]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n192_1_1 ),
	.COUT(\u_cordic/[0].U/n191_1_1 ),
	.SUM(\u_cordic/[0].U/n191_2 )
);
defparam \u_cordic/[0].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n190_1_s  (
	.I0(x_i[2]),
	.I1(y_i[2]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n191_1_1 ),
	.COUT(\u_cordic/[0].U/n190_1_1 ),
	.SUM(\u_cordic/[0].U/n190_2 )
);
defparam \u_cordic/[0].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n189_1_s  (
	.I0(x_i[3]),
	.I1(y_i[3]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n190_1_1 ),
	.COUT(\u_cordic/[0].U/n189_1_1 ),
	.SUM(\u_cordic/[0].U/n189_2 )
);
defparam \u_cordic/[0].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n188_1_s  (
	.I0(x_i[4]),
	.I1(y_i[4]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n189_1_1 ),
	.COUT(\u_cordic/[0].U/n188_1_1 ),
	.SUM(\u_cordic/[0].U/n188_2 )
);
defparam \u_cordic/[0].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n187_1_s  (
	.I0(x_i[5]),
	.I1(y_i[5]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n188_1_1 ),
	.COUT(\u_cordic/[0].U/n187_1_1 ),
	.SUM(\u_cordic/[0].U/n187_2 )
);
defparam \u_cordic/[0].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n186_1_s  (
	.I0(x_i[6]),
	.I1(y_i[6]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n187_1_1 ),
	.COUT(\u_cordic/[0].U/n186_1_1 ),
	.SUM(\u_cordic/[0].U/n186_2 )
);
defparam \u_cordic/[0].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n185_1_s  (
	.I0(x_i[7]),
	.I1(y_i[7]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n186_1_1 ),
	.COUT(\u_cordic/[0].U/n185_1_1 ),
	.SUM(\u_cordic/[0].U/n185_2 )
);
defparam \u_cordic/[0].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n184_1_s  (
	.I0(x_i[8]),
	.I1(y_i[8]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n185_1_1 ),
	.COUT(\u_cordic/[0].U/n184_1_1 ),
	.SUM(\u_cordic/[0].U/n184_2 )
);
defparam \u_cordic/[0].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n183_1_s  (
	.I0(x_i[9]),
	.I1(y_i[9]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n184_1_1 ),
	.COUT(\u_cordic/[0].U/n183_1_1 ),
	.SUM(\u_cordic/[0].U/n183_2 )
);
defparam \u_cordic/[0].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n182_1_s  (
	.I0(x_i[10]),
	.I1(y_i[10]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n183_1_1 ),
	.COUT(\u_cordic/[0].U/n182_1_1 ),
	.SUM(\u_cordic/[0].U/n182_2 )
);
defparam \u_cordic/[0].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n181_1_s  (
	.I0(x_i[11]),
	.I1(y_i[11]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n182_1_1 ),
	.COUT(\u_cordic/[0].U/n181_1_1 ),
	.SUM(\u_cordic/[0].U/n181_2 )
);
defparam \u_cordic/[0].U/n181_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n180_1_s  (
	.I0(x_i[12]),
	.I1(y_i[12]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n181_1_1 ),
	.COUT(\u_cordic/[0].U/n180_1_1 ),
	.SUM(\u_cordic/[0].U/n180_2 )
);
defparam \u_cordic/[0].U/n180_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n179_1_s  (
	.I0(x_i[13]),
	.I1(y_i[13]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n180_1_1 ),
	.COUT(\u_cordic/[0].U/n179_1_1 ),
	.SUM(\u_cordic/[0].U/n179_2 )
);
defparam \u_cordic/[0].U/n179_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n178_1_s  (
	.I0(x_i[14]),
	.I1(y_i[14]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n179_1_1 ),
	.COUT(\u_cordic/[0].U/n178_1_1 ),
	.SUM(\u_cordic/[0].U/n178_2 )
);
defparam \u_cordic/[0].U/n178_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n177_1_s  (
	.I0(x_i[15]),
	.I1(y_i[15]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n178_1_1 ),
	.COUT(\u_cordic/[0].U/n177_1_1 ),
	.SUM(\u_cordic/[0].U/n177_2 )
);
defparam \u_cordic/[0].U/n177_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n176_1_s  (
	.I0(x_i[16]),
	.I1(y_i[16]),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n177_1_1 ),
	.COUT(\u_cordic/[0].U/n176_1_0_COUT ),
	.SUM(\u_cordic/[0].U/n176_2 )
);
defparam \u_cordic/[0].U/n176_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n223_1_s  (
	.I0(theta_i[3]),
	.I1(VCC),
	.I3(theta_i[16]),
	.CIN(\theta_i_d[16]_7 ),
	.COUT(\u_cordic/[0].U/n223_1_1 ),
	.SUM(\u_cordic/[0].U/n223_2 )
);
defparam \u_cordic/[0].U/n223_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n222_1_s  (
	.I0(theta_i[4]),
	.I1(GND),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n223_1_1 ),
	.COUT(\u_cordic/[0].U/n222_1_1 ),
	.SUM(\u_cordic/[0].U/n222_2 )
);
defparam \u_cordic/[0].U/n222_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n221_1_s  (
	.I0(theta_i[5]),
	.I1(GND),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n222_1_1 ),
	.COUT(\u_cordic/[0].U/n221_1_1 ),
	.SUM(\u_cordic/[0].U/n221_2 )
);
defparam \u_cordic/[0].U/n221_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n220_1_s  (
	.I0(theta_i[6]),
	.I1(GND),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n221_1_1 ),
	.COUT(\u_cordic/[0].U/n220_1_1 ),
	.SUM(\u_cordic/[0].U/n220_2 )
);
defparam \u_cordic/[0].U/n220_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n219_1_s  (
	.I0(theta_i[7]),
	.I1(VCC),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n220_1_1 ),
	.COUT(\u_cordic/[0].U/n219_1_1 ),
	.SUM(\u_cordic/[0].U/n219_2 )
);
defparam \u_cordic/[0].U/n219_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n218_1_s  (
	.I0(theta_i[8]),
	.I1(GND),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n219_1_1 ),
	.COUT(\u_cordic/[0].U/n218_1_1 ),
	.SUM(\u_cordic/[0].U/n218_2 )
);
defparam \u_cordic/[0].U/n218_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n217_1_s  (
	.I0(theta_i[9]),
	.I1(GND),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n218_1_1 ),
	.COUT(\u_cordic/[0].U/n217_1_1 ),
	.SUM(\u_cordic/[0].U/n217_2 )
);
defparam \u_cordic/[0].U/n217_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n216_1_s  (
	.I0(theta_i[10]),
	.I1(VCC),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n217_1_1 ),
	.COUT(\u_cordic/[0].U/n216_1_1 ),
	.SUM(\u_cordic/[0].U/n216_2 )
);
defparam \u_cordic/[0].U/n216_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n215_1_s  (
	.I0(theta_i[11]),
	.I1(GND),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n216_1_1 ),
	.COUT(\u_cordic/[0].U/n215_1_1 ),
	.SUM(\u_cordic/[0].U/n215_2 )
);
defparam \u_cordic/[0].U/n215_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n214_1_s  (
	.I0(theta_i[12]),
	.I1(GND),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n215_1_1 ),
	.COUT(\u_cordic/[0].U/n214_1_1 ),
	.SUM(\u_cordic/[0].U/n214_2 )
);
defparam \u_cordic/[0].U/n214_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n213_1_s  (
	.I0(theta_i[13]),
	.I1(VCC),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n214_1_1 ),
	.COUT(\u_cordic/[0].U/n213_1_1 ),
	.SUM(\u_cordic/[0].U/n213_2 )
);
defparam \u_cordic/[0].U/n213_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n212_1_s  (
	.I0(theta_i[14]),
	.I1(VCC),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n213_1_1 ),
	.COUT(\u_cordic/[0].U/n212_1_1 ),
	.SUM(\u_cordic/[0].U/n212_2 )
);
defparam \u_cordic/[0].U/n212_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n211_1_s  (
	.I0(theta_i[15]),
	.I1(GND),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n212_1_1 ),
	.COUT(\u_cordic/[0].U/n211_1_1 ),
	.SUM(\u_cordic/[0].U/n211_2 )
);
defparam \u_cordic/[0].U/n211_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n210_1_s  (
	.I0(theta_i[16]),
	.I1(GND),
	.I3(theta_i[16]),
	.CIN(\u_cordic/[0].U/n211_1_1 ),
	.COUT(\u_cordic/[0].U/n210_1_0_COUT ),
	.SUM(\u_cordic/[0].U/n210_2 )
);
defparam \u_cordic/[0].U/n210_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n209_1_s  (
	.I0(y_i[0]),
	.I1(x_i[0]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(theta_i[16]),
	.COUT(\u_cordic/[0].U/n209_1_1 ),
	.SUM(\u_cordic/[0].U/n209_2 )
);
defparam \u_cordic/[0].U/n209_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n208_1_s  (
	.I0(y_i[1]),
	.I1(x_i[1]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n209_1_1 ),
	.COUT(\u_cordic/[0].U/n208_1_1 ),
	.SUM(\u_cordic/[0].U/n208_2 )
);
defparam \u_cordic/[0].U/n208_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n207_1_s  (
	.I0(y_i[2]),
	.I1(x_i[2]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n208_1_1 ),
	.COUT(\u_cordic/[0].U/n207_1_1 ),
	.SUM(\u_cordic/[0].U/n207_2 )
);
defparam \u_cordic/[0].U/n207_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n206_1_s  (
	.I0(y_i[3]),
	.I1(x_i[3]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n207_1_1 ),
	.COUT(\u_cordic/[0].U/n206_1_1 ),
	.SUM(\u_cordic/[0].U/n206_2 )
);
defparam \u_cordic/[0].U/n206_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n205_1_s  (
	.I0(y_i[4]),
	.I1(x_i[4]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n206_1_1 ),
	.COUT(\u_cordic/[0].U/n205_1_1 ),
	.SUM(\u_cordic/[0].U/n205_2 )
);
defparam \u_cordic/[0].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n204_1_s  (
	.I0(y_i[5]),
	.I1(x_i[5]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n205_1_1 ),
	.COUT(\u_cordic/[0].U/n204_1_1 ),
	.SUM(\u_cordic/[0].U/n204_2 )
);
defparam \u_cordic/[0].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n203_1_s  (
	.I0(y_i[6]),
	.I1(x_i[6]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n204_1_1 ),
	.COUT(\u_cordic/[0].U/n203_1_1 ),
	.SUM(\u_cordic/[0].U/n203_2 )
);
defparam \u_cordic/[0].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n202_1_s  (
	.I0(y_i[7]),
	.I1(x_i[7]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n203_1_1 ),
	.COUT(\u_cordic/[0].U/n202_1_1 ),
	.SUM(\u_cordic/[0].U/n202_2 )
);
defparam \u_cordic/[0].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n201_1_s  (
	.I0(y_i[8]),
	.I1(x_i[8]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n202_1_1 ),
	.COUT(\u_cordic/[0].U/n201_1_1 ),
	.SUM(\u_cordic/[0].U/n201_2 )
);
defparam \u_cordic/[0].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n200_1_s  (
	.I0(y_i[9]),
	.I1(x_i[9]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n201_1_1 ),
	.COUT(\u_cordic/[0].U/n200_1_1 ),
	.SUM(\u_cordic/[0].U/n200_2 )
);
defparam \u_cordic/[0].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n199_1_s  (
	.I0(y_i[10]),
	.I1(x_i[10]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n200_1_1 ),
	.COUT(\u_cordic/[0].U/n199_1_1 ),
	.SUM(\u_cordic/[0].U/n199_2 )
);
defparam \u_cordic/[0].U/n199_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n198_1_s  (
	.I0(y_i[11]),
	.I1(x_i[11]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n199_1_1 ),
	.COUT(\u_cordic/[0].U/n198_1_1 ),
	.SUM(\u_cordic/[0].U/n198_2 )
);
defparam \u_cordic/[0].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n197_1_s  (
	.I0(y_i[12]),
	.I1(x_i[12]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n198_1_1 ),
	.COUT(\u_cordic/[0].U/n197_1_1 ),
	.SUM(\u_cordic/[0].U/n197_2 )
);
defparam \u_cordic/[0].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n196_1_s  (
	.I0(y_i[13]),
	.I1(x_i[13]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n197_1_1 ),
	.COUT(\u_cordic/[0].U/n196_1_1 ),
	.SUM(\u_cordic/[0].U/n196_2 )
);
defparam \u_cordic/[0].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n195_1_s  (
	.I0(y_i[14]),
	.I1(x_i[14]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n196_1_1 ),
	.COUT(\u_cordic/[0].U/n195_1_1 ),
	.SUM(\u_cordic/[0].U/n195_2 )
);
defparam \u_cordic/[0].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n194_1_s  (
	.I0(y_i[15]),
	.I1(x_i[15]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n195_1_1 ),
	.COUT(\u_cordic/[0].U/n194_1_1 ),
	.SUM(\u_cordic/[0].U/n194_2 )
);
defparam \u_cordic/[0].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[0].U/n193_1_s  (
	.I0(y_i[16]),
	.I1(x_i[16]),
	.I3(\theta_i_d[16]_7 ),
	.CIN(\u_cordic/[0].U/n194_1_1 ),
	.COUT(\u_cordic/[0].U/n193_1_0_COUT ),
	.SUM(\u_cordic/[0].U/n193_2 )
);
defparam \u_cordic/[0].U/n193_1_s .ALU_MODE=2;
LUT1 \u_cordic/[0].U/z[1][16]_1_s3  (
	.I0(\u_cordic/z[1] [16]),
	.F(\u_cordic/z[1][16]_1_5 )
);
defparam \u_cordic/[0].U/z[1][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[1].U/x_1_15_s0  (
	.D(\u_cordic/[1].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [15])
);
defparam \u_cordic/[1].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_14_s0  (
	.D(\u_cordic/[1].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [14])
);
defparam \u_cordic/[1].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_13_s0  (
	.D(\u_cordic/[1].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [13])
);
defparam \u_cordic/[1].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_12_s0  (
	.D(\u_cordic/[1].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [12])
);
defparam \u_cordic/[1].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_11_s0  (
	.D(\u_cordic/[1].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [11])
);
defparam \u_cordic/[1].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_10_s0  (
	.D(\u_cordic/[1].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [10])
);
defparam \u_cordic/[1].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_9_s0  (
	.D(\u_cordic/[1].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [9])
);
defparam \u_cordic/[1].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_8_s0  (
	.D(\u_cordic/[1].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [8])
);
defparam \u_cordic/[1].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_7_s0  (
	.D(\u_cordic/[1].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [7])
);
defparam \u_cordic/[1].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_6_s0  (
	.D(\u_cordic/[1].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [6])
);
defparam \u_cordic/[1].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_5_s0  (
	.D(\u_cordic/[1].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [5])
);
defparam \u_cordic/[1].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_4_s0  (
	.D(\u_cordic/[1].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [4])
);
defparam \u_cordic/[1].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_3_s0  (
	.D(\u_cordic/[1].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [3])
);
defparam \u_cordic/[1].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_2_s0  (
	.D(\u_cordic/[1].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [2])
);
defparam \u_cordic/[1].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_1_s0  (
	.D(\u_cordic/[1].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [1])
);
defparam \u_cordic/[1].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_0_s0  (
	.D(\u_cordic/[1].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [0])
);
defparam \u_cordic/[1].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_16_s0  (
	.D(\u_cordic/[1].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [16])
);
defparam \u_cordic/[1].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_15_s0  (
	.D(\u_cordic/[1].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [15])
);
defparam \u_cordic/[1].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_14_s0  (
	.D(\u_cordic/[1].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [14])
);
defparam \u_cordic/[1].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_13_s0  (
	.D(\u_cordic/[1].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [13])
);
defparam \u_cordic/[1].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_12_s0  (
	.D(\u_cordic/[1].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [12])
);
defparam \u_cordic/[1].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_11_s0  (
	.D(\u_cordic/[1].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [11])
);
defparam \u_cordic/[1].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_10_s0  (
	.D(\u_cordic/[1].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [10])
);
defparam \u_cordic/[1].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_9_s0  (
	.D(\u_cordic/[1].U/n206_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [9])
);
defparam \u_cordic/[1].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_8_s0  (
	.D(\u_cordic/[1].U/n207_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [8])
);
defparam \u_cordic/[1].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_7_s0  (
	.D(\u_cordic/[1].U/n208_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [7])
);
defparam \u_cordic/[1].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_6_s0  (
	.D(\u_cordic/[1].U/n209_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [6])
);
defparam \u_cordic/[1].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_5_s0  (
	.D(\u_cordic/[1].U/n210_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [5])
);
defparam \u_cordic/[1].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_4_s0  (
	.D(\u_cordic/[1].U/n211_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [4])
);
defparam \u_cordic/[1].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_3_s0  (
	.D(\u_cordic/[1].U/n212_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [3])
);
defparam \u_cordic/[1].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_2_s0  (
	.D(\u_cordic/[1].U/n213_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [2])
);
defparam \u_cordic/[1].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_1_s0  (
	.D(\u_cordic/[1].U/n214_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [1])
);
defparam \u_cordic/[1].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/y_1_0_s0  (
	.D(\u_cordic/[1].U/n215_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[2] [0])
);
defparam \u_cordic/[1].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_16_s0  (
	.D(\u_cordic/[1].U/n216_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [16])
);
defparam \u_cordic/[1].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_15_s0  (
	.D(\u_cordic/[1].U/n217_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [15])
);
defparam \u_cordic/[1].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_14_s0  (
	.D(\u_cordic/[1].U/n218_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [14])
);
defparam \u_cordic/[1].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_13_s0  (
	.D(\u_cordic/[1].U/n219_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [13])
);
defparam \u_cordic/[1].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_12_s0  (
	.D(\u_cordic/[1].U/n220_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [12])
);
defparam \u_cordic/[1].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_11_s0  (
	.D(\u_cordic/[1].U/n221_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [11])
);
defparam \u_cordic/[1].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_10_s0  (
	.D(\u_cordic/[1].U/n222_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [10])
);
defparam \u_cordic/[1].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_9_s0  (
	.D(\u_cordic/[1].U/n223_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [9])
);
defparam \u_cordic/[1].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_8_s0  (
	.D(\u_cordic/[1].U/n224_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [8])
);
defparam \u_cordic/[1].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_7_s0  (
	.D(\u_cordic/[1].U/n225_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [7])
);
defparam \u_cordic/[1].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_6_s0  (
	.D(\u_cordic/[1].U/n226_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [6])
);
defparam \u_cordic/[1].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_5_s0  (
	.D(\u_cordic/[1].U/n227_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [5])
);
defparam \u_cordic/[1].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_4_s0  (
	.D(\u_cordic/[1].U/n228_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [4])
);
defparam \u_cordic/[1].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_3_s0  (
	.D(\u_cordic/[1].U/n229_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [3])
);
defparam \u_cordic/[1].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_2_s0  (
	.D(\u_cordic/[1].U/n230_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [2])
);
defparam \u_cordic/[1].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_1_s0  (
	.D(\u_cordic/[1].U/n231_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [1])
);
defparam \u_cordic/[1].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/z_1_0_s0  (
	.D(\u_cordic/[1].U/n232_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[2] [0])
);
defparam \u_cordic/[1].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[1].U/x_1_16_s0  (
	.D(\u_cordic/[1].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[2] [16])
);
defparam \u_cordic/[1].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[1].U/n198_1_s  (
	.I0(\u_cordic/x[1] [0]),
	.I1(\u_cordic/y[1] [1]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/z[1][16]_1_5 ),
	.COUT(\u_cordic/[1].U/n198_1_1 ),
	.SUM(\u_cordic/[1].U/n198_2 )
);
defparam \u_cordic/[1].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n197_1_s  (
	.I0(\u_cordic/x[1] [1]),
	.I1(\u_cordic/y[1] [2]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n198_1_1 ),
	.COUT(\u_cordic/[1].U/n197_1_1 ),
	.SUM(\u_cordic/[1].U/n197_2 )
);
defparam \u_cordic/[1].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n196_1_s  (
	.I0(\u_cordic/x[1] [2]),
	.I1(\u_cordic/y[1] [3]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n197_1_1 ),
	.COUT(\u_cordic/[1].U/n196_1_1 ),
	.SUM(\u_cordic/[1].U/n196_2 )
);
defparam \u_cordic/[1].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n195_1_s  (
	.I0(\u_cordic/x[1] [3]),
	.I1(\u_cordic/y[1] [4]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n196_1_1 ),
	.COUT(\u_cordic/[1].U/n195_1_1 ),
	.SUM(\u_cordic/[1].U/n195_2 )
);
defparam \u_cordic/[1].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n194_1_s  (
	.I0(\u_cordic/x[1] [4]),
	.I1(\u_cordic/y[1] [5]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n195_1_1 ),
	.COUT(\u_cordic/[1].U/n194_1_1 ),
	.SUM(\u_cordic/[1].U/n194_2 )
);
defparam \u_cordic/[1].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n193_1_s  (
	.I0(\u_cordic/x[1] [5]),
	.I1(\u_cordic/y[1] [6]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n194_1_1 ),
	.COUT(\u_cordic/[1].U/n193_1_1 ),
	.SUM(\u_cordic/[1].U/n193_2 )
);
defparam \u_cordic/[1].U/n193_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n192_1_s  (
	.I0(\u_cordic/x[1] [6]),
	.I1(\u_cordic/y[1] [7]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n193_1_1 ),
	.COUT(\u_cordic/[1].U/n192_1_1 ),
	.SUM(\u_cordic/[1].U/n192_2 )
);
defparam \u_cordic/[1].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n191_1_s  (
	.I0(\u_cordic/x[1] [7]),
	.I1(\u_cordic/y[1] [8]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n192_1_1 ),
	.COUT(\u_cordic/[1].U/n191_1_1 ),
	.SUM(\u_cordic/[1].U/n191_2 )
);
defparam \u_cordic/[1].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n190_1_s  (
	.I0(\u_cordic/x[1] [8]),
	.I1(\u_cordic/y[1] [9]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n191_1_1 ),
	.COUT(\u_cordic/[1].U/n190_1_1 ),
	.SUM(\u_cordic/[1].U/n190_2 )
);
defparam \u_cordic/[1].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n189_1_s  (
	.I0(\u_cordic/x[1] [9]),
	.I1(\u_cordic/y[1] [10]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n190_1_1 ),
	.COUT(\u_cordic/[1].U/n189_1_1 ),
	.SUM(\u_cordic/[1].U/n189_2 )
);
defparam \u_cordic/[1].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n188_1_s  (
	.I0(\u_cordic/x[1] [10]),
	.I1(\u_cordic/y[1] [11]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n189_1_1 ),
	.COUT(\u_cordic/[1].U/n188_1_1 ),
	.SUM(\u_cordic/[1].U/n188_2 )
);
defparam \u_cordic/[1].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n187_1_s  (
	.I0(\u_cordic/x[1] [11]),
	.I1(\u_cordic/y[1] [12]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n188_1_1 ),
	.COUT(\u_cordic/[1].U/n187_1_1 ),
	.SUM(\u_cordic/[1].U/n187_2 )
);
defparam \u_cordic/[1].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n186_1_s  (
	.I0(\u_cordic/x[1] [12]),
	.I1(\u_cordic/y[1] [13]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n187_1_1 ),
	.COUT(\u_cordic/[1].U/n186_1_1 ),
	.SUM(\u_cordic/[1].U/n186_2 )
);
defparam \u_cordic/[1].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n185_1_s  (
	.I0(\u_cordic/x[1] [13]),
	.I1(\u_cordic/y[1] [14]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n186_1_1 ),
	.COUT(\u_cordic/[1].U/n185_1_1 ),
	.SUM(\u_cordic/[1].U/n185_2 )
);
defparam \u_cordic/[1].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n184_1_s  (
	.I0(\u_cordic/x[1] [14]),
	.I1(\u_cordic/y[1] [15]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n185_1_1 ),
	.COUT(\u_cordic/[1].U/n184_1_1 ),
	.SUM(\u_cordic/[1].U/n184_2 )
);
defparam \u_cordic/[1].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n183_1_s  (
	.I0(\u_cordic/x[1] [15]),
	.I1(\u_cordic/y[1] [16]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n184_1_1 ),
	.COUT(\u_cordic/[1].U/n183_1_1 ),
	.SUM(\u_cordic/[1].U/n183_2 )
);
defparam \u_cordic/[1].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n182_1_s  (
	.I0(\u_cordic/x[1] [16]),
	.I1(\u_cordic/y[1] [16]),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n183_1_1 ),
	.COUT(\u_cordic/[1].U/n182_1_0_COUT ),
	.SUM(\u_cordic/[1].U/n182_2 )
);
defparam \u_cordic/[1].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n232_1_s  (
	.I0(\u_cordic/z[1] [0]),
	.I1(VCC),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/z[1][16]_1_5 ),
	.COUT(\u_cordic/[1].U/n232_1_1 ),
	.SUM(\u_cordic/[1].U/n232_2 )
);
defparam \u_cordic/[1].U/n232_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n231_1_s  (
	.I0(\u_cordic/z[1] [1]),
	.I1(GND),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n232_1_1 ),
	.COUT(\u_cordic/[1].U/n231_1_1 ),
	.SUM(\u_cordic/[1].U/n231_2 )
);
defparam \u_cordic/[1].U/n231_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n230_1_s  (
	.I0(\u_cordic/z[1] [2]),
	.I1(GND),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n231_1_1 ),
	.COUT(\u_cordic/[1].U/n230_1_1 ),
	.SUM(\u_cordic/[1].U/n230_2 )
);
defparam \u_cordic/[1].U/n230_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n229_1_s  (
	.I0(\u_cordic/z[1] [3]),
	.I1(VCC),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n230_1_1 ),
	.COUT(\u_cordic/[1].U/n229_1_1 ),
	.SUM(\u_cordic/[1].U/n229_2 )
);
defparam \u_cordic/[1].U/n229_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n228_1_s  (
	.I0(\u_cordic/z[1] [4]),
	.I1(VCC),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n229_1_1 ),
	.COUT(\u_cordic/[1].U/n228_1_1 ),
	.SUM(\u_cordic/[1].U/n228_2 )
);
defparam \u_cordic/[1].U/n228_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n227_1_s  (
	.I0(\u_cordic/z[1] [5]),
	.I1(GND),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n228_1_1 ),
	.COUT(\u_cordic/[1].U/n227_1_1 ),
	.SUM(\u_cordic/[1].U/n227_2 )
);
defparam \u_cordic/[1].U/n227_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n226_1_s  (
	.I0(\u_cordic/z[1] [6]),
	.I1(VCC),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n227_1_1 ),
	.COUT(\u_cordic/[1].U/n226_1_1 ),
	.SUM(\u_cordic/[1].U/n226_2 )
);
defparam \u_cordic/[1].U/n226_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n225_1_s  (
	.I0(\u_cordic/z[1] [7]),
	.I1(GND),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n226_1_1 ),
	.COUT(\u_cordic/[1].U/n225_1_1 ),
	.SUM(\u_cordic/[1].U/n225_2 )
);
defparam \u_cordic/[1].U/n225_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n224_1_s  (
	.I0(\u_cordic/z[1] [8]),
	.I1(VCC),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n225_1_1 ),
	.COUT(\u_cordic/[1].U/n224_1_1 ),
	.SUM(\u_cordic/[1].U/n224_2 )
);
defparam \u_cordic/[1].U/n224_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n223_1_s  (
	.I0(\u_cordic/z[1] [9]),
	.I1(VCC),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n224_1_1 ),
	.COUT(\u_cordic/[1].U/n223_1_1 ),
	.SUM(\u_cordic/[1].U/n223_2 )
);
defparam \u_cordic/[1].U/n223_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n222_1_s  (
	.I0(\u_cordic/z[1] [10]),
	.I1(GND),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n223_1_1 ),
	.COUT(\u_cordic/[1].U/n222_1_1 ),
	.SUM(\u_cordic/[1].U/n222_2 )
);
defparam \u_cordic/[1].U/n222_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n221_1_s  (
	.I0(\u_cordic/z[1] [11]),
	.I1(VCC),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n222_1_1 ),
	.COUT(\u_cordic/[1].U/n221_1_1 ),
	.SUM(\u_cordic/[1].U/n221_2 )
);
defparam \u_cordic/[1].U/n221_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n220_1_s  (
	.I0(\u_cordic/z[1] [12]),
	.I1(VCC),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n221_1_1 ),
	.COUT(\u_cordic/[1].U/n220_1_1 ),
	.SUM(\u_cordic/[1].U/n220_2 )
);
defparam \u_cordic/[1].U/n220_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n219_1_s  (
	.I0(\u_cordic/z[1] [13]),
	.I1(VCC),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n220_1_1 ),
	.COUT(\u_cordic/[1].U/n219_1_1 ),
	.SUM(\u_cordic/[1].U/n219_2 )
);
defparam \u_cordic/[1].U/n219_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n218_1_s  (
	.I0(\u_cordic/z[1] [14]),
	.I1(GND),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n219_1_1 ),
	.COUT(\u_cordic/[1].U/n218_1_1 ),
	.SUM(\u_cordic/[1].U/n218_2 )
);
defparam \u_cordic/[1].U/n218_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n217_1_s  (
	.I0(\u_cordic/z[1] [15]),
	.I1(GND),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n218_1_1 ),
	.COUT(\u_cordic/[1].U/n217_1_1 ),
	.SUM(\u_cordic/[1].U/n217_2 )
);
defparam \u_cordic/[1].U/n217_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n216_1_s  (
	.I0(\u_cordic/z[1] [16]),
	.I1(GND),
	.I3(\u_cordic/z[1] [16]),
	.CIN(\u_cordic/[1].U/n217_1_1 ),
	.COUT(\u_cordic/[1].U/n216_1_0_COUT ),
	.SUM(\u_cordic/[1].U/n216_2 )
);
defparam \u_cordic/[1].U/n216_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n215_1_s  (
	.I0(\u_cordic/y[1] [0]),
	.I1(\u_cordic/x[1] [1]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/z[1] [16]),
	.COUT(\u_cordic/[1].U/n215_1_1 ),
	.SUM(\u_cordic/[1].U/n215_2 )
);
defparam \u_cordic/[1].U/n215_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n214_1_s  (
	.I0(\u_cordic/y[1] [1]),
	.I1(\u_cordic/x[1] [2]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n215_1_1 ),
	.COUT(\u_cordic/[1].U/n214_1_1 ),
	.SUM(\u_cordic/[1].U/n214_2 )
);
defparam \u_cordic/[1].U/n214_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n213_1_s  (
	.I0(\u_cordic/y[1] [2]),
	.I1(\u_cordic/x[1] [3]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n214_1_1 ),
	.COUT(\u_cordic/[1].U/n213_1_1 ),
	.SUM(\u_cordic/[1].U/n213_2 )
);
defparam \u_cordic/[1].U/n213_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n212_1_s  (
	.I0(\u_cordic/y[1] [3]),
	.I1(\u_cordic/x[1] [4]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n213_1_1 ),
	.COUT(\u_cordic/[1].U/n212_1_1 ),
	.SUM(\u_cordic/[1].U/n212_2 )
);
defparam \u_cordic/[1].U/n212_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n211_1_s  (
	.I0(\u_cordic/y[1] [4]),
	.I1(\u_cordic/x[1] [5]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n212_1_1 ),
	.COUT(\u_cordic/[1].U/n211_1_1 ),
	.SUM(\u_cordic/[1].U/n211_2 )
);
defparam \u_cordic/[1].U/n211_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n210_1_s  (
	.I0(\u_cordic/y[1] [5]),
	.I1(\u_cordic/x[1] [6]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n211_1_1 ),
	.COUT(\u_cordic/[1].U/n210_1_1 ),
	.SUM(\u_cordic/[1].U/n210_2 )
);
defparam \u_cordic/[1].U/n210_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n209_1_s  (
	.I0(\u_cordic/y[1] [6]),
	.I1(\u_cordic/x[1] [7]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n210_1_1 ),
	.COUT(\u_cordic/[1].U/n209_1_1 ),
	.SUM(\u_cordic/[1].U/n209_2 )
);
defparam \u_cordic/[1].U/n209_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n208_1_s  (
	.I0(\u_cordic/y[1] [7]),
	.I1(\u_cordic/x[1] [8]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n209_1_1 ),
	.COUT(\u_cordic/[1].U/n208_1_1 ),
	.SUM(\u_cordic/[1].U/n208_2 )
);
defparam \u_cordic/[1].U/n208_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n207_1_s  (
	.I0(\u_cordic/y[1] [8]),
	.I1(\u_cordic/x[1] [9]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n208_1_1 ),
	.COUT(\u_cordic/[1].U/n207_1_1 ),
	.SUM(\u_cordic/[1].U/n207_2 )
);
defparam \u_cordic/[1].U/n207_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n206_1_s  (
	.I0(\u_cordic/y[1] [9]),
	.I1(\u_cordic/x[1] [10]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n207_1_1 ),
	.COUT(\u_cordic/[1].U/n206_1_1 ),
	.SUM(\u_cordic/[1].U/n206_2 )
);
defparam \u_cordic/[1].U/n206_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n205_1_s  (
	.I0(\u_cordic/y[1] [10]),
	.I1(\u_cordic/x[1] [11]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n206_1_1 ),
	.COUT(\u_cordic/[1].U/n205_1_1 ),
	.SUM(\u_cordic/[1].U/n205_2 )
);
defparam \u_cordic/[1].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n204_1_s  (
	.I0(\u_cordic/y[1] [11]),
	.I1(\u_cordic/x[1] [12]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n205_1_1 ),
	.COUT(\u_cordic/[1].U/n204_1_1 ),
	.SUM(\u_cordic/[1].U/n204_2 )
);
defparam \u_cordic/[1].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n203_1_s  (
	.I0(\u_cordic/y[1] [12]),
	.I1(\u_cordic/x[1] [13]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n204_1_1 ),
	.COUT(\u_cordic/[1].U/n203_1_1 ),
	.SUM(\u_cordic/[1].U/n203_2 )
);
defparam \u_cordic/[1].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n202_1_s  (
	.I0(\u_cordic/y[1] [13]),
	.I1(\u_cordic/x[1] [14]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n203_1_1 ),
	.COUT(\u_cordic/[1].U/n202_1_1 ),
	.SUM(\u_cordic/[1].U/n202_2 )
);
defparam \u_cordic/[1].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n201_1_s  (
	.I0(\u_cordic/y[1] [14]),
	.I1(\u_cordic/x[1] [15]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n202_1_1 ),
	.COUT(\u_cordic/[1].U/n201_1_1 ),
	.SUM(\u_cordic/[1].U/n201_2 )
);
defparam \u_cordic/[1].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n200_1_s  (
	.I0(\u_cordic/y[1] [15]),
	.I1(\u_cordic/x[1] [16]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n201_1_1 ),
	.COUT(\u_cordic/[1].U/n200_1_1 ),
	.SUM(\u_cordic/[1].U/n200_2 )
);
defparam \u_cordic/[1].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[1].U/n199_1_s  (
	.I0(\u_cordic/y[1] [16]),
	.I1(\u_cordic/x[1] [16]),
	.I3(\u_cordic/z[1][16]_1_5 ),
	.CIN(\u_cordic/[1].U/n200_1_1 ),
	.COUT(\u_cordic/[1].U/n199_1_0_COUT ),
	.SUM(\u_cordic/[1].U/n199_2 )
);
defparam \u_cordic/[1].U/n199_1_s .ALU_MODE=2;
LUT1 \u_cordic/[1].U/z[2][16]_1_s3  (
	.I0(\u_cordic/z[2] [16]),
	.F(\u_cordic/z[2][16]_1_5 )
);
defparam \u_cordic/[1].U/z[2][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[2].U/x_1_15_s0  (
	.D(\u_cordic/[2].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [15])
);
defparam \u_cordic/[2].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_14_s0  (
	.D(\u_cordic/[2].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [14])
);
defparam \u_cordic/[2].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_13_s0  (
	.D(\u_cordic/[2].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [13])
);
defparam \u_cordic/[2].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_12_s0  (
	.D(\u_cordic/[2].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [12])
);
defparam \u_cordic/[2].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_11_s0  (
	.D(\u_cordic/[2].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [11])
);
defparam \u_cordic/[2].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_10_s0  (
	.D(\u_cordic/[2].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [10])
);
defparam \u_cordic/[2].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_9_s0  (
	.D(\u_cordic/[2].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [9])
);
defparam \u_cordic/[2].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_8_s0  (
	.D(\u_cordic/[2].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [8])
);
defparam \u_cordic/[2].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_7_s0  (
	.D(\u_cordic/[2].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [7])
);
defparam \u_cordic/[2].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_6_s0  (
	.D(\u_cordic/[2].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [6])
);
defparam \u_cordic/[2].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_5_s0  (
	.D(\u_cordic/[2].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [5])
);
defparam \u_cordic/[2].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_4_s0  (
	.D(\u_cordic/[2].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [4])
);
defparam \u_cordic/[2].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_3_s0  (
	.D(\u_cordic/[2].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [3])
);
defparam \u_cordic/[2].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_2_s0  (
	.D(\u_cordic/[2].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [2])
);
defparam \u_cordic/[2].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_1_s0  (
	.D(\u_cordic/[2].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [1])
);
defparam \u_cordic/[2].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_0_s0  (
	.D(\u_cordic/[2].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [0])
);
defparam \u_cordic/[2].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_16_s0  (
	.D(\u_cordic/[2].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [16])
);
defparam \u_cordic/[2].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_15_s0  (
	.D(\u_cordic/[2].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [15])
);
defparam \u_cordic/[2].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_14_s0  (
	.D(\u_cordic/[2].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [14])
);
defparam \u_cordic/[2].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_13_s0  (
	.D(\u_cordic/[2].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [13])
);
defparam \u_cordic/[2].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_12_s0  (
	.D(\u_cordic/[2].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [12])
);
defparam \u_cordic/[2].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_11_s0  (
	.D(\u_cordic/[2].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [11])
);
defparam \u_cordic/[2].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_10_s0  (
	.D(\u_cordic/[2].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [10])
);
defparam \u_cordic/[2].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_9_s0  (
	.D(\u_cordic/[2].U/n206_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [9])
);
defparam \u_cordic/[2].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_8_s0  (
	.D(\u_cordic/[2].U/n207_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [8])
);
defparam \u_cordic/[2].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_7_s0  (
	.D(\u_cordic/[2].U/n208_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [7])
);
defparam \u_cordic/[2].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_6_s0  (
	.D(\u_cordic/[2].U/n209_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [6])
);
defparam \u_cordic/[2].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_5_s0  (
	.D(\u_cordic/[2].U/n210_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [5])
);
defparam \u_cordic/[2].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_4_s0  (
	.D(\u_cordic/[2].U/n211_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [4])
);
defparam \u_cordic/[2].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_3_s0  (
	.D(\u_cordic/[2].U/n212_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [3])
);
defparam \u_cordic/[2].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_2_s0  (
	.D(\u_cordic/[2].U/n213_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [2])
);
defparam \u_cordic/[2].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_1_s0  (
	.D(\u_cordic/[2].U/n214_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [1])
);
defparam \u_cordic/[2].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/y_1_0_s0  (
	.D(\u_cordic/[2].U/n215_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[3] [0])
);
defparam \u_cordic/[2].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_16_s0  (
	.D(\u_cordic/[2].U/n216_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [16])
);
defparam \u_cordic/[2].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_15_s0  (
	.D(\u_cordic/[2].U/n217_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [15])
);
defparam \u_cordic/[2].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_14_s0  (
	.D(\u_cordic/[2].U/n218_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [14])
);
defparam \u_cordic/[2].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_13_s0  (
	.D(\u_cordic/[2].U/n219_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [13])
);
defparam \u_cordic/[2].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_12_s0  (
	.D(\u_cordic/[2].U/n220_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [12])
);
defparam \u_cordic/[2].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_11_s0  (
	.D(\u_cordic/[2].U/n221_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [11])
);
defparam \u_cordic/[2].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_10_s0  (
	.D(\u_cordic/[2].U/n222_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [10])
);
defparam \u_cordic/[2].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_9_s0  (
	.D(\u_cordic/[2].U/n223_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [9])
);
defparam \u_cordic/[2].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_8_s0  (
	.D(\u_cordic/[2].U/n224_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [8])
);
defparam \u_cordic/[2].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_7_s0  (
	.D(\u_cordic/[2].U/n225_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [7])
);
defparam \u_cordic/[2].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_6_s0  (
	.D(\u_cordic/[2].U/n226_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [6])
);
defparam \u_cordic/[2].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_5_s0  (
	.D(\u_cordic/[2].U/n227_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [5])
);
defparam \u_cordic/[2].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_4_s0  (
	.D(\u_cordic/[2].U/n228_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [4])
);
defparam \u_cordic/[2].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_3_s0  (
	.D(\u_cordic/[2].U/n229_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [3])
);
defparam \u_cordic/[2].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_2_s0  (
	.D(\u_cordic/[2].U/n230_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [2])
);
defparam \u_cordic/[2].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_1_s0  (
	.D(\u_cordic/[2].U/n231_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [1])
);
defparam \u_cordic/[2].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/z_1_0_s0  (
	.D(\u_cordic/[2].U/n232_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[3] [0])
);
defparam \u_cordic/[2].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[2].U/x_1_16_s0  (
	.D(\u_cordic/[2].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[3] [16])
);
defparam \u_cordic/[2].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[2].U/n198_1_s  (
	.I0(\u_cordic/x[2] [0]),
	.I1(\u_cordic/y[2] [2]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/z[2][16]_1_5 ),
	.COUT(\u_cordic/[2].U/n198_1_1 ),
	.SUM(\u_cordic/[2].U/n198_2 )
);
defparam \u_cordic/[2].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n197_1_s  (
	.I0(\u_cordic/x[2] [1]),
	.I1(\u_cordic/y[2] [3]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n198_1_1 ),
	.COUT(\u_cordic/[2].U/n197_1_1 ),
	.SUM(\u_cordic/[2].U/n197_2 )
);
defparam \u_cordic/[2].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n196_1_s  (
	.I0(\u_cordic/x[2] [2]),
	.I1(\u_cordic/y[2] [4]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n197_1_1 ),
	.COUT(\u_cordic/[2].U/n196_1_1 ),
	.SUM(\u_cordic/[2].U/n196_2 )
);
defparam \u_cordic/[2].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n195_1_s  (
	.I0(\u_cordic/x[2] [3]),
	.I1(\u_cordic/y[2] [5]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n196_1_1 ),
	.COUT(\u_cordic/[2].U/n195_1_1 ),
	.SUM(\u_cordic/[2].U/n195_2 )
);
defparam \u_cordic/[2].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n194_1_s  (
	.I0(\u_cordic/x[2] [4]),
	.I1(\u_cordic/y[2] [6]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n195_1_1 ),
	.COUT(\u_cordic/[2].U/n194_1_1 ),
	.SUM(\u_cordic/[2].U/n194_2 )
);
defparam \u_cordic/[2].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n193_1_s  (
	.I0(\u_cordic/x[2] [5]),
	.I1(\u_cordic/y[2] [7]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n194_1_1 ),
	.COUT(\u_cordic/[2].U/n193_1_1 ),
	.SUM(\u_cordic/[2].U/n193_2 )
);
defparam \u_cordic/[2].U/n193_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n192_1_s  (
	.I0(\u_cordic/x[2] [6]),
	.I1(\u_cordic/y[2] [8]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n193_1_1 ),
	.COUT(\u_cordic/[2].U/n192_1_1 ),
	.SUM(\u_cordic/[2].U/n192_2 )
);
defparam \u_cordic/[2].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n191_1_s  (
	.I0(\u_cordic/x[2] [7]),
	.I1(\u_cordic/y[2] [9]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n192_1_1 ),
	.COUT(\u_cordic/[2].U/n191_1_1 ),
	.SUM(\u_cordic/[2].U/n191_2 )
);
defparam \u_cordic/[2].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n190_1_s  (
	.I0(\u_cordic/x[2] [8]),
	.I1(\u_cordic/y[2] [10]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n191_1_1 ),
	.COUT(\u_cordic/[2].U/n190_1_1 ),
	.SUM(\u_cordic/[2].U/n190_2 )
);
defparam \u_cordic/[2].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n189_1_s  (
	.I0(\u_cordic/x[2] [9]),
	.I1(\u_cordic/y[2] [11]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n190_1_1 ),
	.COUT(\u_cordic/[2].U/n189_1_1 ),
	.SUM(\u_cordic/[2].U/n189_2 )
);
defparam \u_cordic/[2].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n188_1_s  (
	.I0(\u_cordic/x[2] [10]),
	.I1(\u_cordic/y[2] [12]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n189_1_1 ),
	.COUT(\u_cordic/[2].U/n188_1_1 ),
	.SUM(\u_cordic/[2].U/n188_2 )
);
defparam \u_cordic/[2].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n187_1_s  (
	.I0(\u_cordic/x[2] [11]),
	.I1(\u_cordic/y[2] [13]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n188_1_1 ),
	.COUT(\u_cordic/[2].U/n187_1_1 ),
	.SUM(\u_cordic/[2].U/n187_2 )
);
defparam \u_cordic/[2].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n186_1_s  (
	.I0(\u_cordic/x[2] [12]),
	.I1(\u_cordic/y[2] [14]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n187_1_1 ),
	.COUT(\u_cordic/[2].U/n186_1_1 ),
	.SUM(\u_cordic/[2].U/n186_2 )
);
defparam \u_cordic/[2].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n185_1_s  (
	.I0(\u_cordic/x[2] [13]),
	.I1(\u_cordic/y[2] [15]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n186_1_1 ),
	.COUT(\u_cordic/[2].U/n185_1_1 ),
	.SUM(\u_cordic/[2].U/n185_2 )
);
defparam \u_cordic/[2].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n184_1_s  (
	.I0(\u_cordic/x[2] [14]),
	.I1(\u_cordic/y[2] [16]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n185_1_1 ),
	.COUT(\u_cordic/[2].U/n184_1_1 ),
	.SUM(\u_cordic/[2].U/n184_2 )
);
defparam \u_cordic/[2].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n183_1_s  (
	.I0(\u_cordic/x[2] [15]),
	.I1(\u_cordic/y[2] [16]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n184_1_1 ),
	.COUT(\u_cordic/[2].U/n183_1_1 ),
	.SUM(\u_cordic/[2].U/n183_2 )
);
defparam \u_cordic/[2].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n182_1_s  (
	.I0(\u_cordic/x[2] [16]),
	.I1(\u_cordic/y[2] [16]),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n183_1_1 ),
	.COUT(\u_cordic/[2].U/n182_1_0_COUT ),
	.SUM(\u_cordic/[2].U/n182_2 )
);
defparam \u_cordic/[2].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n232_1_s  (
	.I0(\u_cordic/z[2] [0]),
	.I1(VCC),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/z[2][16]_1_5 ),
	.COUT(\u_cordic/[2].U/n232_1_1 ),
	.SUM(\u_cordic/[2].U/n232_2 )
);
defparam \u_cordic/[2].U/n232_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n231_1_s  (
	.I0(\u_cordic/z[2] [1]),
	.I1(VCC),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n232_1_1 ),
	.COUT(\u_cordic/[2].U/n231_1_1 ),
	.SUM(\u_cordic/[2].U/n231_2 )
);
defparam \u_cordic/[2].U/n231_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n230_1_s  (
	.I0(\u_cordic/z[2] [2]),
	.I1(GND),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n231_1_1 ),
	.COUT(\u_cordic/[2].U/n230_1_1 ),
	.SUM(\u_cordic/[2].U/n230_2 )
);
defparam \u_cordic/[2].U/n230_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n229_1_s  (
	.I0(\u_cordic/z[2] [3]),
	.I1(VCC),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n230_1_1 ),
	.COUT(\u_cordic/[2].U/n229_1_1 ),
	.SUM(\u_cordic/[2].U/n229_2 )
);
defparam \u_cordic/[2].U/n229_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n228_1_s  (
	.I0(\u_cordic/z[2] [4]),
	.I1(VCC),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n229_1_1 ),
	.COUT(\u_cordic/[2].U/n228_1_1 ),
	.SUM(\u_cordic/[2].U/n228_2 )
);
defparam \u_cordic/[2].U/n228_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n227_1_s  (
	.I0(\u_cordic/z[2] [5]),
	.I1(GND),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n228_1_1 ),
	.COUT(\u_cordic/[2].U/n227_1_1 ),
	.SUM(\u_cordic/[2].U/n227_2 )
);
defparam \u_cordic/[2].U/n227_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n226_1_s  (
	.I0(\u_cordic/z[2] [6]),
	.I1(VCC),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n227_1_1 ),
	.COUT(\u_cordic/[2].U/n226_1_1 ),
	.SUM(\u_cordic/[2].U/n226_2 )
);
defparam \u_cordic/[2].U/n226_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n225_1_s  (
	.I0(\u_cordic/z[2] [7]),
	.I1(GND),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n226_1_1 ),
	.COUT(\u_cordic/[2].U/n225_1_1 ),
	.SUM(\u_cordic/[2].U/n225_2 )
);
defparam \u_cordic/[2].U/n225_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n224_1_s  (
	.I0(\u_cordic/z[2] [8]),
	.I1(VCC),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n225_1_1 ),
	.COUT(\u_cordic/[2].U/n224_1_1 ),
	.SUM(\u_cordic/[2].U/n224_2 )
);
defparam \u_cordic/[2].U/n224_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n223_1_s  (
	.I0(\u_cordic/z[2] [9]),
	.I1(VCC),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n224_1_1 ),
	.COUT(\u_cordic/[2].U/n223_1_1 ),
	.SUM(\u_cordic/[2].U/n223_2 )
);
defparam \u_cordic/[2].U/n223_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n222_1_s  (
	.I0(\u_cordic/z[2] [10]),
	.I1(VCC),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n223_1_1 ),
	.COUT(\u_cordic/[2].U/n222_1_1 ),
	.SUM(\u_cordic/[2].U/n222_2 )
);
defparam \u_cordic/[2].U/n222_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n221_1_s  (
	.I0(\u_cordic/z[2] [11]),
	.I1(VCC),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n222_1_1 ),
	.COUT(\u_cordic/[2].U/n221_1_1 ),
	.SUM(\u_cordic/[2].U/n221_2 )
);
defparam \u_cordic/[2].U/n221_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n220_1_s  (
	.I0(\u_cordic/z[2] [12]),
	.I1(VCC),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n221_1_1 ),
	.COUT(\u_cordic/[2].U/n220_1_1 ),
	.SUM(\u_cordic/[2].U/n220_2 )
);
defparam \u_cordic/[2].U/n220_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n219_1_s  (
	.I0(\u_cordic/z[2] [13]),
	.I1(GND),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n220_1_1 ),
	.COUT(\u_cordic/[2].U/n219_1_1 ),
	.SUM(\u_cordic/[2].U/n219_2 )
);
defparam \u_cordic/[2].U/n219_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n218_1_s  (
	.I0(\u_cordic/z[2] [14]),
	.I1(GND),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n219_1_1 ),
	.COUT(\u_cordic/[2].U/n218_1_1 ),
	.SUM(\u_cordic/[2].U/n218_2 )
);
defparam \u_cordic/[2].U/n218_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n217_1_s  (
	.I0(\u_cordic/z[2] [15]),
	.I1(GND),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n218_1_1 ),
	.COUT(\u_cordic/[2].U/n217_1_1 ),
	.SUM(\u_cordic/[2].U/n217_2 )
);
defparam \u_cordic/[2].U/n217_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n216_1_s  (
	.I0(\u_cordic/z[2] [16]),
	.I1(GND),
	.I3(\u_cordic/z[2] [16]),
	.CIN(\u_cordic/[2].U/n217_1_1 ),
	.COUT(\u_cordic/[2].U/n216_1_0_COUT ),
	.SUM(\u_cordic/[2].U/n216_2 )
);
defparam \u_cordic/[2].U/n216_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n215_1_s  (
	.I0(\u_cordic/y[2] [0]),
	.I1(\u_cordic/x[2] [2]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/z[2] [16]),
	.COUT(\u_cordic/[2].U/n215_1_1 ),
	.SUM(\u_cordic/[2].U/n215_2 )
);
defparam \u_cordic/[2].U/n215_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n214_1_s  (
	.I0(\u_cordic/y[2] [1]),
	.I1(\u_cordic/x[2] [3]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n215_1_1 ),
	.COUT(\u_cordic/[2].U/n214_1_1 ),
	.SUM(\u_cordic/[2].U/n214_2 )
);
defparam \u_cordic/[2].U/n214_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n213_1_s  (
	.I0(\u_cordic/y[2] [2]),
	.I1(\u_cordic/x[2] [4]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n214_1_1 ),
	.COUT(\u_cordic/[2].U/n213_1_1 ),
	.SUM(\u_cordic/[2].U/n213_2 )
);
defparam \u_cordic/[2].U/n213_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n212_1_s  (
	.I0(\u_cordic/y[2] [3]),
	.I1(\u_cordic/x[2] [5]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n213_1_1 ),
	.COUT(\u_cordic/[2].U/n212_1_1 ),
	.SUM(\u_cordic/[2].U/n212_2 )
);
defparam \u_cordic/[2].U/n212_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n211_1_s  (
	.I0(\u_cordic/y[2] [4]),
	.I1(\u_cordic/x[2] [6]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n212_1_1 ),
	.COUT(\u_cordic/[2].U/n211_1_1 ),
	.SUM(\u_cordic/[2].U/n211_2 )
);
defparam \u_cordic/[2].U/n211_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n210_1_s  (
	.I0(\u_cordic/y[2] [5]),
	.I1(\u_cordic/x[2] [7]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n211_1_1 ),
	.COUT(\u_cordic/[2].U/n210_1_1 ),
	.SUM(\u_cordic/[2].U/n210_2 )
);
defparam \u_cordic/[2].U/n210_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n209_1_s  (
	.I0(\u_cordic/y[2] [6]),
	.I1(\u_cordic/x[2] [8]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n210_1_1 ),
	.COUT(\u_cordic/[2].U/n209_1_1 ),
	.SUM(\u_cordic/[2].U/n209_2 )
);
defparam \u_cordic/[2].U/n209_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n208_1_s  (
	.I0(\u_cordic/y[2] [7]),
	.I1(\u_cordic/x[2] [9]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n209_1_1 ),
	.COUT(\u_cordic/[2].U/n208_1_1 ),
	.SUM(\u_cordic/[2].U/n208_2 )
);
defparam \u_cordic/[2].U/n208_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n207_1_s  (
	.I0(\u_cordic/y[2] [8]),
	.I1(\u_cordic/x[2] [10]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n208_1_1 ),
	.COUT(\u_cordic/[2].U/n207_1_1 ),
	.SUM(\u_cordic/[2].U/n207_2 )
);
defparam \u_cordic/[2].U/n207_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n206_1_s  (
	.I0(\u_cordic/y[2] [9]),
	.I1(\u_cordic/x[2] [11]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n207_1_1 ),
	.COUT(\u_cordic/[2].U/n206_1_1 ),
	.SUM(\u_cordic/[2].U/n206_2 )
);
defparam \u_cordic/[2].U/n206_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n205_1_s  (
	.I0(\u_cordic/y[2] [10]),
	.I1(\u_cordic/x[2] [12]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n206_1_1 ),
	.COUT(\u_cordic/[2].U/n205_1_1 ),
	.SUM(\u_cordic/[2].U/n205_2 )
);
defparam \u_cordic/[2].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n204_1_s  (
	.I0(\u_cordic/y[2] [11]),
	.I1(\u_cordic/x[2] [13]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n205_1_1 ),
	.COUT(\u_cordic/[2].U/n204_1_1 ),
	.SUM(\u_cordic/[2].U/n204_2 )
);
defparam \u_cordic/[2].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n203_1_s  (
	.I0(\u_cordic/y[2] [12]),
	.I1(\u_cordic/x[2] [14]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n204_1_1 ),
	.COUT(\u_cordic/[2].U/n203_1_1 ),
	.SUM(\u_cordic/[2].U/n203_2 )
);
defparam \u_cordic/[2].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n202_1_s  (
	.I0(\u_cordic/y[2] [13]),
	.I1(\u_cordic/x[2] [15]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n203_1_1 ),
	.COUT(\u_cordic/[2].U/n202_1_1 ),
	.SUM(\u_cordic/[2].U/n202_2 )
);
defparam \u_cordic/[2].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n201_1_s  (
	.I0(\u_cordic/y[2] [14]),
	.I1(\u_cordic/x[2] [16]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n202_1_1 ),
	.COUT(\u_cordic/[2].U/n201_1_1 ),
	.SUM(\u_cordic/[2].U/n201_2 )
);
defparam \u_cordic/[2].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n200_1_s  (
	.I0(\u_cordic/y[2] [15]),
	.I1(\u_cordic/x[2] [16]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n201_1_1 ),
	.COUT(\u_cordic/[2].U/n200_1_1 ),
	.SUM(\u_cordic/[2].U/n200_2 )
);
defparam \u_cordic/[2].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[2].U/n199_1_s  (
	.I0(\u_cordic/y[2] [16]),
	.I1(\u_cordic/x[2] [16]),
	.I3(\u_cordic/z[2][16]_1_5 ),
	.CIN(\u_cordic/[2].U/n200_1_1 ),
	.COUT(\u_cordic/[2].U/n199_1_0_COUT ),
	.SUM(\u_cordic/[2].U/n199_2 )
);
defparam \u_cordic/[2].U/n199_1_s .ALU_MODE=2;
LUT1 \u_cordic/[2].U/z[3][16]_1_s3  (
	.I0(\u_cordic/z[3] [16]),
	.F(\u_cordic/z[3][16]_1_5 )
);
defparam \u_cordic/[2].U/z[3][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[3].U/x_1_15_s0  (
	.D(\u_cordic/[3].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [15])
);
defparam \u_cordic/[3].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_14_s0  (
	.D(\u_cordic/[3].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [14])
);
defparam \u_cordic/[3].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_13_s0  (
	.D(\u_cordic/[3].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [13])
);
defparam \u_cordic/[3].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_12_s0  (
	.D(\u_cordic/[3].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [12])
);
defparam \u_cordic/[3].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_11_s0  (
	.D(\u_cordic/[3].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [11])
);
defparam \u_cordic/[3].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_10_s0  (
	.D(\u_cordic/[3].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [10])
);
defparam \u_cordic/[3].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_9_s0  (
	.D(\u_cordic/[3].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [9])
);
defparam \u_cordic/[3].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_8_s0  (
	.D(\u_cordic/[3].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [8])
);
defparam \u_cordic/[3].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_7_s0  (
	.D(\u_cordic/[3].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [7])
);
defparam \u_cordic/[3].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_6_s0  (
	.D(\u_cordic/[3].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [6])
);
defparam \u_cordic/[3].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_5_s0  (
	.D(\u_cordic/[3].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [5])
);
defparam \u_cordic/[3].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_4_s0  (
	.D(\u_cordic/[3].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [4])
);
defparam \u_cordic/[3].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_3_s0  (
	.D(\u_cordic/[3].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [3])
);
defparam \u_cordic/[3].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_2_s0  (
	.D(\u_cordic/[3].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [2])
);
defparam \u_cordic/[3].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_1_s0  (
	.D(\u_cordic/[3].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [1])
);
defparam \u_cordic/[3].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_0_s0  (
	.D(\u_cordic/[3].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [0])
);
defparam \u_cordic/[3].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_16_s0  (
	.D(\u_cordic/[3].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [16])
);
defparam \u_cordic/[3].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_15_s0  (
	.D(\u_cordic/[3].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [15])
);
defparam \u_cordic/[3].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_14_s0  (
	.D(\u_cordic/[3].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [14])
);
defparam \u_cordic/[3].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_13_s0  (
	.D(\u_cordic/[3].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [13])
);
defparam \u_cordic/[3].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_12_s0  (
	.D(\u_cordic/[3].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [12])
);
defparam \u_cordic/[3].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_11_s0  (
	.D(\u_cordic/[3].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [11])
);
defparam \u_cordic/[3].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_10_s0  (
	.D(\u_cordic/[3].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [10])
);
defparam \u_cordic/[3].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_9_s0  (
	.D(\u_cordic/[3].U/n206_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [9])
);
defparam \u_cordic/[3].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_8_s0  (
	.D(\u_cordic/[3].U/n207_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [8])
);
defparam \u_cordic/[3].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_7_s0  (
	.D(\u_cordic/[3].U/n208_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [7])
);
defparam \u_cordic/[3].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_6_s0  (
	.D(\u_cordic/[3].U/n209_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [6])
);
defparam \u_cordic/[3].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_5_s0  (
	.D(\u_cordic/[3].U/n210_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [5])
);
defparam \u_cordic/[3].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_4_s0  (
	.D(\u_cordic/[3].U/n211_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [4])
);
defparam \u_cordic/[3].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_3_s0  (
	.D(\u_cordic/[3].U/n212_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [3])
);
defparam \u_cordic/[3].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_2_s0  (
	.D(\u_cordic/[3].U/n213_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [2])
);
defparam \u_cordic/[3].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_1_s0  (
	.D(\u_cordic/[3].U/n214_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [1])
);
defparam \u_cordic/[3].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/y_1_0_s0  (
	.D(\u_cordic/[3].U/n215_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[4] [0])
);
defparam \u_cordic/[3].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_16_s0  (
	.D(\u_cordic/[3].U/n216_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [16])
);
defparam \u_cordic/[3].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_15_s0  (
	.D(\u_cordic/[3].U/n217_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [15])
);
defparam \u_cordic/[3].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_14_s0  (
	.D(\u_cordic/[3].U/n218_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [14])
);
defparam \u_cordic/[3].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_13_s0  (
	.D(\u_cordic/[3].U/n219_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [13])
);
defparam \u_cordic/[3].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_12_s0  (
	.D(\u_cordic/[3].U/n220_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [12])
);
defparam \u_cordic/[3].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_11_s0  (
	.D(\u_cordic/[3].U/n221_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [11])
);
defparam \u_cordic/[3].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_10_s0  (
	.D(\u_cordic/[3].U/n222_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [10])
);
defparam \u_cordic/[3].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_9_s0  (
	.D(\u_cordic/[3].U/n223_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [9])
);
defparam \u_cordic/[3].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_8_s0  (
	.D(\u_cordic/[3].U/n224_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [8])
);
defparam \u_cordic/[3].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_7_s0  (
	.D(\u_cordic/[3].U/n225_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [7])
);
defparam \u_cordic/[3].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_6_s0  (
	.D(\u_cordic/[3].U/n226_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [6])
);
defparam \u_cordic/[3].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_5_s0  (
	.D(\u_cordic/[3].U/n227_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [5])
);
defparam \u_cordic/[3].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_4_s0  (
	.D(\u_cordic/[3].U/n228_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [4])
);
defparam \u_cordic/[3].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_3_s0  (
	.D(\u_cordic/[3].U/n229_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [3])
);
defparam \u_cordic/[3].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_2_s0  (
	.D(\u_cordic/[3].U/n230_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [2])
);
defparam \u_cordic/[3].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_1_s0  (
	.D(\u_cordic/[3].U/n231_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [1])
);
defparam \u_cordic/[3].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/z_1_0_s0  (
	.D(\u_cordic/[3].U/n232_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[4] [0])
);
defparam \u_cordic/[3].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[3].U/x_1_16_s0  (
	.D(\u_cordic/[3].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[4] [16])
);
defparam \u_cordic/[3].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[3].U/n198_1_s  (
	.I0(\u_cordic/x[3] [0]),
	.I1(\u_cordic/y[3] [3]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/z[3][16]_1_5 ),
	.COUT(\u_cordic/[3].U/n198_1_1 ),
	.SUM(\u_cordic/[3].U/n198_2 )
);
defparam \u_cordic/[3].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n197_1_s  (
	.I0(\u_cordic/x[3] [1]),
	.I1(\u_cordic/y[3] [4]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n198_1_1 ),
	.COUT(\u_cordic/[3].U/n197_1_1 ),
	.SUM(\u_cordic/[3].U/n197_2 )
);
defparam \u_cordic/[3].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n196_1_s  (
	.I0(\u_cordic/x[3] [2]),
	.I1(\u_cordic/y[3] [5]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n197_1_1 ),
	.COUT(\u_cordic/[3].U/n196_1_1 ),
	.SUM(\u_cordic/[3].U/n196_2 )
);
defparam \u_cordic/[3].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n195_1_s  (
	.I0(\u_cordic/x[3] [3]),
	.I1(\u_cordic/y[3] [6]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n196_1_1 ),
	.COUT(\u_cordic/[3].U/n195_1_1 ),
	.SUM(\u_cordic/[3].U/n195_2 )
);
defparam \u_cordic/[3].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n194_1_s  (
	.I0(\u_cordic/x[3] [4]),
	.I1(\u_cordic/y[3] [7]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n195_1_1 ),
	.COUT(\u_cordic/[3].U/n194_1_1 ),
	.SUM(\u_cordic/[3].U/n194_2 )
);
defparam \u_cordic/[3].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n193_1_s  (
	.I0(\u_cordic/x[3] [5]),
	.I1(\u_cordic/y[3] [8]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n194_1_1 ),
	.COUT(\u_cordic/[3].U/n193_1_1 ),
	.SUM(\u_cordic/[3].U/n193_2 )
);
defparam \u_cordic/[3].U/n193_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n192_1_s  (
	.I0(\u_cordic/x[3] [6]),
	.I1(\u_cordic/y[3] [9]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n193_1_1 ),
	.COUT(\u_cordic/[3].U/n192_1_1 ),
	.SUM(\u_cordic/[3].U/n192_2 )
);
defparam \u_cordic/[3].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n191_1_s  (
	.I0(\u_cordic/x[3] [7]),
	.I1(\u_cordic/y[3] [10]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n192_1_1 ),
	.COUT(\u_cordic/[3].U/n191_1_1 ),
	.SUM(\u_cordic/[3].U/n191_2 )
);
defparam \u_cordic/[3].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n190_1_s  (
	.I0(\u_cordic/x[3] [8]),
	.I1(\u_cordic/y[3] [11]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n191_1_1 ),
	.COUT(\u_cordic/[3].U/n190_1_1 ),
	.SUM(\u_cordic/[3].U/n190_2 )
);
defparam \u_cordic/[3].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n189_1_s  (
	.I0(\u_cordic/x[3] [9]),
	.I1(\u_cordic/y[3] [12]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n190_1_1 ),
	.COUT(\u_cordic/[3].U/n189_1_1 ),
	.SUM(\u_cordic/[3].U/n189_2 )
);
defparam \u_cordic/[3].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n188_1_s  (
	.I0(\u_cordic/x[3] [10]),
	.I1(\u_cordic/y[3] [13]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n189_1_1 ),
	.COUT(\u_cordic/[3].U/n188_1_1 ),
	.SUM(\u_cordic/[3].U/n188_2 )
);
defparam \u_cordic/[3].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n187_1_s  (
	.I0(\u_cordic/x[3] [11]),
	.I1(\u_cordic/y[3] [14]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n188_1_1 ),
	.COUT(\u_cordic/[3].U/n187_1_1 ),
	.SUM(\u_cordic/[3].U/n187_2 )
);
defparam \u_cordic/[3].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n186_1_s  (
	.I0(\u_cordic/x[3] [12]),
	.I1(\u_cordic/y[3] [15]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n187_1_1 ),
	.COUT(\u_cordic/[3].U/n186_1_1 ),
	.SUM(\u_cordic/[3].U/n186_2 )
);
defparam \u_cordic/[3].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n185_1_s  (
	.I0(\u_cordic/x[3] [13]),
	.I1(\u_cordic/y[3] [16]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n186_1_1 ),
	.COUT(\u_cordic/[3].U/n185_1_1 ),
	.SUM(\u_cordic/[3].U/n185_2 )
);
defparam \u_cordic/[3].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n184_1_s  (
	.I0(\u_cordic/x[3] [14]),
	.I1(\u_cordic/y[3] [16]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n185_1_1 ),
	.COUT(\u_cordic/[3].U/n184_1_1 ),
	.SUM(\u_cordic/[3].U/n184_2 )
);
defparam \u_cordic/[3].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n183_1_s  (
	.I0(\u_cordic/x[3] [15]),
	.I1(\u_cordic/y[3] [16]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n184_1_1 ),
	.COUT(\u_cordic/[3].U/n183_1_1 ),
	.SUM(\u_cordic/[3].U/n183_2 )
);
defparam \u_cordic/[3].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n182_1_s  (
	.I0(\u_cordic/x[3] [16]),
	.I1(\u_cordic/y[3] [16]),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n183_1_1 ),
	.COUT(\u_cordic/[3].U/n182_1_0_COUT ),
	.SUM(\u_cordic/[3].U/n182_2 )
);
defparam \u_cordic/[3].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n232_1_s  (
	.I0(\u_cordic/z[3] [0]),
	.I1(VCC),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/z[3][16]_1_5 ),
	.COUT(\u_cordic/[3].U/n232_1_1 ),
	.SUM(\u_cordic/[3].U/n232_2 )
);
defparam \u_cordic/[3].U/n232_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n231_1_s  (
	.I0(\u_cordic/z[3] [1]),
	.I1(VCC),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n232_1_1 ),
	.COUT(\u_cordic/[3].U/n231_1_1 ),
	.SUM(\u_cordic/[3].U/n231_2 )
);
defparam \u_cordic/[3].U/n231_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n230_1_s  (
	.I0(\u_cordic/z[3] [2]),
	.I1(GND),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n231_1_1 ),
	.COUT(\u_cordic/[3].U/n230_1_1 ),
	.SUM(\u_cordic/[3].U/n230_2 )
);
defparam \u_cordic/[3].U/n230_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n229_1_s  (
	.I0(\u_cordic/z[3] [3]),
	.I1(VCC),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n230_1_1 ),
	.COUT(\u_cordic/[3].U/n229_1_1 ),
	.SUM(\u_cordic/[3].U/n229_2 )
);
defparam \u_cordic/[3].U/n229_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n228_1_s  (
	.I0(\u_cordic/z[3] [4]),
	.I1(GND),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n229_1_1 ),
	.COUT(\u_cordic/[3].U/n228_1_1 ),
	.SUM(\u_cordic/[3].U/n228_2 )
);
defparam \u_cordic/[3].U/n228_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n227_1_s  (
	.I0(\u_cordic/z[3] [5]),
	.I1(VCC),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n228_1_1 ),
	.COUT(\u_cordic/[3].U/n227_1_1 ),
	.SUM(\u_cordic/[3].U/n227_2 )
);
defparam \u_cordic/[3].U/n227_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n226_1_s  (
	.I0(\u_cordic/z[3] [6]),
	.I1(VCC),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n227_1_1 ),
	.COUT(\u_cordic/[3].U/n226_1_1 ),
	.SUM(\u_cordic/[3].U/n226_2 )
);
defparam \u_cordic/[3].U/n226_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n225_1_s  (
	.I0(\u_cordic/z[3] [7]),
	.I1(VCC),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n226_1_1 ),
	.COUT(\u_cordic/[3].U/n225_1_1 ),
	.SUM(\u_cordic/[3].U/n225_2 )
);
defparam \u_cordic/[3].U/n225_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n224_1_s  (
	.I0(\u_cordic/z[3] [8]),
	.I1(VCC),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n225_1_1 ),
	.COUT(\u_cordic/[3].U/n224_1_1 ),
	.SUM(\u_cordic/[3].U/n224_2 )
);
defparam \u_cordic/[3].U/n224_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n223_1_s  (
	.I0(\u_cordic/z[3] [9]),
	.I1(VCC),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n224_1_1 ),
	.COUT(\u_cordic/[3].U/n223_1_1 ),
	.SUM(\u_cordic/[3].U/n223_2 )
);
defparam \u_cordic/[3].U/n223_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n222_1_s  (
	.I0(\u_cordic/z[3] [10]),
	.I1(VCC),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n223_1_1 ),
	.COUT(\u_cordic/[3].U/n222_1_1 ),
	.SUM(\u_cordic/[3].U/n222_2 )
);
defparam \u_cordic/[3].U/n222_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n221_1_s  (
	.I0(\u_cordic/z[3] [11]),
	.I1(VCC),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n222_1_1 ),
	.COUT(\u_cordic/[3].U/n221_1_1 ),
	.SUM(\u_cordic/[3].U/n221_2 )
);
defparam \u_cordic/[3].U/n221_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n220_1_s  (
	.I0(\u_cordic/z[3] [12]),
	.I1(GND),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n221_1_1 ),
	.COUT(\u_cordic/[3].U/n220_1_1 ),
	.SUM(\u_cordic/[3].U/n220_2 )
);
defparam \u_cordic/[3].U/n220_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n219_1_s  (
	.I0(\u_cordic/z[3] [13]),
	.I1(GND),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n220_1_1 ),
	.COUT(\u_cordic/[3].U/n219_1_1 ),
	.SUM(\u_cordic/[3].U/n219_2 )
);
defparam \u_cordic/[3].U/n219_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n218_1_s  (
	.I0(\u_cordic/z[3] [14]),
	.I1(GND),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n219_1_1 ),
	.COUT(\u_cordic/[3].U/n218_1_1 ),
	.SUM(\u_cordic/[3].U/n218_2 )
);
defparam \u_cordic/[3].U/n218_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n217_1_s  (
	.I0(\u_cordic/z[3] [15]),
	.I1(GND),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n218_1_1 ),
	.COUT(\u_cordic/[3].U/n217_1_1 ),
	.SUM(\u_cordic/[3].U/n217_2 )
);
defparam \u_cordic/[3].U/n217_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n216_1_s  (
	.I0(\u_cordic/z[3] [16]),
	.I1(GND),
	.I3(\u_cordic/z[3] [16]),
	.CIN(\u_cordic/[3].U/n217_1_1 ),
	.COUT(\u_cordic/[3].U/n216_1_0_COUT ),
	.SUM(\u_cordic/[3].U/n216_2 )
);
defparam \u_cordic/[3].U/n216_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n215_1_s  (
	.I0(\u_cordic/y[3] [0]),
	.I1(\u_cordic/x[3] [3]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/z[3] [16]),
	.COUT(\u_cordic/[3].U/n215_1_1 ),
	.SUM(\u_cordic/[3].U/n215_2 )
);
defparam \u_cordic/[3].U/n215_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n214_1_s  (
	.I0(\u_cordic/y[3] [1]),
	.I1(\u_cordic/x[3] [4]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n215_1_1 ),
	.COUT(\u_cordic/[3].U/n214_1_1 ),
	.SUM(\u_cordic/[3].U/n214_2 )
);
defparam \u_cordic/[3].U/n214_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n213_1_s  (
	.I0(\u_cordic/y[3] [2]),
	.I1(\u_cordic/x[3] [5]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n214_1_1 ),
	.COUT(\u_cordic/[3].U/n213_1_1 ),
	.SUM(\u_cordic/[3].U/n213_2 )
);
defparam \u_cordic/[3].U/n213_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n212_1_s  (
	.I0(\u_cordic/y[3] [3]),
	.I1(\u_cordic/x[3] [6]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n213_1_1 ),
	.COUT(\u_cordic/[3].U/n212_1_1 ),
	.SUM(\u_cordic/[3].U/n212_2 )
);
defparam \u_cordic/[3].U/n212_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n211_1_s  (
	.I0(\u_cordic/y[3] [4]),
	.I1(\u_cordic/x[3] [7]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n212_1_1 ),
	.COUT(\u_cordic/[3].U/n211_1_1 ),
	.SUM(\u_cordic/[3].U/n211_2 )
);
defparam \u_cordic/[3].U/n211_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n210_1_s  (
	.I0(\u_cordic/y[3] [5]),
	.I1(\u_cordic/x[3] [8]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n211_1_1 ),
	.COUT(\u_cordic/[3].U/n210_1_1 ),
	.SUM(\u_cordic/[3].U/n210_2 )
);
defparam \u_cordic/[3].U/n210_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n209_1_s  (
	.I0(\u_cordic/y[3] [6]),
	.I1(\u_cordic/x[3] [9]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n210_1_1 ),
	.COUT(\u_cordic/[3].U/n209_1_1 ),
	.SUM(\u_cordic/[3].U/n209_2 )
);
defparam \u_cordic/[3].U/n209_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n208_1_s  (
	.I0(\u_cordic/y[3] [7]),
	.I1(\u_cordic/x[3] [10]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n209_1_1 ),
	.COUT(\u_cordic/[3].U/n208_1_1 ),
	.SUM(\u_cordic/[3].U/n208_2 )
);
defparam \u_cordic/[3].U/n208_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n207_1_s  (
	.I0(\u_cordic/y[3] [8]),
	.I1(\u_cordic/x[3] [11]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n208_1_1 ),
	.COUT(\u_cordic/[3].U/n207_1_1 ),
	.SUM(\u_cordic/[3].U/n207_2 )
);
defparam \u_cordic/[3].U/n207_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n206_1_s  (
	.I0(\u_cordic/y[3] [9]),
	.I1(\u_cordic/x[3] [12]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n207_1_1 ),
	.COUT(\u_cordic/[3].U/n206_1_1 ),
	.SUM(\u_cordic/[3].U/n206_2 )
);
defparam \u_cordic/[3].U/n206_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n205_1_s  (
	.I0(\u_cordic/y[3] [10]),
	.I1(\u_cordic/x[3] [13]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n206_1_1 ),
	.COUT(\u_cordic/[3].U/n205_1_1 ),
	.SUM(\u_cordic/[3].U/n205_2 )
);
defparam \u_cordic/[3].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n204_1_s  (
	.I0(\u_cordic/y[3] [11]),
	.I1(\u_cordic/x[3] [14]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n205_1_1 ),
	.COUT(\u_cordic/[3].U/n204_1_1 ),
	.SUM(\u_cordic/[3].U/n204_2 )
);
defparam \u_cordic/[3].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n203_1_s  (
	.I0(\u_cordic/y[3] [12]),
	.I1(\u_cordic/x[3] [15]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n204_1_1 ),
	.COUT(\u_cordic/[3].U/n203_1_1 ),
	.SUM(\u_cordic/[3].U/n203_2 )
);
defparam \u_cordic/[3].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n202_1_s  (
	.I0(\u_cordic/y[3] [13]),
	.I1(\u_cordic/x[3] [16]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n203_1_1 ),
	.COUT(\u_cordic/[3].U/n202_1_1 ),
	.SUM(\u_cordic/[3].U/n202_2 )
);
defparam \u_cordic/[3].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n201_1_s  (
	.I0(\u_cordic/y[3] [14]),
	.I1(\u_cordic/x[3] [16]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n202_1_1 ),
	.COUT(\u_cordic/[3].U/n201_1_1 ),
	.SUM(\u_cordic/[3].U/n201_2 )
);
defparam \u_cordic/[3].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n200_1_s  (
	.I0(\u_cordic/y[3] [15]),
	.I1(\u_cordic/x[3] [16]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n201_1_1 ),
	.COUT(\u_cordic/[3].U/n200_1_1 ),
	.SUM(\u_cordic/[3].U/n200_2 )
);
defparam \u_cordic/[3].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[3].U/n199_1_s  (
	.I0(\u_cordic/y[3] [16]),
	.I1(\u_cordic/x[3] [16]),
	.I3(\u_cordic/z[3][16]_1_5 ),
	.CIN(\u_cordic/[3].U/n200_1_1 ),
	.COUT(\u_cordic/[3].U/n199_1_0_COUT ),
	.SUM(\u_cordic/[3].U/n199_2 )
);
defparam \u_cordic/[3].U/n199_1_s .ALU_MODE=2;
LUT1 \u_cordic/[3].U/z[4][16]_1_s3  (
	.I0(\u_cordic/z[4] [16]),
	.F(\u_cordic/z[4][16]_1_5 )
);
defparam \u_cordic/[3].U/z[4][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[4].U/x_1_15_s0  (
	.D(\u_cordic/[4].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [15])
);
defparam \u_cordic/[4].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_14_s0  (
	.D(\u_cordic/[4].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [14])
);
defparam \u_cordic/[4].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_13_s0  (
	.D(\u_cordic/[4].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [13])
);
defparam \u_cordic/[4].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_12_s0  (
	.D(\u_cordic/[4].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [12])
);
defparam \u_cordic/[4].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_11_s0  (
	.D(\u_cordic/[4].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [11])
);
defparam \u_cordic/[4].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_10_s0  (
	.D(\u_cordic/[4].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [10])
);
defparam \u_cordic/[4].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_9_s0  (
	.D(\u_cordic/[4].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [9])
);
defparam \u_cordic/[4].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_8_s0  (
	.D(\u_cordic/[4].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [8])
);
defparam \u_cordic/[4].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_7_s0  (
	.D(\u_cordic/[4].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [7])
);
defparam \u_cordic/[4].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_6_s0  (
	.D(\u_cordic/[4].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [6])
);
defparam \u_cordic/[4].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_5_s0  (
	.D(\u_cordic/[4].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [5])
);
defparam \u_cordic/[4].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_4_s0  (
	.D(\u_cordic/[4].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [4])
);
defparam \u_cordic/[4].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_3_s0  (
	.D(\u_cordic/[4].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [3])
);
defparam \u_cordic/[4].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_2_s0  (
	.D(\u_cordic/[4].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [2])
);
defparam \u_cordic/[4].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_1_s0  (
	.D(\u_cordic/[4].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [1])
);
defparam \u_cordic/[4].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_0_s0  (
	.D(\u_cordic/[4].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [0])
);
defparam \u_cordic/[4].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_16_s0  (
	.D(\u_cordic/[4].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [16])
);
defparam \u_cordic/[4].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_15_s0  (
	.D(\u_cordic/[4].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [15])
);
defparam \u_cordic/[4].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_14_s0  (
	.D(\u_cordic/[4].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [14])
);
defparam \u_cordic/[4].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_13_s0  (
	.D(\u_cordic/[4].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [13])
);
defparam \u_cordic/[4].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_12_s0  (
	.D(\u_cordic/[4].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [12])
);
defparam \u_cordic/[4].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_11_s0  (
	.D(\u_cordic/[4].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [11])
);
defparam \u_cordic/[4].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_10_s0  (
	.D(\u_cordic/[4].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [10])
);
defparam \u_cordic/[4].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_9_s0  (
	.D(\u_cordic/[4].U/n206_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [9])
);
defparam \u_cordic/[4].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_8_s0  (
	.D(\u_cordic/[4].U/n207_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [8])
);
defparam \u_cordic/[4].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_7_s0  (
	.D(\u_cordic/[4].U/n208_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [7])
);
defparam \u_cordic/[4].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_6_s0  (
	.D(\u_cordic/[4].U/n209_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [6])
);
defparam \u_cordic/[4].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_5_s0  (
	.D(\u_cordic/[4].U/n210_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [5])
);
defparam \u_cordic/[4].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_4_s0  (
	.D(\u_cordic/[4].U/n211_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [4])
);
defparam \u_cordic/[4].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_3_s0  (
	.D(\u_cordic/[4].U/n212_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [3])
);
defparam \u_cordic/[4].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_2_s0  (
	.D(\u_cordic/[4].U/n213_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [2])
);
defparam \u_cordic/[4].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_1_s0  (
	.D(\u_cordic/[4].U/n214_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [1])
);
defparam \u_cordic/[4].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/y_1_0_s0  (
	.D(\u_cordic/[4].U/n215_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[5] [0])
);
defparam \u_cordic/[4].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_16_s0  (
	.D(\u_cordic/[4].U/n216_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [16])
);
defparam \u_cordic/[4].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_15_s0  (
	.D(\u_cordic/[4].U/n217_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [15])
);
defparam \u_cordic/[4].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_14_s0  (
	.D(\u_cordic/[4].U/n218_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [14])
);
defparam \u_cordic/[4].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_13_s0  (
	.D(\u_cordic/[4].U/n219_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [13])
);
defparam \u_cordic/[4].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_12_s0  (
	.D(\u_cordic/[4].U/n220_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [12])
);
defparam \u_cordic/[4].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_11_s0  (
	.D(\u_cordic/[4].U/n221_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [11])
);
defparam \u_cordic/[4].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_10_s0  (
	.D(\u_cordic/[4].U/n222_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [10])
);
defparam \u_cordic/[4].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_9_s0  (
	.D(\u_cordic/[4].U/n223_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [9])
);
defparam \u_cordic/[4].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_8_s0  (
	.D(\u_cordic/[4].U/n224_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [8])
);
defparam \u_cordic/[4].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_7_s0  (
	.D(\u_cordic/[4].U/n225_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [7])
);
defparam \u_cordic/[4].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_6_s0  (
	.D(\u_cordic/[4].U/n226_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [6])
);
defparam \u_cordic/[4].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_5_s0  (
	.D(\u_cordic/[4].U/n227_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [5])
);
defparam \u_cordic/[4].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_4_s0  (
	.D(\u_cordic/[4].U/n228_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [4])
);
defparam \u_cordic/[4].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_3_s0  (
	.D(\u_cordic/[4].U/n229_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [3])
);
defparam \u_cordic/[4].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_2_s0  (
	.D(\u_cordic/[4].U/n230_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [2])
);
defparam \u_cordic/[4].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_1_s0  (
	.D(\u_cordic/[4].U/n231_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [1])
);
defparam \u_cordic/[4].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/z_1_0_s0  (
	.D(\u_cordic/[4].U/n232_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[5] [0])
);
defparam \u_cordic/[4].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[4].U/x_1_16_s0  (
	.D(\u_cordic/[4].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[5] [16])
);
defparam \u_cordic/[4].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[4].U/n198_1_s  (
	.I0(\u_cordic/x[4] [0]),
	.I1(\u_cordic/y[4] [4]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/z[4][16]_1_5 ),
	.COUT(\u_cordic/[4].U/n198_1_1 ),
	.SUM(\u_cordic/[4].U/n198_2 )
);
defparam \u_cordic/[4].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n197_1_s  (
	.I0(\u_cordic/x[4] [1]),
	.I1(\u_cordic/y[4] [5]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n198_1_1 ),
	.COUT(\u_cordic/[4].U/n197_1_1 ),
	.SUM(\u_cordic/[4].U/n197_2 )
);
defparam \u_cordic/[4].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n196_1_s  (
	.I0(\u_cordic/x[4] [2]),
	.I1(\u_cordic/y[4] [6]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n197_1_1 ),
	.COUT(\u_cordic/[4].U/n196_1_1 ),
	.SUM(\u_cordic/[4].U/n196_2 )
);
defparam \u_cordic/[4].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n195_1_s  (
	.I0(\u_cordic/x[4] [3]),
	.I1(\u_cordic/y[4] [7]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n196_1_1 ),
	.COUT(\u_cordic/[4].U/n195_1_1 ),
	.SUM(\u_cordic/[4].U/n195_2 )
);
defparam \u_cordic/[4].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n194_1_s  (
	.I0(\u_cordic/x[4] [4]),
	.I1(\u_cordic/y[4] [8]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n195_1_1 ),
	.COUT(\u_cordic/[4].U/n194_1_1 ),
	.SUM(\u_cordic/[4].U/n194_2 )
);
defparam \u_cordic/[4].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n193_1_s  (
	.I0(\u_cordic/x[4] [5]),
	.I1(\u_cordic/y[4] [9]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n194_1_1 ),
	.COUT(\u_cordic/[4].U/n193_1_1 ),
	.SUM(\u_cordic/[4].U/n193_2 )
);
defparam \u_cordic/[4].U/n193_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n192_1_s  (
	.I0(\u_cordic/x[4] [6]),
	.I1(\u_cordic/y[4] [10]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n193_1_1 ),
	.COUT(\u_cordic/[4].U/n192_1_1 ),
	.SUM(\u_cordic/[4].U/n192_2 )
);
defparam \u_cordic/[4].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n191_1_s  (
	.I0(\u_cordic/x[4] [7]),
	.I1(\u_cordic/y[4] [11]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n192_1_1 ),
	.COUT(\u_cordic/[4].U/n191_1_1 ),
	.SUM(\u_cordic/[4].U/n191_2 )
);
defparam \u_cordic/[4].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n190_1_s  (
	.I0(\u_cordic/x[4] [8]),
	.I1(\u_cordic/y[4] [12]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n191_1_1 ),
	.COUT(\u_cordic/[4].U/n190_1_1 ),
	.SUM(\u_cordic/[4].U/n190_2 )
);
defparam \u_cordic/[4].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n189_1_s  (
	.I0(\u_cordic/x[4] [9]),
	.I1(\u_cordic/y[4] [13]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n190_1_1 ),
	.COUT(\u_cordic/[4].U/n189_1_1 ),
	.SUM(\u_cordic/[4].U/n189_2 )
);
defparam \u_cordic/[4].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n188_1_s  (
	.I0(\u_cordic/x[4] [10]),
	.I1(\u_cordic/y[4] [14]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n189_1_1 ),
	.COUT(\u_cordic/[4].U/n188_1_1 ),
	.SUM(\u_cordic/[4].U/n188_2 )
);
defparam \u_cordic/[4].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n187_1_s  (
	.I0(\u_cordic/x[4] [11]),
	.I1(\u_cordic/y[4] [15]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n188_1_1 ),
	.COUT(\u_cordic/[4].U/n187_1_1 ),
	.SUM(\u_cordic/[4].U/n187_2 )
);
defparam \u_cordic/[4].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n186_1_s  (
	.I0(\u_cordic/x[4] [12]),
	.I1(\u_cordic/y[4] [16]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n187_1_1 ),
	.COUT(\u_cordic/[4].U/n186_1_1 ),
	.SUM(\u_cordic/[4].U/n186_2 )
);
defparam \u_cordic/[4].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n185_1_s  (
	.I0(\u_cordic/x[4] [13]),
	.I1(\u_cordic/y[4] [16]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n186_1_1 ),
	.COUT(\u_cordic/[4].U/n185_1_1 ),
	.SUM(\u_cordic/[4].U/n185_2 )
);
defparam \u_cordic/[4].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n184_1_s  (
	.I0(\u_cordic/x[4] [14]),
	.I1(\u_cordic/y[4] [16]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n185_1_1 ),
	.COUT(\u_cordic/[4].U/n184_1_1 ),
	.SUM(\u_cordic/[4].U/n184_2 )
);
defparam \u_cordic/[4].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n183_1_s  (
	.I0(\u_cordic/x[4] [15]),
	.I1(\u_cordic/y[4] [16]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n184_1_1 ),
	.COUT(\u_cordic/[4].U/n183_1_1 ),
	.SUM(\u_cordic/[4].U/n183_2 )
);
defparam \u_cordic/[4].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n182_1_s  (
	.I0(\u_cordic/x[4] [16]),
	.I1(\u_cordic/y[4] [16]),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n183_1_1 ),
	.COUT(\u_cordic/[4].U/n182_1_0_COUT ),
	.SUM(\u_cordic/[4].U/n182_2 )
);
defparam \u_cordic/[4].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n232_1_s  (
	.I0(\u_cordic/z[4] [0]),
	.I1(VCC),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/z[4][16]_1_5 ),
	.COUT(\u_cordic/[4].U/n232_1_1 ),
	.SUM(\u_cordic/[4].U/n232_2 )
);
defparam \u_cordic/[4].U/n232_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n231_1_s  (
	.I0(\u_cordic/z[4] [1]),
	.I1(GND),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n232_1_1 ),
	.COUT(\u_cordic/[4].U/n231_1_1 ),
	.SUM(\u_cordic/[4].U/n231_2 )
);
defparam \u_cordic/[4].U/n231_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n230_1_s  (
	.I0(\u_cordic/z[4] [2]),
	.I1(VCC),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n231_1_1 ),
	.COUT(\u_cordic/[4].U/n230_1_1 ),
	.SUM(\u_cordic/[4].U/n230_2 )
);
defparam \u_cordic/[4].U/n230_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n229_1_s  (
	.I0(\u_cordic/z[4] [3]),
	.I1(VCC),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n230_1_1 ),
	.COUT(\u_cordic/[4].U/n229_1_1 ),
	.SUM(\u_cordic/[4].U/n229_2 )
);
defparam \u_cordic/[4].U/n229_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n228_1_s  (
	.I0(\u_cordic/z[4] [4]),
	.I1(VCC),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n229_1_1 ),
	.COUT(\u_cordic/[4].U/n228_1_1 ),
	.SUM(\u_cordic/[4].U/n228_2 )
);
defparam \u_cordic/[4].U/n228_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n227_1_s  (
	.I0(\u_cordic/z[4] [5]),
	.I1(VCC),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n228_1_1 ),
	.COUT(\u_cordic/[4].U/n227_1_1 ),
	.SUM(\u_cordic/[4].U/n227_2 )
);
defparam \u_cordic/[4].U/n227_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n226_1_s  (
	.I0(\u_cordic/z[4] [6]),
	.I1(VCC),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n227_1_1 ),
	.COUT(\u_cordic/[4].U/n226_1_1 ),
	.SUM(\u_cordic/[4].U/n226_2 )
);
defparam \u_cordic/[4].U/n226_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n225_1_s  (
	.I0(\u_cordic/z[4] [7]),
	.I1(VCC),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n226_1_1 ),
	.COUT(\u_cordic/[4].U/n225_1_1 ),
	.SUM(\u_cordic/[4].U/n225_2 )
);
defparam \u_cordic/[4].U/n225_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n224_1_s  (
	.I0(\u_cordic/z[4] [8]),
	.I1(VCC),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n225_1_1 ),
	.COUT(\u_cordic/[4].U/n224_1_1 ),
	.SUM(\u_cordic/[4].U/n224_2 )
);
defparam \u_cordic/[4].U/n224_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n223_1_s  (
	.I0(\u_cordic/z[4] [9]),
	.I1(VCC),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n224_1_1 ),
	.COUT(\u_cordic/[4].U/n223_1_1 ),
	.SUM(\u_cordic/[4].U/n223_2 )
);
defparam \u_cordic/[4].U/n223_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n222_1_s  (
	.I0(\u_cordic/z[4] [10]),
	.I1(VCC),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n223_1_1 ),
	.COUT(\u_cordic/[4].U/n222_1_1 ),
	.SUM(\u_cordic/[4].U/n222_2 )
);
defparam \u_cordic/[4].U/n222_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n221_1_s  (
	.I0(\u_cordic/z[4] [11]),
	.I1(GND),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n222_1_1 ),
	.COUT(\u_cordic/[4].U/n221_1_1 ),
	.SUM(\u_cordic/[4].U/n221_2 )
);
defparam \u_cordic/[4].U/n221_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n220_1_s  (
	.I0(\u_cordic/z[4] [12]),
	.I1(GND),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n221_1_1 ),
	.COUT(\u_cordic/[4].U/n220_1_1 ),
	.SUM(\u_cordic/[4].U/n220_2 )
);
defparam \u_cordic/[4].U/n220_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n219_1_s  (
	.I0(\u_cordic/z[4] [13]),
	.I1(GND),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n220_1_1 ),
	.COUT(\u_cordic/[4].U/n219_1_1 ),
	.SUM(\u_cordic/[4].U/n219_2 )
);
defparam \u_cordic/[4].U/n219_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n218_1_s  (
	.I0(\u_cordic/z[4] [14]),
	.I1(GND),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n219_1_1 ),
	.COUT(\u_cordic/[4].U/n218_1_1 ),
	.SUM(\u_cordic/[4].U/n218_2 )
);
defparam \u_cordic/[4].U/n218_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n217_1_s  (
	.I0(\u_cordic/z[4] [15]),
	.I1(GND),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n218_1_1 ),
	.COUT(\u_cordic/[4].U/n217_1_1 ),
	.SUM(\u_cordic/[4].U/n217_2 )
);
defparam \u_cordic/[4].U/n217_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n216_1_s  (
	.I0(\u_cordic/z[4] [16]),
	.I1(GND),
	.I3(\u_cordic/z[4] [16]),
	.CIN(\u_cordic/[4].U/n217_1_1 ),
	.COUT(\u_cordic/[4].U/n216_1_0_COUT ),
	.SUM(\u_cordic/[4].U/n216_2 )
);
defparam \u_cordic/[4].U/n216_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n215_1_s  (
	.I0(\u_cordic/y[4] [0]),
	.I1(\u_cordic/x[4] [4]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/z[4] [16]),
	.COUT(\u_cordic/[4].U/n215_1_1 ),
	.SUM(\u_cordic/[4].U/n215_2 )
);
defparam \u_cordic/[4].U/n215_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n214_1_s  (
	.I0(\u_cordic/y[4] [1]),
	.I1(\u_cordic/x[4] [5]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n215_1_1 ),
	.COUT(\u_cordic/[4].U/n214_1_1 ),
	.SUM(\u_cordic/[4].U/n214_2 )
);
defparam \u_cordic/[4].U/n214_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n213_1_s  (
	.I0(\u_cordic/y[4] [2]),
	.I1(\u_cordic/x[4] [6]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n214_1_1 ),
	.COUT(\u_cordic/[4].U/n213_1_1 ),
	.SUM(\u_cordic/[4].U/n213_2 )
);
defparam \u_cordic/[4].U/n213_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n212_1_s  (
	.I0(\u_cordic/y[4] [3]),
	.I1(\u_cordic/x[4] [7]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n213_1_1 ),
	.COUT(\u_cordic/[4].U/n212_1_1 ),
	.SUM(\u_cordic/[4].U/n212_2 )
);
defparam \u_cordic/[4].U/n212_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n211_1_s  (
	.I0(\u_cordic/y[4] [4]),
	.I1(\u_cordic/x[4] [8]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n212_1_1 ),
	.COUT(\u_cordic/[4].U/n211_1_1 ),
	.SUM(\u_cordic/[4].U/n211_2 )
);
defparam \u_cordic/[4].U/n211_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n210_1_s  (
	.I0(\u_cordic/y[4] [5]),
	.I1(\u_cordic/x[4] [9]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n211_1_1 ),
	.COUT(\u_cordic/[4].U/n210_1_1 ),
	.SUM(\u_cordic/[4].U/n210_2 )
);
defparam \u_cordic/[4].U/n210_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n209_1_s  (
	.I0(\u_cordic/y[4] [6]),
	.I1(\u_cordic/x[4] [10]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n210_1_1 ),
	.COUT(\u_cordic/[4].U/n209_1_1 ),
	.SUM(\u_cordic/[4].U/n209_2 )
);
defparam \u_cordic/[4].U/n209_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n208_1_s  (
	.I0(\u_cordic/y[4] [7]),
	.I1(\u_cordic/x[4] [11]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n209_1_1 ),
	.COUT(\u_cordic/[4].U/n208_1_1 ),
	.SUM(\u_cordic/[4].U/n208_2 )
);
defparam \u_cordic/[4].U/n208_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n207_1_s  (
	.I0(\u_cordic/y[4] [8]),
	.I1(\u_cordic/x[4] [12]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n208_1_1 ),
	.COUT(\u_cordic/[4].U/n207_1_1 ),
	.SUM(\u_cordic/[4].U/n207_2 )
);
defparam \u_cordic/[4].U/n207_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n206_1_s  (
	.I0(\u_cordic/y[4] [9]),
	.I1(\u_cordic/x[4] [13]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n207_1_1 ),
	.COUT(\u_cordic/[4].U/n206_1_1 ),
	.SUM(\u_cordic/[4].U/n206_2 )
);
defparam \u_cordic/[4].U/n206_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n205_1_s  (
	.I0(\u_cordic/y[4] [10]),
	.I1(\u_cordic/x[4] [14]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n206_1_1 ),
	.COUT(\u_cordic/[4].U/n205_1_1 ),
	.SUM(\u_cordic/[4].U/n205_2 )
);
defparam \u_cordic/[4].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n204_1_s  (
	.I0(\u_cordic/y[4] [11]),
	.I1(\u_cordic/x[4] [15]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n205_1_1 ),
	.COUT(\u_cordic/[4].U/n204_1_1 ),
	.SUM(\u_cordic/[4].U/n204_2 )
);
defparam \u_cordic/[4].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n203_1_s  (
	.I0(\u_cordic/y[4] [12]),
	.I1(\u_cordic/x[4] [16]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n204_1_1 ),
	.COUT(\u_cordic/[4].U/n203_1_1 ),
	.SUM(\u_cordic/[4].U/n203_2 )
);
defparam \u_cordic/[4].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n202_1_s  (
	.I0(\u_cordic/y[4] [13]),
	.I1(\u_cordic/x[4] [16]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n203_1_1 ),
	.COUT(\u_cordic/[4].U/n202_1_1 ),
	.SUM(\u_cordic/[4].U/n202_2 )
);
defparam \u_cordic/[4].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n201_1_s  (
	.I0(\u_cordic/y[4] [14]),
	.I1(\u_cordic/x[4] [16]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n202_1_1 ),
	.COUT(\u_cordic/[4].U/n201_1_1 ),
	.SUM(\u_cordic/[4].U/n201_2 )
);
defparam \u_cordic/[4].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n200_1_s  (
	.I0(\u_cordic/y[4] [15]),
	.I1(\u_cordic/x[4] [16]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n201_1_1 ),
	.COUT(\u_cordic/[4].U/n200_1_1 ),
	.SUM(\u_cordic/[4].U/n200_2 )
);
defparam \u_cordic/[4].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[4].U/n199_1_s  (
	.I0(\u_cordic/y[4] [16]),
	.I1(\u_cordic/x[4] [16]),
	.I3(\u_cordic/z[4][16]_1_5 ),
	.CIN(\u_cordic/[4].U/n200_1_1 ),
	.COUT(\u_cordic/[4].U/n199_1_0_COUT ),
	.SUM(\u_cordic/[4].U/n199_2 )
);
defparam \u_cordic/[4].U/n199_1_s .ALU_MODE=2;
LUT1 \u_cordic/[4].U/z[5][16]_1_s3  (
	.I0(\u_cordic/z[5] [16]),
	.F(\u_cordic/z[5][16]_1_5 )
);
defparam \u_cordic/[4].U/z[5][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[5].U/x_1_15_s0  (
	.D(\u_cordic/[5].U/n163_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [15])
);
defparam \u_cordic/[5].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_14_s0  (
	.D(\u_cordic/[5].U/n164_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [14])
);
defparam \u_cordic/[5].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_13_s0  (
	.D(\u_cordic/[5].U/n165_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [13])
);
defparam \u_cordic/[5].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_12_s0  (
	.D(\u_cordic/[5].U/n166_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [12])
);
defparam \u_cordic/[5].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_11_s0  (
	.D(\u_cordic/[5].U/n167_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [11])
);
defparam \u_cordic/[5].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_10_s0  (
	.D(\u_cordic/[5].U/n168_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [10])
);
defparam \u_cordic/[5].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_9_s0  (
	.D(\u_cordic/[5].U/n169_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [9])
);
defparam \u_cordic/[5].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_8_s0  (
	.D(\u_cordic/[5].U/n170_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [8])
);
defparam \u_cordic/[5].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_7_s0  (
	.D(\u_cordic/[5].U/n171_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [7])
);
defparam \u_cordic/[5].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_6_s0  (
	.D(\u_cordic/[5].U/n172_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [6])
);
defparam \u_cordic/[5].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_5_s0  (
	.D(\u_cordic/[5].U/n173_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [5])
);
defparam \u_cordic/[5].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_4_s0  (
	.D(\u_cordic/[5].U/n174_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [4])
);
defparam \u_cordic/[5].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_3_s0  (
	.D(\u_cordic/[5].U/n175_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [3])
);
defparam \u_cordic/[5].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_2_s0  (
	.D(\u_cordic/[5].U/n176_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [2])
);
defparam \u_cordic/[5].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_1_s0  (
	.D(\u_cordic/[5].U/n177_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [1])
);
defparam \u_cordic/[5].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_0_s0  (
	.D(\u_cordic/[5].U/n178_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [0])
);
defparam \u_cordic/[5].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_16_s0  (
	.D(\u_cordic/[5].U/n179_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [16])
);
defparam \u_cordic/[5].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_15_s0  (
	.D(\u_cordic/[5].U/n180_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [15])
);
defparam \u_cordic/[5].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_14_s0  (
	.D(\u_cordic/[5].U/n181_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [14])
);
defparam \u_cordic/[5].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_13_s0  (
	.D(\u_cordic/[5].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [13])
);
defparam \u_cordic/[5].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_12_s0  (
	.D(\u_cordic/[5].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [12])
);
defparam \u_cordic/[5].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_11_s0  (
	.D(\u_cordic/[5].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [11])
);
defparam \u_cordic/[5].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_10_s0  (
	.D(\u_cordic/[5].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [10])
);
defparam \u_cordic/[5].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_9_s0  (
	.D(\u_cordic/[5].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [9])
);
defparam \u_cordic/[5].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_8_s0  (
	.D(\u_cordic/[5].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [8])
);
defparam \u_cordic/[5].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_7_s0  (
	.D(\u_cordic/[5].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [7])
);
defparam \u_cordic/[5].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_6_s0  (
	.D(\u_cordic/[5].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [6])
);
defparam \u_cordic/[5].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_5_s0  (
	.D(\u_cordic/[5].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [5])
);
defparam \u_cordic/[5].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_4_s0  (
	.D(\u_cordic/[5].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [4])
);
defparam \u_cordic/[5].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_3_s0  (
	.D(\u_cordic/[5].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [3])
);
defparam \u_cordic/[5].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_2_s0  (
	.D(\u_cordic/[5].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [2])
);
defparam \u_cordic/[5].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_1_s0  (
	.D(\u_cordic/[5].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [1])
);
defparam \u_cordic/[5].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/y_1_0_s0  (
	.D(\u_cordic/[5].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[6] [0])
);
defparam \u_cordic/[5].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_16_s0  (
	.D(\u_cordic/[5].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [16])
);
defparam \u_cordic/[5].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_15_s0  (
	.D(\u_cordic/[5].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [15])
);
defparam \u_cordic/[5].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_14_s0  (
	.D(\u_cordic/[5].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [14])
);
defparam \u_cordic/[5].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_13_s0  (
	.D(\u_cordic/[5].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [13])
);
defparam \u_cordic/[5].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_12_s0  (
	.D(\u_cordic/[5].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [12])
);
defparam \u_cordic/[5].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_11_s0  (
	.D(\u_cordic/[5].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [11])
);
defparam \u_cordic/[5].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_10_s0  (
	.D(\u_cordic/[5].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [10])
);
defparam \u_cordic/[5].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_9_s0  (
	.D(\u_cordic/z[5] [9]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [9])
);
defparam \u_cordic/[5].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_8_s0  (
	.D(\u_cordic/z[5] [8]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [8])
);
defparam \u_cordic/[5].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_7_s0  (
	.D(\u_cordic/z[5] [7]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [7])
);
defparam \u_cordic/[5].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_6_s0  (
	.D(\u_cordic/z[5] [6]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [6])
);
defparam \u_cordic/[5].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_5_s0  (
	.D(\u_cordic/z[5] [5]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [5])
);
defparam \u_cordic/[5].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_4_s0  (
	.D(\u_cordic/z[5] [4]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [4])
);
defparam \u_cordic/[5].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_3_s0  (
	.D(\u_cordic/z[5] [3]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [3])
);
defparam \u_cordic/[5].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_2_s0  (
	.D(\u_cordic/z[5] [2]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [2])
);
defparam \u_cordic/[5].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_1_s0  (
	.D(\u_cordic/z[5] [1]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [1])
);
defparam \u_cordic/[5].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/z_1_0_s0  (
	.D(\u_cordic/z[5] [0]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[6] [0])
);
defparam \u_cordic/[5].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[5].U/x_1_16_s0  (
	.D(\u_cordic/[5].U/n162_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[6] [16])
);
defparam \u_cordic/[5].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[5].U/n178_1_s  (
	.I0(\u_cordic/x[5] [0]),
	.I1(\u_cordic/y[5] [5]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/z[5][16]_1_5 ),
	.COUT(\u_cordic/[5].U/n178_1_1 ),
	.SUM(\u_cordic/[5].U/n178_2 )
);
defparam \u_cordic/[5].U/n178_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n177_1_s  (
	.I0(\u_cordic/x[5] [1]),
	.I1(\u_cordic/y[5] [6]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n178_1_1 ),
	.COUT(\u_cordic/[5].U/n177_1_1 ),
	.SUM(\u_cordic/[5].U/n177_2 )
);
defparam \u_cordic/[5].U/n177_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n176_1_s  (
	.I0(\u_cordic/x[5] [2]),
	.I1(\u_cordic/y[5] [7]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n177_1_1 ),
	.COUT(\u_cordic/[5].U/n176_1_1 ),
	.SUM(\u_cordic/[5].U/n176_2 )
);
defparam \u_cordic/[5].U/n176_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n175_1_s  (
	.I0(\u_cordic/x[5] [3]),
	.I1(\u_cordic/y[5] [8]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n176_1_1 ),
	.COUT(\u_cordic/[5].U/n175_1_1 ),
	.SUM(\u_cordic/[5].U/n175_2 )
);
defparam \u_cordic/[5].U/n175_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n174_1_s  (
	.I0(\u_cordic/x[5] [4]),
	.I1(\u_cordic/y[5] [9]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n175_1_1 ),
	.COUT(\u_cordic/[5].U/n174_1_1 ),
	.SUM(\u_cordic/[5].U/n174_2 )
);
defparam \u_cordic/[5].U/n174_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n173_1_s  (
	.I0(\u_cordic/x[5] [5]),
	.I1(\u_cordic/y[5] [10]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n174_1_1 ),
	.COUT(\u_cordic/[5].U/n173_1_1 ),
	.SUM(\u_cordic/[5].U/n173_2 )
);
defparam \u_cordic/[5].U/n173_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n172_1_s  (
	.I0(\u_cordic/x[5] [6]),
	.I1(\u_cordic/y[5] [11]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n173_1_1 ),
	.COUT(\u_cordic/[5].U/n172_1_1 ),
	.SUM(\u_cordic/[5].U/n172_2 )
);
defparam \u_cordic/[5].U/n172_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n171_1_s  (
	.I0(\u_cordic/x[5] [7]),
	.I1(\u_cordic/y[5] [12]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n172_1_1 ),
	.COUT(\u_cordic/[5].U/n171_1_1 ),
	.SUM(\u_cordic/[5].U/n171_2 )
);
defparam \u_cordic/[5].U/n171_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n170_1_s  (
	.I0(\u_cordic/x[5] [8]),
	.I1(\u_cordic/y[5] [13]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n171_1_1 ),
	.COUT(\u_cordic/[5].U/n170_1_1 ),
	.SUM(\u_cordic/[5].U/n170_2 )
);
defparam \u_cordic/[5].U/n170_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n169_1_s  (
	.I0(\u_cordic/x[5] [9]),
	.I1(\u_cordic/y[5] [14]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n170_1_1 ),
	.COUT(\u_cordic/[5].U/n169_1_1 ),
	.SUM(\u_cordic/[5].U/n169_2 )
);
defparam \u_cordic/[5].U/n169_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n168_1_s  (
	.I0(\u_cordic/x[5] [10]),
	.I1(\u_cordic/y[5] [15]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n169_1_1 ),
	.COUT(\u_cordic/[5].U/n168_1_1 ),
	.SUM(\u_cordic/[5].U/n168_2 )
);
defparam \u_cordic/[5].U/n168_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n167_1_s  (
	.I0(\u_cordic/x[5] [11]),
	.I1(\u_cordic/y[5] [16]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n168_1_1 ),
	.COUT(\u_cordic/[5].U/n167_1_1 ),
	.SUM(\u_cordic/[5].U/n167_2 )
);
defparam \u_cordic/[5].U/n167_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n166_1_s  (
	.I0(\u_cordic/x[5] [12]),
	.I1(\u_cordic/y[5] [16]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n167_1_1 ),
	.COUT(\u_cordic/[5].U/n166_1_1 ),
	.SUM(\u_cordic/[5].U/n166_2 )
);
defparam \u_cordic/[5].U/n166_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n165_1_s  (
	.I0(\u_cordic/x[5] [13]),
	.I1(\u_cordic/y[5] [16]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n166_1_1 ),
	.COUT(\u_cordic/[5].U/n165_1_1 ),
	.SUM(\u_cordic/[5].U/n165_2 )
);
defparam \u_cordic/[5].U/n165_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n164_1_s  (
	.I0(\u_cordic/x[5] [14]),
	.I1(\u_cordic/y[5] [16]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n165_1_1 ),
	.COUT(\u_cordic/[5].U/n164_1_1 ),
	.SUM(\u_cordic/[5].U/n164_2 )
);
defparam \u_cordic/[5].U/n164_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n163_1_s  (
	.I0(\u_cordic/x[5] [15]),
	.I1(\u_cordic/y[5] [16]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n164_1_1 ),
	.COUT(\u_cordic/[5].U/n163_1_1 ),
	.SUM(\u_cordic/[5].U/n163_2 )
);
defparam \u_cordic/[5].U/n163_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n162_1_s  (
	.I0(\u_cordic/x[5] [16]),
	.I1(\u_cordic/y[5] [16]),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n163_1_1 ),
	.COUT(\u_cordic/[5].U/n162_1_0_COUT ),
	.SUM(\u_cordic/[5].U/n162_2 )
);
defparam \u_cordic/[5].U/n162_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n202_1_s  (
	.I0(\u_cordic/z[5] [10]),
	.I1(VCC),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/z[5][16]_1_5 ),
	.COUT(\u_cordic/[5].U/n202_1_1 ),
	.SUM(\u_cordic/[5].U/n202_2 )
);
defparam \u_cordic/[5].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n201_1_s  (
	.I0(\u_cordic/z[5] [11]),
	.I1(GND),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n202_1_1 ),
	.COUT(\u_cordic/[5].U/n201_1_1 ),
	.SUM(\u_cordic/[5].U/n201_2 )
);
defparam \u_cordic/[5].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n200_1_s  (
	.I0(\u_cordic/z[5] [12]),
	.I1(GND),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n201_1_1 ),
	.COUT(\u_cordic/[5].U/n200_1_1 ),
	.SUM(\u_cordic/[5].U/n200_2 )
);
defparam \u_cordic/[5].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n199_1_s  (
	.I0(\u_cordic/z[5] [13]),
	.I1(GND),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n200_1_1 ),
	.COUT(\u_cordic/[5].U/n199_1_1 ),
	.SUM(\u_cordic/[5].U/n199_2 )
);
defparam \u_cordic/[5].U/n199_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n198_1_s  (
	.I0(\u_cordic/z[5] [14]),
	.I1(GND),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n199_1_1 ),
	.COUT(\u_cordic/[5].U/n198_1_1 ),
	.SUM(\u_cordic/[5].U/n198_2 )
);
defparam \u_cordic/[5].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n197_1_s  (
	.I0(\u_cordic/z[5] [15]),
	.I1(GND),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n198_1_1 ),
	.COUT(\u_cordic/[5].U/n197_1_1 ),
	.SUM(\u_cordic/[5].U/n197_2 )
);
defparam \u_cordic/[5].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n196_1_s  (
	.I0(\u_cordic/z[5] [16]),
	.I1(GND),
	.I3(\u_cordic/z[5] [16]),
	.CIN(\u_cordic/[5].U/n197_1_1 ),
	.COUT(\u_cordic/[5].U/n196_1_0_COUT ),
	.SUM(\u_cordic/[5].U/n196_2 )
);
defparam \u_cordic/[5].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n195_1_s  (
	.I0(\u_cordic/y[5] [0]),
	.I1(\u_cordic/x[5] [5]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/z[5] [16]),
	.COUT(\u_cordic/[5].U/n195_1_1 ),
	.SUM(\u_cordic/[5].U/n195_2 )
);
defparam \u_cordic/[5].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n194_1_s  (
	.I0(\u_cordic/y[5] [1]),
	.I1(\u_cordic/x[5] [6]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n195_1_1 ),
	.COUT(\u_cordic/[5].U/n194_1_1 ),
	.SUM(\u_cordic/[5].U/n194_2 )
);
defparam \u_cordic/[5].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n193_1_s  (
	.I0(\u_cordic/y[5] [2]),
	.I1(\u_cordic/x[5] [7]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n194_1_1 ),
	.COUT(\u_cordic/[5].U/n193_1_1 ),
	.SUM(\u_cordic/[5].U/n193_2 )
);
defparam \u_cordic/[5].U/n193_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n192_1_s  (
	.I0(\u_cordic/y[5] [3]),
	.I1(\u_cordic/x[5] [8]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n193_1_1 ),
	.COUT(\u_cordic/[5].U/n192_1_1 ),
	.SUM(\u_cordic/[5].U/n192_2 )
);
defparam \u_cordic/[5].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n191_1_s  (
	.I0(\u_cordic/y[5] [4]),
	.I1(\u_cordic/x[5] [9]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n192_1_1 ),
	.COUT(\u_cordic/[5].U/n191_1_1 ),
	.SUM(\u_cordic/[5].U/n191_2 )
);
defparam \u_cordic/[5].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n190_1_s  (
	.I0(\u_cordic/y[5] [5]),
	.I1(\u_cordic/x[5] [10]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n191_1_1 ),
	.COUT(\u_cordic/[5].U/n190_1_1 ),
	.SUM(\u_cordic/[5].U/n190_2 )
);
defparam \u_cordic/[5].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n189_1_s  (
	.I0(\u_cordic/y[5] [6]),
	.I1(\u_cordic/x[5] [11]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n190_1_1 ),
	.COUT(\u_cordic/[5].U/n189_1_1 ),
	.SUM(\u_cordic/[5].U/n189_2 )
);
defparam \u_cordic/[5].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n188_1_s  (
	.I0(\u_cordic/y[5] [7]),
	.I1(\u_cordic/x[5] [12]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n189_1_1 ),
	.COUT(\u_cordic/[5].U/n188_1_1 ),
	.SUM(\u_cordic/[5].U/n188_2 )
);
defparam \u_cordic/[5].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n187_1_s  (
	.I0(\u_cordic/y[5] [8]),
	.I1(\u_cordic/x[5] [13]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n188_1_1 ),
	.COUT(\u_cordic/[5].U/n187_1_1 ),
	.SUM(\u_cordic/[5].U/n187_2 )
);
defparam \u_cordic/[5].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n186_1_s  (
	.I0(\u_cordic/y[5] [9]),
	.I1(\u_cordic/x[5] [14]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n187_1_1 ),
	.COUT(\u_cordic/[5].U/n186_1_1 ),
	.SUM(\u_cordic/[5].U/n186_2 )
);
defparam \u_cordic/[5].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n185_1_s  (
	.I0(\u_cordic/y[5] [10]),
	.I1(\u_cordic/x[5] [15]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n186_1_1 ),
	.COUT(\u_cordic/[5].U/n185_1_1 ),
	.SUM(\u_cordic/[5].U/n185_2 )
);
defparam \u_cordic/[5].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n184_1_s  (
	.I0(\u_cordic/y[5] [11]),
	.I1(\u_cordic/x[5] [16]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n185_1_1 ),
	.COUT(\u_cordic/[5].U/n184_1_1 ),
	.SUM(\u_cordic/[5].U/n184_2 )
);
defparam \u_cordic/[5].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n183_1_s  (
	.I0(\u_cordic/y[5] [12]),
	.I1(\u_cordic/x[5] [16]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n184_1_1 ),
	.COUT(\u_cordic/[5].U/n183_1_1 ),
	.SUM(\u_cordic/[5].U/n183_2 )
);
defparam \u_cordic/[5].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n182_1_s  (
	.I0(\u_cordic/y[5] [13]),
	.I1(\u_cordic/x[5] [16]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n183_1_1 ),
	.COUT(\u_cordic/[5].U/n182_1_1 ),
	.SUM(\u_cordic/[5].U/n182_2 )
);
defparam \u_cordic/[5].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n181_1_s  (
	.I0(\u_cordic/y[5] [14]),
	.I1(\u_cordic/x[5] [16]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n182_1_1 ),
	.COUT(\u_cordic/[5].U/n181_1_1 ),
	.SUM(\u_cordic/[5].U/n181_2 )
);
defparam \u_cordic/[5].U/n181_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n180_1_s  (
	.I0(\u_cordic/y[5] [15]),
	.I1(\u_cordic/x[5] [16]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n181_1_1 ),
	.COUT(\u_cordic/[5].U/n180_1_1 ),
	.SUM(\u_cordic/[5].U/n180_2 )
);
defparam \u_cordic/[5].U/n180_1_s .ALU_MODE=2;
ALU \u_cordic/[5].U/n179_1_s  (
	.I0(\u_cordic/y[5] [16]),
	.I1(\u_cordic/x[5] [16]),
	.I3(\u_cordic/z[5][16]_1_5 ),
	.CIN(\u_cordic/[5].U/n180_1_1 ),
	.COUT(\u_cordic/[5].U/n179_1_0_COUT ),
	.SUM(\u_cordic/[5].U/n179_2 )
);
defparam \u_cordic/[5].U/n179_1_s .ALU_MODE=2;
LUT1 \u_cordic/[5].U/z[6][16]_1_s3  (
	.I0(\u_cordic/z[6] [16]),
	.F(\u_cordic/z[6][16]_1_5 )
);
defparam \u_cordic/[5].U/z[6][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[6].U/x_1_15_s0  (
	.D(\u_cordic/[6].U/n165_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [15])
);
defparam \u_cordic/[6].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_14_s0  (
	.D(\u_cordic/[6].U/n166_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [14])
);
defparam \u_cordic/[6].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_13_s0  (
	.D(\u_cordic/[6].U/n167_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [13])
);
defparam \u_cordic/[6].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_12_s0  (
	.D(\u_cordic/[6].U/n168_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [12])
);
defparam \u_cordic/[6].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_11_s0  (
	.D(\u_cordic/[6].U/n169_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [11])
);
defparam \u_cordic/[6].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_10_s0  (
	.D(\u_cordic/[6].U/n170_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [10])
);
defparam \u_cordic/[6].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_9_s0  (
	.D(\u_cordic/[6].U/n171_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [9])
);
defparam \u_cordic/[6].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_8_s0  (
	.D(\u_cordic/[6].U/n172_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [8])
);
defparam \u_cordic/[6].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_7_s0  (
	.D(\u_cordic/[6].U/n173_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [7])
);
defparam \u_cordic/[6].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_6_s0  (
	.D(\u_cordic/[6].U/n174_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [6])
);
defparam \u_cordic/[6].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_5_s0  (
	.D(\u_cordic/[6].U/n175_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [5])
);
defparam \u_cordic/[6].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_4_s0  (
	.D(\u_cordic/[6].U/n176_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [4])
);
defparam \u_cordic/[6].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_3_s0  (
	.D(\u_cordic/[6].U/n177_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [3])
);
defparam \u_cordic/[6].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_2_s0  (
	.D(\u_cordic/[6].U/n178_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [2])
);
defparam \u_cordic/[6].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_1_s0  (
	.D(\u_cordic/[6].U/n179_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [1])
);
defparam \u_cordic/[6].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_0_s0  (
	.D(\u_cordic/[6].U/n180_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [0])
);
defparam \u_cordic/[6].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_16_s0  (
	.D(\u_cordic/[6].U/n181_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [16])
);
defparam \u_cordic/[6].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_15_s0  (
	.D(\u_cordic/[6].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [15])
);
defparam \u_cordic/[6].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_14_s0  (
	.D(\u_cordic/[6].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [14])
);
defparam \u_cordic/[6].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_13_s0  (
	.D(\u_cordic/[6].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [13])
);
defparam \u_cordic/[6].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_12_s0  (
	.D(\u_cordic/[6].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [12])
);
defparam \u_cordic/[6].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_11_s0  (
	.D(\u_cordic/[6].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [11])
);
defparam \u_cordic/[6].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_10_s0  (
	.D(\u_cordic/[6].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [10])
);
defparam \u_cordic/[6].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_9_s0  (
	.D(\u_cordic/[6].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [9])
);
defparam \u_cordic/[6].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_8_s0  (
	.D(\u_cordic/[6].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [8])
);
defparam \u_cordic/[6].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_7_s0  (
	.D(\u_cordic/[6].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [7])
);
defparam \u_cordic/[6].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_6_s0  (
	.D(\u_cordic/[6].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [6])
);
defparam \u_cordic/[6].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_5_s0  (
	.D(\u_cordic/[6].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [5])
);
defparam \u_cordic/[6].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_4_s0  (
	.D(\u_cordic/[6].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [4])
);
defparam \u_cordic/[6].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_3_s0  (
	.D(\u_cordic/[6].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [3])
);
defparam \u_cordic/[6].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_2_s0  (
	.D(\u_cordic/[6].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [2])
);
defparam \u_cordic/[6].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_1_s0  (
	.D(\u_cordic/[6].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [1])
);
defparam \u_cordic/[6].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/y_1_0_s0  (
	.D(\u_cordic/[6].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[7] [0])
);
defparam \u_cordic/[6].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_16_s0  (
	.D(\u_cordic/[6].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [16])
);
defparam \u_cordic/[6].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_15_s0  (
	.D(\u_cordic/[6].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [15])
);
defparam \u_cordic/[6].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_14_s0  (
	.D(\u_cordic/[6].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [14])
);
defparam \u_cordic/[6].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_13_s0  (
	.D(\u_cordic/[6].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [13])
);
defparam \u_cordic/[6].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_12_s0  (
	.D(\u_cordic/[6].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [12])
);
defparam \u_cordic/[6].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_11_s0  (
	.D(\u_cordic/[6].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [11])
);
defparam \u_cordic/[6].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_10_s0  (
	.D(\u_cordic/[6].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [10])
);
defparam \u_cordic/[6].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_9_s0  (
	.D(\u_cordic/[6].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [9])
);
defparam \u_cordic/[6].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_8_s0  (
	.D(\u_cordic/z[6] [8]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [8])
);
defparam \u_cordic/[6].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_7_s0  (
	.D(\u_cordic/z[6] [7]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [7])
);
defparam \u_cordic/[6].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_6_s0  (
	.D(\u_cordic/z[6] [6]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [6])
);
defparam \u_cordic/[6].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_5_s0  (
	.D(\u_cordic/z[6] [5]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [5])
);
defparam \u_cordic/[6].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_4_s0  (
	.D(\u_cordic/z[6] [4]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [4])
);
defparam \u_cordic/[6].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_3_s0  (
	.D(\u_cordic/z[6] [3]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [3])
);
defparam \u_cordic/[6].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_2_s0  (
	.D(\u_cordic/z[6] [2]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [2])
);
defparam \u_cordic/[6].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_1_s0  (
	.D(\u_cordic/z[6] [1]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [1])
);
defparam \u_cordic/[6].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/z_1_0_s0  (
	.D(\u_cordic/z[6] [0]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[7] [0])
);
defparam \u_cordic/[6].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[6].U/x_1_16_s0  (
	.D(\u_cordic/[6].U/n164_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[7] [16])
);
defparam \u_cordic/[6].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[6].U/n180_1_s  (
	.I0(\u_cordic/x[6] [0]),
	.I1(\u_cordic/y[6] [6]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/z[6][16]_1_5 ),
	.COUT(\u_cordic/[6].U/n180_1_1 ),
	.SUM(\u_cordic/[6].U/n180_2 )
);
defparam \u_cordic/[6].U/n180_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n179_1_s  (
	.I0(\u_cordic/x[6] [1]),
	.I1(\u_cordic/y[6] [7]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n180_1_1 ),
	.COUT(\u_cordic/[6].U/n179_1_1 ),
	.SUM(\u_cordic/[6].U/n179_2 )
);
defparam \u_cordic/[6].U/n179_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n178_1_s  (
	.I0(\u_cordic/x[6] [2]),
	.I1(\u_cordic/y[6] [8]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n179_1_1 ),
	.COUT(\u_cordic/[6].U/n178_1_1 ),
	.SUM(\u_cordic/[6].U/n178_2 )
);
defparam \u_cordic/[6].U/n178_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n177_1_s  (
	.I0(\u_cordic/x[6] [3]),
	.I1(\u_cordic/y[6] [9]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n178_1_1 ),
	.COUT(\u_cordic/[6].U/n177_1_1 ),
	.SUM(\u_cordic/[6].U/n177_2 )
);
defparam \u_cordic/[6].U/n177_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n176_1_s  (
	.I0(\u_cordic/x[6] [4]),
	.I1(\u_cordic/y[6] [10]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n177_1_1 ),
	.COUT(\u_cordic/[6].U/n176_1_1 ),
	.SUM(\u_cordic/[6].U/n176_2 )
);
defparam \u_cordic/[6].U/n176_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n175_1_s  (
	.I0(\u_cordic/x[6] [5]),
	.I1(\u_cordic/y[6] [11]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n176_1_1 ),
	.COUT(\u_cordic/[6].U/n175_1_1 ),
	.SUM(\u_cordic/[6].U/n175_2 )
);
defparam \u_cordic/[6].U/n175_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n174_1_s  (
	.I0(\u_cordic/x[6] [6]),
	.I1(\u_cordic/y[6] [12]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n175_1_1 ),
	.COUT(\u_cordic/[6].U/n174_1_1 ),
	.SUM(\u_cordic/[6].U/n174_2 )
);
defparam \u_cordic/[6].U/n174_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n173_1_s  (
	.I0(\u_cordic/x[6] [7]),
	.I1(\u_cordic/y[6] [13]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n174_1_1 ),
	.COUT(\u_cordic/[6].U/n173_1_1 ),
	.SUM(\u_cordic/[6].U/n173_2 )
);
defparam \u_cordic/[6].U/n173_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n172_1_s  (
	.I0(\u_cordic/x[6] [8]),
	.I1(\u_cordic/y[6] [14]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n173_1_1 ),
	.COUT(\u_cordic/[6].U/n172_1_1 ),
	.SUM(\u_cordic/[6].U/n172_2 )
);
defparam \u_cordic/[6].U/n172_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n171_1_s  (
	.I0(\u_cordic/x[6] [9]),
	.I1(\u_cordic/y[6] [15]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n172_1_1 ),
	.COUT(\u_cordic/[6].U/n171_1_1 ),
	.SUM(\u_cordic/[6].U/n171_2 )
);
defparam \u_cordic/[6].U/n171_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n170_1_s  (
	.I0(\u_cordic/x[6] [10]),
	.I1(\u_cordic/y[6] [16]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n171_1_1 ),
	.COUT(\u_cordic/[6].U/n170_1_1 ),
	.SUM(\u_cordic/[6].U/n170_2 )
);
defparam \u_cordic/[6].U/n170_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n169_1_s  (
	.I0(\u_cordic/x[6] [11]),
	.I1(\u_cordic/y[6] [16]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n170_1_1 ),
	.COUT(\u_cordic/[6].U/n169_1_1 ),
	.SUM(\u_cordic/[6].U/n169_2 )
);
defparam \u_cordic/[6].U/n169_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n168_1_s  (
	.I0(\u_cordic/x[6] [12]),
	.I1(\u_cordic/y[6] [16]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n169_1_1 ),
	.COUT(\u_cordic/[6].U/n168_1_1 ),
	.SUM(\u_cordic/[6].U/n168_2 )
);
defparam \u_cordic/[6].U/n168_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n167_1_s  (
	.I0(\u_cordic/x[6] [13]),
	.I1(\u_cordic/y[6] [16]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n168_1_1 ),
	.COUT(\u_cordic/[6].U/n167_1_1 ),
	.SUM(\u_cordic/[6].U/n167_2 )
);
defparam \u_cordic/[6].U/n167_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n166_1_s  (
	.I0(\u_cordic/x[6] [14]),
	.I1(\u_cordic/y[6] [16]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n167_1_1 ),
	.COUT(\u_cordic/[6].U/n166_1_1 ),
	.SUM(\u_cordic/[6].U/n166_2 )
);
defparam \u_cordic/[6].U/n166_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n165_1_s  (
	.I0(\u_cordic/x[6] [15]),
	.I1(\u_cordic/y[6] [16]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n166_1_1 ),
	.COUT(\u_cordic/[6].U/n165_1_1 ),
	.SUM(\u_cordic/[6].U/n165_2 )
);
defparam \u_cordic/[6].U/n165_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n164_1_s  (
	.I0(\u_cordic/x[6] [16]),
	.I1(\u_cordic/y[6] [16]),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n165_1_1 ),
	.COUT(\u_cordic/[6].U/n164_1_0_COUT ),
	.SUM(\u_cordic/[6].U/n164_2 )
);
defparam \u_cordic/[6].U/n164_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n205_1_s  (
	.I0(\u_cordic/z[6] [9]),
	.I1(VCC),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/z[6][16]_1_5 ),
	.COUT(\u_cordic/[6].U/n205_1_1 ),
	.SUM(\u_cordic/[6].U/n205_2 )
);
defparam \u_cordic/[6].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n204_1_s  (
	.I0(\u_cordic/z[6] [10]),
	.I1(GND),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n205_1_1 ),
	.COUT(\u_cordic/[6].U/n204_1_1 ),
	.SUM(\u_cordic/[6].U/n204_2 )
);
defparam \u_cordic/[6].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n203_1_s  (
	.I0(\u_cordic/z[6] [11]),
	.I1(GND),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n204_1_1 ),
	.COUT(\u_cordic/[6].U/n203_1_1 ),
	.SUM(\u_cordic/[6].U/n203_2 )
);
defparam \u_cordic/[6].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n202_1_s  (
	.I0(\u_cordic/z[6] [12]),
	.I1(GND),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n203_1_1 ),
	.COUT(\u_cordic/[6].U/n202_1_1 ),
	.SUM(\u_cordic/[6].U/n202_2 )
);
defparam \u_cordic/[6].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n201_1_s  (
	.I0(\u_cordic/z[6] [13]),
	.I1(GND),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n202_1_1 ),
	.COUT(\u_cordic/[6].U/n201_1_1 ),
	.SUM(\u_cordic/[6].U/n201_2 )
);
defparam \u_cordic/[6].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n200_1_s  (
	.I0(\u_cordic/z[6] [14]),
	.I1(GND),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n201_1_1 ),
	.COUT(\u_cordic/[6].U/n200_1_1 ),
	.SUM(\u_cordic/[6].U/n200_2 )
);
defparam \u_cordic/[6].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n199_1_s  (
	.I0(\u_cordic/z[6] [15]),
	.I1(GND),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n200_1_1 ),
	.COUT(\u_cordic/[6].U/n199_1_1 ),
	.SUM(\u_cordic/[6].U/n199_2 )
);
defparam \u_cordic/[6].U/n199_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n198_1_s  (
	.I0(\u_cordic/z[6] [16]),
	.I1(GND),
	.I3(\u_cordic/z[6] [16]),
	.CIN(\u_cordic/[6].U/n199_1_1 ),
	.COUT(\u_cordic/[6].U/n198_1_0_COUT ),
	.SUM(\u_cordic/[6].U/n198_2 )
);
defparam \u_cordic/[6].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n197_1_s  (
	.I0(\u_cordic/y[6] [0]),
	.I1(\u_cordic/x[6] [6]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/z[6] [16]),
	.COUT(\u_cordic/[6].U/n197_1_1 ),
	.SUM(\u_cordic/[6].U/n197_2 )
);
defparam \u_cordic/[6].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n196_1_s  (
	.I0(\u_cordic/y[6] [1]),
	.I1(\u_cordic/x[6] [7]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n197_1_1 ),
	.COUT(\u_cordic/[6].U/n196_1_1 ),
	.SUM(\u_cordic/[6].U/n196_2 )
);
defparam \u_cordic/[6].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n195_1_s  (
	.I0(\u_cordic/y[6] [2]),
	.I1(\u_cordic/x[6] [8]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n196_1_1 ),
	.COUT(\u_cordic/[6].U/n195_1_1 ),
	.SUM(\u_cordic/[6].U/n195_2 )
);
defparam \u_cordic/[6].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n194_1_s  (
	.I0(\u_cordic/y[6] [3]),
	.I1(\u_cordic/x[6] [9]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n195_1_1 ),
	.COUT(\u_cordic/[6].U/n194_1_1 ),
	.SUM(\u_cordic/[6].U/n194_2 )
);
defparam \u_cordic/[6].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n193_1_s  (
	.I0(\u_cordic/y[6] [4]),
	.I1(\u_cordic/x[6] [10]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n194_1_1 ),
	.COUT(\u_cordic/[6].U/n193_1_1 ),
	.SUM(\u_cordic/[6].U/n193_2 )
);
defparam \u_cordic/[6].U/n193_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n192_1_s  (
	.I0(\u_cordic/y[6] [5]),
	.I1(\u_cordic/x[6] [11]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n193_1_1 ),
	.COUT(\u_cordic/[6].U/n192_1_1 ),
	.SUM(\u_cordic/[6].U/n192_2 )
);
defparam \u_cordic/[6].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n191_1_s  (
	.I0(\u_cordic/y[6] [6]),
	.I1(\u_cordic/x[6] [12]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n192_1_1 ),
	.COUT(\u_cordic/[6].U/n191_1_1 ),
	.SUM(\u_cordic/[6].U/n191_2 )
);
defparam \u_cordic/[6].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n190_1_s  (
	.I0(\u_cordic/y[6] [7]),
	.I1(\u_cordic/x[6] [13]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n191_1_1 ),
	.COUT(\u_cordic/[6].U/n190_1_1 ),
	.SUM(\u_cordic/[6].U/n190_2 )
);
defparam \u_cordic/[6].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n189_1_s  (
	.I0(\u_cordic/y[6] [8]),
	.I1(\u_cordic/x[6] [14]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n190_1_1 ),
	.COUT(\u_cordic/[6].U/n189_1_1 ),
	.SUM(\u_cordic/[6].U/n189_2 )
);
defparam \u_cordic/[6].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n188_1_s  (
	.I0(\u_cordic/y[6] [9]),
	.I1(\u_cordic/x[6] [15]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n189_1_1 ),
	.COUT(\u_cordic/[6].U/n188_1_1 ),
	.SUM(\u_cordic/[6].U/n188_2 )
);
defparam \u_cordic/[6].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n187_1_s  (
	.I0(\u_cordic/y[6] [10]),
	.I1(\u_cordic/x[6] [16]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n188_1_1 ),
	.COUT(\u_cordic/[6].U/n187_1_1 ),
	.SUM(\u_cordic/[6].U/n187_2 )
);
defparam \u_cordic/[6].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n186_1_s  (
	.I0(\u_cordic/y[6] [11]),
	.I1(\u_cordic/x[6] [16]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n187_1_1 ),
	.COUT(\u_cordic/[6].U/n186_1_1 ),
	.SUM(\u_cordic/[6].U/n186_2 )
);
defparam \u_cordic/[6].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n185_1_s  (
	.I0(\u_cordic/y[6] [12]),
	.I1(\u_cordic/x[6] [16]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n186_1_1 ),
	.COUT(\u_cordic/[6].U/n185_1_1 ),
	.SUM(\u_cordic/[6].U/n185_2 )
);
defparam \u_cordic/[6].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n184_1_s  (
	.I0(\u_cordic/y[6] [13]),
	.I1(\u_cordic/x[6] [16]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n185_1_1 ),
	.COUT(\u_cordic/[6].U/n184_1_1 ),
	.SUM(\u_cordic/[6].U/n184_2 )
);
defparam \u_cordic/[6].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n183_1_s  (
	.I0(\u_cordic/y[6] [14]),
	.I1(\u_cordic/x[6] [16]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n184_1_1 ),
	.COUT(\u_cordic/[6].U/n183_1_1 ),
	.SUM(\u_cordic/[6].U/n183_2 )
);
defparam \u_cordic/[6].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n182_1_s  (
	.I0(\u_cordic/y[6] [15]),
	.I1(\u_cordic/x[6] [16]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n183_1_1 ),
	.COUT(\u_cordic/[6].U/n182_1_1 ),
	.SUM(\u_cordic/[6].U/n182_2 )
);
defparam \u_cordic/[6].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[6].U/n181_1_s  (
	.I0(\u_cordic/y[6] [16]),
	.I1(\u_cordic/x[6] [16]),
	.I3(\u_cordic/z[6][16]_1_5 ),
	.CIN(\u_cordic/[6].U/n182_1_1 ),
	.COUT(\u_cordic/[6].U/n181_1_0_COUT ),
	.SUM(\u_cordic/[6].U/n181_2 )
);
defparam \u_cordic/[6].U/n181_1_s .ALU_MODE=2;
LUT1 \u_cordic/[6].U/z[7][16]_1_s3  (
	.I0(\u_cordic/z[7] [16]),
	.F(\u_cordic/z[7][16]_1_5 )
);
defparam \u_cordic/[6].U/z[7][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[7].U/x_1_15_s0  (
	.D(\u_cordic/[7].U/n167_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [15])
);
defparam \u_cordic/[7].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_14_s0  (
	.D(\u_cordic/[7].U/n168_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [14])
);
defparam \u_cordic/[7].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_13_s0  (
	.D(\u_cordic/[7].U/n169_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [13])
);
defparam \u_cordic/[7].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_12_s0  (
	.D(\u_cordic/[7].U/n170_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [12])
);
defparam \u_cordic/[7].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_11_s0  (
	.D(\u_cordic/[7].U/n171_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [11])
);
defparam \u_cordic/[7].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_10_s0  (
	.D(\u_cordic/[7].U/n172_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [10])
);
defparam \u_cordic/[7].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_9_s0  (
	.D(\u_cordic/[7].U/n173_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [9])
);
defparam \u_cordic/[7].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_8_s0  (
	.D(\u_cordic/[7].U/n174_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [8])
);
defparam \u_cordic/[7].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_7_s0  (
	.D(\u_cordic/[7].U/n175_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [7])
);
defparam \u_cordic/[7].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_6_s0  (
	.D(\u_cordic/[7].U/n176_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [6])
);
defparam \u_cordic/[7].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_5_s0  (
	.D(\u_cordic/[7].U/n177_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [5])
);
defparam \u_cordic/[7].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_4_s0  (
	.D(\u_cordic/[7].U/n178_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [4])
);
defparam \u_cordic/[7].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_3_s0  (
	.D(\u_cordic/[7].U/n179_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [3])
);
defparam \u_cordic/[7].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_2_s0  (
	.D(\u_cordic/[7].U/n180_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [2])
);
defparam \u_cordic/[7].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_1_s0  (
	.D(\u_cordic/[7].U/n181_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [1])
);
defparam \u_cordic/[7].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_0_s0  (
	.D(\u_cordic/[7].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [0])
);
defparam \u_cordic/[7].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_16_s0  (
	.D(\u_cordic/[7].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [16])
);
defparam \u_cordic/[7].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_15_s0  (
	.D(\u_cordic/[7].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [15])
);
defparam \u_cordic/[7].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_14_s0  (
	.D(\u_cordic/[7].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [14])
);
defparam \u_cordic/[7].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_13_s0  (
	.D(\u_cordic/[7].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [13])
);
defparam \u_cordic/[7].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_12_s0  (
	.D(\u_cordic/[7].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [12])
);
defparam \u_cordic/[7].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_11_s0  (
	.D(\u_cordic/[7].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [11])
);
defparam \u_cordic/[7].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_10_s0  (
	.D(\u_cordic/[7].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [10])
);
defparam \u_cordic/[7].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_9_s0  (
	.D(\u_cordic/[7].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [9])
);
defparam \u_cordic/[7].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_8_s0  (
	.D(\u_cordic/[7].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [8])
);
defparam \u_cordic/[7].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_7_s0  (
	.D(\u_cordic/[7].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [7])
);
defparam \u_cordic/[7].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_6_s0  (
	.D(\u_cordic/[7].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [6])
);
defparam \u_cordic/[7].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_5_s0  (
	.D(\u_cordic/[7].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [5])
);
defparam \u_cordic/[7].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_4_s0  (
	.D(\u_cordic/[7].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [4])
);
defparam \u_cordic/[7].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_3_s0  (
	.D(\u_cordic/[7].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [3])
);
defparam \u_cordic/[7].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_2_s0  (
	.D(\u_cordic/[7].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [2])
);
defparam \u_cordic/[7].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_1_s0  (
	.D(\u_cordic/[7].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [1])
);
defparam \u_cordic/[7].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/y_1_0_s0  (
	.D(\u_cordic/[7].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[8] [0])
);
defparam \u_cordic/[7].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_16_s0  (
	.D(\u_cordic/[7].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [16])
);
defparam \u_cordic/[7].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_15_s0  (
	.D(\u_cordic/[7].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [15])
);
defparam \u_cordic/[7].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_14_s0  (
	.D(\u_cordic/[7].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [14])
);
defparam \u_cordic/[7].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_13_s0  (
	.D(\u_cordic/[7].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [13])
);
defparam \u_cordic/[7].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_12_s0  (
	.D(\u_cordic/[7].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [12])
);
defparam \u_cordic/[7].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_11_s0  (
	.D(\u_cordic/[7].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [11])
);
defparam \u_cordic/[7].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_10_s0  (
	.D(\u_cordic/[7].U/n206_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [10])
);
defparam \u_cordic/[7].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_9_s0  (
	.D(\u_cordic/[7].U/n207_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [9])
);
defparam \u_cordic/[7].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_8_s0  (
	.D(\u_cordic/[7].U/n208_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [8])
);
defparam \u_cordic/[7].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_7_s0  (
	.D(\u_cordic/z[7] [7]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [7])
);
defparam \u_cordic/[7].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_6_s0  (
	.D(\u_cordic/z[7] [6]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [6])
);
defparam \u_cordic/[7].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_5_s0  (
	.D(\u_cordic/z[7] [5]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [5])
);
defparam \u_cordic/[7].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_4_s0  (
	.D(\u_cordic/z[7] [4]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [4])
);
defparam \u_cordic/[7].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_3_s0  (
	.D(\u_cordic/z[7] [3]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [3])
);
defparam \u_cordic/[7].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_2_s0  (
	.D(\u_cordic/z[7] [2]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [2])
);
defparam \u_cordic/[7].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_1_s0  (
	.D(\u_cordic/z[7] [1]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [1])
);
defparam \u_cordic/[7].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/z_1_0_s0  (
	.D(\u_cordic/z[7] [0]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[8] [0])
);
defparam \u_cordic/[7].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[7].U/x_1_16_s0  (
	.D(\u_cordic/[7].U/n166_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[8] [16])
);
defparam \u_cordic/[7].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[7].U/n182_1_s  (
	.I0(\u_cordic/x[7] [0]),
	.I1(\u_cordic/y[7] [7]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/z[7][16]_1_5 ),
	.COUT(\u_cordic/[7].U/n182_1_1 ),
	.SUM(\u_cordic/[7].U/n182_2 )
);
defparam \u_cordic/[7].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n181_1_s  (
	.I0(\u_cordic/x[7] [1]),
	.I1(\u_cordic/y[7] [8]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n182_1_1 ),
	.COUT(\u_cordic/[7].U/n181_1_1 ),
	.SUM(\u_cordic/[7].U/n181_2 )
);
defparam \u_cordic/[7].U/n181_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n180_1_s  (
	.I0(\u_cordic/x[7] [2]),
	.I1(\u_cordic/y[7] [9]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n181_1_1 ),
	.COUT(\u_cordic/[7].U/n180_1_1 ),
	.SUM(\u_cordic/[7].U/n180_2 )
);
defparam \u_cordic/[7].U/n180_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n179_1_s  (
	.I0(\u_cordic/x[7] [3]),
	.I1(\u_cordic/y[7] [10]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n180_1_1 ),
	.COUT(\u_cordic/[7].U/n179_1_1 ),
	.SUM(\u_cordic/[7].U/n179_2 )
);
defparam \u_cordic/[7].U/n179_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n178_1_s  (
	.I0(\u_cordic/x[7] [4]),
	.I1(\u_cordic/y[7] [11]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n179_1_1 ),
	.COUT(\u_cordic/[7].U/n178_1_1 ),
	.SUM(\u_cordic/[7].U/n178_2 )
);
defparam \u_cordic/[7].U/n178_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n177_1_s  (
	.I0(\u_cordic/x[7] [5]),
	.I1(\u_cordic/y[7] [12]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n178_1_1 ),
	.COUT(\u_cordic/[7].U/n177_1_1 ),
	.SUM(\u_cordic/[7].U/n177_2 )
);
defparam \u_cordic/[7].U/n177_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n176_1_s  (
	.I0(\u_cordic/x[7] [6]),
	.I1(\u_cordic/y[7] [13]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n177_1_1 ),
	.COUT(\u_cordic/[7].U/n176_1_1 ),
	.SUM(\u_cordic/[7].U/n176_2 )
);
defparam \u_cordic/[7].U/n176_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n175_1_s  (
	.I0(\u_cordic/x[7] [7]),
	.I1(\u_cordic/y[7] [14]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n176_1_1 ),
	.COUT(\u_cordic/[7].U/n175_1_1 ),
	.SUM(\u_cordic/[7].U/n175_2 )
);
defparam \u_cordic/[7].U/n175_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n174_1_s  (
	.I0(\u_cordic/x[7] [8]),
	.I1(\u_cordic/y[7] [15]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n175_1_1 ),
	.COUT(\u_cordic/[7].U/n174_1_1 ),
	.SUM(\u_cordic/[7].U/n174_2 )
);
defparam \u_cordic/[7].U/n174_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n173_1_s  (
	.I0(\u_cordic/x[7] [9]),
	.I1(\u_cordic/y[7] [16]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n174_1_1 ),
	.COUT(\u_cordic/[7].U/n173_1_1 ),
	.SUM(\u_cordic/[7].U/n173_2 )
);
defparam \u_cordic/[7].U/n173_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n172_1_s  (
	.I0(\u_cordic/x[7] [10]),
	.I1(\u_cordic/y[7] [16]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n173_1_1 ),
	.COUT(\u_cordic/[7].U/n172_1_1 ),
	.SUM(\u_cordic/[7].U/n172_2 )
);
defparam \u_cordic/[7].U/n172_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n171_1_s  (
	.I0(\u_cordic/x[7] [11]),
	.I1(\u_cordic/y[7] [16]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n172_1_1 ),
	.COUT(\u_cordic/[7].U/n171_1_1 ),
	.SUM(\u_cordic/[7].U/n171_2 )
);
defparam \u_cordic/[7].U/n171_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n170_1_s  (
	.I0(\u_cordic/x[7] [12]),
	.I1(\u_cordic/y[7] [16]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n171_1_1 ),
	.COUT(\u_cordic/[7].U/n170_1_1 ),
	.SUM(\u_cordic/[7].U/n170_2 )
);
defparam \u_cordic/[7].U/n170_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n169_1_s  (
	.I0(\u_cordic/x[7] [13]),
	.I1(\u_cordic/y[7] [16]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n170_1_1 ),
	.COUT(\u_cordic/[7].U/n169_1_1 ),
	.SUM(\u_cordic/[7].U/n169_2 )
);
defparam \u_cordic/[7].U/n169_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n168_1_s  (
	.I0(\u_cordic/x[7] [14]),
	.I1(\u_cordic/y[7] [16]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n169_1_1 ),
	.COUT(\u_cordic/[7].U/n168_1_1 ),
	.SUM(\u_cordic/[7].U/n168_2 )
);
defparam \u_cordic/[7].U/n168_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n167_1_s  (
	.I0(\u_cordic/x[7] [15]),
	.I1(\u_cordic/y[7] [16]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n168_1_1 ),
	.COUT(\u_cordic/[7].U/n167_1_1 ),
	.SUM(\u_cordic/[7].U/n167_2 )
);
defparam \u_cordic/[7].U/n167_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n166_1_s  (
	.I0(\u_cordic/x[7] [16]),
	.I1(\u_cordic/y[7] [16]),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n167_1_1 ),
	.COUT(\u_cordic/[7].U/n166_1_0_COUT ),
	.SUM(\u_cordic/[7].U/n166_2 )
);
defparam \u_cordic/[7].U/n166_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n208_1_s  (
	.I0(\u_cordic/z[7] [8]),
	.I1(VCC),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/z[7][16]_1_5 ),
	.COUT(\u_cordic/[7].U/n208_1_1 ),
	.SUM(\u_cordic/[7].U/n208_2 )
);
defparam \u_cordic/[7].U/n208_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n207_1_s  (
	.I0(\u_cordic/z[7] [9]),
	.I1(GND),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n208_1_1 ),
	.COUT(\u_cordic/[7].U/n207_1_1 ),
	.SUM(\u_cordic/[7].U/n207_2 )
);
defparam \u_cordic/[7].U/n207_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n206_1_s  (
	.I0(\u_cordic/z[7] [10]),
	.I1(GND),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n207_1_1 ),
	.COUT(\u_cordic/[7].U/n206_1_1 ),
	.SUM(\u_cordic/[7].U/n206_2 )
);
defparam \u_cordic/[7].U/n206_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n205_1_s  (
	.I0(\u_cordic/z[7] [11]),
	.I1(GND),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n206_1_1 ),
	.COUT(\u_cordic/[7].U/n205_1_1 ),
	.SUM(\u_cordic/[7].U/n205_2 )
);
defparam \u_cordic/[7].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n204_1_s  (
	.I0(\u_cordic/z[7] [12]),
	.I1(GND),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n205_1_1 ),
	.COUT(\u_cordic/[7].U/n204_1_1 ),
	.SUM(\u_cordic/[7].U/n204_2 )
);
defparam \u_cordic/[7].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n203_1_s  (
	.I0(\u_cordic/z[7] [13]),
	.I1(GND),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n204_1_1 ),
	.COUT(\u_cordic/[7].U/n203_1_1 ),
	.SUM(\u_cordic/[7].U/n203_2 )
);
defparam \u_cordic/[7].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n202_1_s  (
	.I0(\u_cordic/z[7] [14]),
	.I1(GND),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n203_1_1 ),
	.COUT(\u_cordic/[7].U/n202_1_1 ),
	.SUM(\u_cordic/[7].U/n202_2 )
);
defparam \u_cordic/[7].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n201_1_s  (
	.I0(\u_cordic/z[7] [15]),
	.I1(GND),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n202_1_1 ),
	.COUT(\u_cordic/[7].U/n201_1_1 ),
	.SUM(\u_cordic/[7].U/n201_2 )
);
defparam \u_cordic/[7].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n200_1_s  (
	.I0(\u_cordic/z[7] [16]),
	.I1(GND),
	.I3(\u_cordic/z[7] [16]),
	.CIN(\u_cordic/[7].U/n201_1_1 ),
	.COUT(\u_cordic/[7].U/n200_1_0_COUT ),
	.SUM(\u_cordic/[7].U/n200_2 )
);
defparam \u_cordic/[7].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n199_1_s  (
	.I0(\u_cordic/y[7] [0]),
	.I1(\u_cordic/x[7] [7]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/z[7] [16]),
	.COUT(\u_cordic/[7].U/n199_1_1 ),
	.SUM(\u_cordic/[7].U/n199_2 )
);
defparam \u_cordic/[7].U/n199_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n198_1_s  (
	.I0(\u_cordic/y[7] [1]),
	.I1(\u_cordic/x[7] [8]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n199_1_1 ),
	.COUT(\u_cordic/[7].U/n198_1_1 ),
	.SUM(\u_cordic/[7].U/n198_2 )
);
defparam \u_cordic/[7].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n197_1_s  (
	.I0(\u_cordic/y[7] [2]),
	.I1(\u_cordic/x[7] [9]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n198_1_1 ),
	.COUT(\u_cordic/[7].U/n197_1_1 ),
	.SUM(\u_cordic/[7].U/n197_2 )
);
defparam \u_cordic/[7].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n196_1_s  (
	.I0(\u_cordic/y[7] [3]),
	.I1(\u_cordic/x[7] [10]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n197_1_1 ),
	.COUT(\u_cordic/[7].U/n196_1_1 ),
	.SUM(\u_cordic/[7].U/n196_2 )
);
defparam \u_cordic/[7].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n195_1_s  (
	.I0(\u_cordic/y[7] [4]),
	.I1(\u_cordic/x[7] [11]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n196_1_1 ),
	.COUT(\u_cordic/[7].U/n195_1_1 ),
	.SUM(\u_cordic/[7].U/n195_2 )
);
defparam \u_cordic/[7].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n194_1_s  (
	.I0(\u_cordic/y[7] [5]),
	.I1(\u_cordic/x[7] [12]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n195_1_1 ),
	.COUT(\u_cordic/[7].U/n194_1_1 ),
	.SUM(\u_cordic/[7].U/n194_2 )
);
defparam \u_cordic/[7].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n193_1_s  (
	.I0(\u_cordic/y[7] [6]),
	.I1(\u_cordic/x[7] [13]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n194_1_1 ),
	.COUT(\u_cordic/[7].U/n193_1_1 ),
	.SUM(\u_cordic/[7].U/n193_2 )
);
defparam \u_cordic/[7].U/n193_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n192_1_s  (
	.I0(\u_cordic/y[7] [7]),
	.I1(\u_cordic/x[7] [14]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n193_1_1 ),
	.COUT(\u_cordic/[7].U/n192_1_1 ),
	.SUM(\u_cordic/[7].U/n192_2 )
);
defparam \u_cordic/[7].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n191_1_s  (
	.I0(\u_cordic/y[7] [8]),
	.I1(\u_cordic/x[7] [15]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n192_1_1 ),
	.COUT(\u_cordic/[7].U/n191_1_1 ),
	.SUM(\u_cordic/[7].U/n191_2 )
);
defparam \u_cordic/[7].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n190_1_s  (
	.I0(\u_cordic/y[7] [9]),
	.I1(\u_cordic/x[7] [16]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n191_1_1 ),
	.COUT(\u_cordic/[7].U/n190_1_1 ),
	.SUM(\u_cordic/[7].U/n190_2 )
);
defparam \u_cordic/[7].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n189_1_s  (
	.I0(\u_cordic/y[7] [10]),
	.I1(\u_cordic/x[7] [16]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n190_1_1 ),
	.COUT(\u_cordic/[7].U/n189_1_1 ),
	.SUM(\u_cordic/[7].U/n189_2 )
);
defparam \u_cordic/[7].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n188_1_s  (
	.I0(\u_cordic/y[7] [11]),
	.I1(\u_cordic/x[7] [16]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n189_1_1 ),
	.COUT(\u_cordic/[7].U/n188_1_1 ),
	.SUM(\u_cordic/[7].U/n188_2 )
);
defparam \u_cordic/[7].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n187_1_s  (
	.I0(\u_cordic/y[7] [12]),
	.I1(\u_cordic/x[7] [16]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n188_1_1 ),
	.COUT(\u_cordic/[7].U/n187_1_1 ),
	.SUM(\u_cordic/[7].U/n187_2 )
);
defparam \u_cordic/[7].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n186_1_s  (
	.I0(\u_cordic/y[7] [13]),
	.I1(\u_cordic/x[7] [16]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n187_1_1 ),
	.COUT(\u_cordic/[7].U/n186_1_1 ),
	.SUM(\u_cordic/[7].U/n186_2 )
);
defparam \u_cordic/[7].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n185_1_s  (
	.I0(\u_cordic/y[7] [14]),
	.I1(\u_cordic/x[7] [16]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n186_1_1 ),
	.COUT(\u_cordic/[7].U/n185_1_1 ),
	.SUM(\u_cordic/[7].U/n185_2 )
);
defparam \u_cordic/[7].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n184_1_s  (
	.I0(\u_cordic/y[7] [15]),
	.I1(\u_cordic/x[7] [16]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n185_1_1 ),
	.COUT(\u_cordic/[7].U/n184_1_1 ),
	.SUM(\u_cordic/[7].U/n184_2 )
);
defparam \u_cordic/[7].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[7].U/n183_1_s  (
	.I0(\u_cordic/y[7] [16]),
	.I1(\u_cordic/x[7] [16]),
	.I3(\u_cordic/z[7][16]_1_5 ),
	.CIN(\u_cordic/[7].U/n184_1_1 ),
	.COUT(\u_cordic/[7].U/n183_1_0_COUT ),
	.SUM(\u_cordic/[7].U/n183_2 )
);
defparam \u_cordic/[7].U/n183_1_s .ALU_MODE=2;
LUT1 \u_cordic/[7].U/z[8][16]_1_s3  (
	.I0(\u_cordic/z[8] [16]),
	.F(\u_cordic/z[8][16]_1_5 )
);
defparam \u_cordic/[7].U/z[8][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[8].U/x_1_15_s0  (
	.D(\u_cordic/[8].U/n169_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [15])
);
defparam \u_cordic/[8].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_14_s0  (
	.D(\u_cordic/[8].U/n170_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [14])
);
defparam \u_cordic/[8].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_13_s0  (
	.D(\u_cordic/[8].U/n171_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [13])
);
defparam \u_cordic/[8].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_12_s0  (
	.D(\u_cordic/[8].U/n172_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [12])
);
defparam \u_cordic/[8].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_11_s0  (
	.D(\u_cordic/[8].U/n173_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [11])
);
defparam \u_cordic/[8].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_10_s0  (
	.D(\u_cordic/[8].U/n174_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [10])
);
defparam \u_cordic/[8].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_9_s0  (
	.D(\u_cordic/[8].U/n175_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [9])
);
defparam \u_cordic/[8].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_8_s0  (
	.D(\u_cordic/[8].U/n176_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [8])
);
defparam \u_cordic/[8].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_7_s0  (
	.D(\u_cordic/[8].U/n177_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [7])
);
defparam \u_cordic/[8].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_6_s0  (
	.D(\u_cordic/[8].U/n178_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [6])
);
defparam \u_cordic/[8].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_5_s0  (
	.D(\u_cordic/[8].U/n179_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [5])
);
defparam \u_cordic/[8].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_4_s0  (
	.D(\u_cordic/[8].U/n180_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [4])
);
defparam \u_cordic/[8].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_3_s0  (
	.D(\u_cordic/[8].U/n181_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [3])
);
defparam \u_cordic/[8].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_2_s0  (
	.D(\u_cordic/[8].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [2])
);
defparam \u_cordic/[8].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_1_s0  (
	.D(\u_cordic/[8].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [1])
);
defparam \u_cordic/[8].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_0_s0  (
	.D(\u_cordic/[8].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [0])
);
defparam \u_cordic/[8].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_16_s0  (
	.D(\u_cordic/[8].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [16])
);
defparam \u_cordic/[8].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_15_s0  (
	.D(\u_cordic/[8].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [15])
);
defparam \u_cordic/[8].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_14_s0  (
	.D(\u_cordic/[8].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [14])
);
defparam \u_cordic/[8].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_13_s0  (
	.D(\u_cordic/[8].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [13])
);
defparam \u_cordic/[8].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_12_s0  (
	.D(\u_cordic/[8].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [12])
);
defparam \u_cordic/[8].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_11_s0  (
	.D(\u_cordic/[8].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [11])
);
defparam \u_cordic/[8].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_10_s0  (
	.D(\u_cordic/[8].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [10])
);
defparam \u_cordic/[8].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_9_s0  (
	.D(\u_cordic/[8].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [9])
);
defparam \u_cordic/[8].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_8_s0  (
	.D(\u_cordic/[8].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [8])
);
defparam \u_cordic/[8].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_7_s0  (
	.D(\u_cordic/[8].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [7])
);
defparam \u_cordic/[8].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_6_s0  (
	.D(\u_cordic/[8].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [6])
);
defparam \u_cordic/[8].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_5_s0  (
	.D(\u_cordic/[8].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [5])
);
defparam \u_cordic/[8].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_4_s0  (
	.D(\u_cordic/[8].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [4])
);
defparam \u_cordic/[8].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_3_s0  (
	.D(\u_cordic/[8].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [3])
);
defparam \u_cordic/[8].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_2_s0  (
	.D(\u_cordic/[8].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [2])
);
defparam \u_cordic/[8].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_1_s0  (
	.D(\u_cordic/[8].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [1])
);
defparam \u_cordic/[8].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/y_1_0_s0  (
	.D(\u_cordic/[8].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[9] [0])
);
defparam \u_cordic/[8].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_16_s0  (
	.D(\u_cordic/[8].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [16])
);
defparam \u_cordic/[8].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_15_s0  (
	.D(\u_cordic/[8].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [15])
);
defparam \u_cordic/[8].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_14_s0  (
	.D(\u_cordic/[8].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [14])
);
defparam \u_cordic/[8].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_13_s0  (
	.D(\u_cordic/[8].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [13])
);
defparam \u_cordic/[8].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_12_s0  (
	.D(\u_cordic/[8].U/n206_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [12])
);
defparam \u_cordic/[8].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_11_s0  (
	.D(\u_cordic/[8].U/n207_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [11])
);
defparam \u_cordic/[8].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_10_s0  (
	.D(\u_cordic/[8].U/n208_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [10])
);
defparam \u_cordic/[8].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_9_s0  (
	.D(\u_cordic/[8].U/n209_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [9])
);
defparam \u_cordic/[8].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_8_s0  (
	.D(\u_cordic/[8].U/n210_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [8])
);
defparam \u_cordic/[8].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_7_s0  (
	.D(\u_cordic/[8].U/n211_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [7])
);
defparam \u_cordic/[8].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_6_s0  (
	.D(\u_cordic/z[8] [6]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [6])
);
defparam \u_cordic/[8].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_5_s0  (
	.D(\u_cordic/z[8] [5]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [5])
);
defparam \u_cordic/[8].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_4_s0  (
	.D(\u_cordic/z[8] [4]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [4])
);
defparam \u_cordic/[8].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_3_s0  (
	.D(\u_cordic/z[8] [3]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [3])
);
defparam \u_cordic/[8].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_2_s0  (
	.D(\u_cordic/z[8] [2]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [2])
);
defparam \u_cordic/[8].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_1_s0  (
	.D(\u_cordic/z[8] [1]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [1])
);
defparam \u_cordic/[8].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/z_1_0_s0  (
	.D(\u_cordic/z[8] [0]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[9] [0])
);
defparam \u_cordic/[8].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[8].U/x_1_16_s0  (
	.D(\u_cordic/[8].U/n168_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[9] [16])
);
defparam \u_cordic/[8].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[8].U/n184_1_s  (
	.I0(\u_cordic/x[8] [0]),
	.I1(\u_cordic/y[8] [8]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/z[8][16]_1_5 ),
	.COUT(\u_cordic/[8].U/n184_1_1 ),
	.SUM(\u_cordic/[8].U/n184_2 )
);
defparam \u_cordic/[8].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n183_1_s  (
	.I0(\u_cordic/x[8] [1]),
	.I1(\u_cordic/y[8] [9]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n184_1_1 ),
	.COUT(\u_cordic/[8].U/n183_1_1 ),
	.SUM(\u_cordic/[8].U/n183_2 )
);
defparam \u_cordic/[8].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n182_1_s  (
	.I0(\u_cordic/x[8] [2]),
	.I1(\u_cordic/y[8] [10]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n183_1_1 ),
	.COUT(\u_cordic/[8].U/n182_1_1 ),
	.SUM(\u_cordic/[8].U/n182_2 )
);
defparam \u_cordic/[8].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n181_1_s  (
	.I0(\u_cordic/x[8] [3]),
	.I1(\u_cordic/y[8] [11]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n182_1_1 ),
	.COUT(\u_cordic/[8].U/n181_1_1 ),
	.SUM(\u_cordic/[8].U/n181_2 )
);
defparam \u_cordic/[8].U/n181_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n180_1_s  (
	.I0(\u_cordic/x[8] [4]),
	.I1(\u_cordic/y[8] [12]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n181_1_1 ),
	.COUT(\u_cordic/[8].U/n180_1_1 ),
	.SUM(\u_cordic/[8].U/n180_2 )
);
defparam \u_cordic/[8].U/n180_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n179_1_s  (
	.I0(\u_cordic/x[8] [5]),
	.I1(\u_cordic/y[8] [13]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n180_1_1 ),
	.COUT(\u_cordic/[8].U/n179_1_1 ),
	.SUM(\u_cordic/[8].U/n179_2 )
);
defparam \u_cordic/[8].U/n179_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n178_1_s  (
	.I0(\u_cordic/x[8] [6]),
	.I1(\u_cordic/y[8] [14]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n179_1_1 ),
	.COUT(\u_cordic/[8].U/n178_1_1 ),
	.SUM(\u_cordic/[8].U/n178_2 )
);
defparam \u_cordic/[8].U/n178_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n177_1_s  (
	.I0(\u_cordic/x[8] [7]),
	.I1(\u_cordic/y[8] [15]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n178_1_1 ),
	.COUT(\u_cordic/[8].U/n177_1_1 ),
	.SUM(\u_cordic/[8].U/n177_2 )
);
defparam \u_cordic/[8].U/n177_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n176_1_s  (
	.I0(\u_cordic/x[8] [8]),
	.I1(\u_cordic/y[8] [16]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n177_1_1 ),
	.COUT(\u_cordic/[8].U/n176_1_1 ),
	.SUM(\u_cordic/[8].U/n176_2 )
);
defparam \u_cordic/[8].U/n176_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n175_1_s  (
	.I0(\u_cordic/x[8] [9]),
	.I1(\u_cordic/y[8] [16]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n176_1_1 ),
	.COUT(\u_cordic/[8].U/n175_1_1 ),
	.SUM(\u_cordic/[8].U/n175_2 )
);
defparam \u_cordic/[8].U/n175_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n174_1_s  (
	.I0(\u_cordic/x[8] [10]),
	.I1(\u_cordic/y[8] [16]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n175_1_1 ),
	.COUT(\u_cordic/[8].U/n174_1_1 ),
	.SUM(\u_cordic/[8].U/n174_2 )
);
defparam \u_cordic/[8].U/n174_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n173_1_s  (
	.I0(\u_cordic/x[8] [11]),
	.I1(\u_cordic/y[8] [16]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n174_1_1 ),
	.COUT(\u_cordic/[8].U/n173_1_1 ),
	.SUM(\u_cordic/[8].U/n173_2 )
);
defparam \u_cordic/[8].U/n173_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n172_1_s  (
	.I0(\u_cordic/x[8] [12]),
	.I1(\u_cordic/y[8] [16]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n173_1_1 ),
	.COUT(\u_cordic/[8].U/n172_1_1 ),
	.SUM(\u_cordic/[8].U/n172_2 )
);
defparam \u_cordic/[8].U/n172_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n171_1_s  (
	.I0(\u_cordic/x[8] [13]),
	.I1(\u_cordic/y[8] [16]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n172_1_1 ),
	.COUT(\u_cordic/[8].U/n171_1_1 ),
	.SUM(\u_cordic/[8].U/n171_2 )
);
defparam \u_cordic/[8].U/n171_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n170_1_s  (
	.I0(\u_cordic/x[8] [14]),
	.I1(\u_cordic/y[8] [16]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n171_1_1 ),
	.COUT(\u_cordic/[8].U/n170_1_1 ),
	.SUM(\u_cordic/[8].U/n170_2 )
);
defparam \u_cordic/[8].U/n170_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n169_1_s  (
	.I0(\u_cordic/x[8] [15]),
	.I1(\u_cordic/y[8] [16]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n170_1_1 ),
	.COUT(\u_cordic/[8].U/n169_1_1 ),
	.SUM(\u_cordic/[8].U/n169_2 )
);
defparam \u_cordic/[8].U/n169_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n168_1_s  (
	.I0(\u_cordic/x[8] [16]),
	.I1(\u_cordic/y[8] [16]),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n169_1_1 ),
	.COUT(\u_cordic/[8].U/n168_1_0_COUT ),
	.SUM(\u_cordic/[8].U/n168_2 )
);
defparam \u_cordic/[8].U/n168_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n211_1_s  (
	.I0(\u_cordic/z[8] [7]),
	.I1(VCC),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/z[8][16]_1_5 ),
	.COUT(\u_cordic/[8].U/n211_1_1 ),
	.SUM(\u_cordic/[8].U/n211_2 )
);
defparam \u_cordic/[8].U/n211_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n210_1_s  (
	.I0(\u_cordic/z[8] [8]),
	.I1(GND),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n211_1_1 ),
	.COUT(\u_cordic/[8].U/n210_1_1 ),
	.SUM(\u_cordic/[8].U/n210_2 )
);
defparam \u_cordic/[8].U/n210_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n209_1_s  (
	.I0(\u_cordic/z[8] [9]),
	.I1(GND),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n210_1_1 ),
	.COUT(\u_cordic/[8].U/n209_1_1 ),
	.SUM(\u_cordic/[8].U/n209_2 )
);
defparam \u_cordic/[8].U/n209_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n208_1_s  (
	.I0(\u_cordic/z[8] [10]),
	.I1(GND),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n209_1_1 ),
	.COUT(\u_cordic/[8].U/n208_1_1 ),
	.SUM(\u_cordic/[8].U/n208_2 )
);
defparam \u_cordic/[8].U/n208_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n207_1_s  (
	.I0(\u_cordic/z[8] [11]),
	.I1(GND),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n208_1_1 ),
	.COUT(\u_cordic/[8].U/n207_1_1 ),
	.SUM(\u_cordic/[8].U/n207_2 )
);
defparam \u_cordic/[8].U/n207_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n206_1_s  (
	.I0(\u_cordic/z[8] [12]),
	.I1(GND),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n207_1_1 ),
	.COUT(\u_cordic/[8].U/n206_1_1 ),
	.SUM(\u_cordic/[8].U/n206_2 )
);
defparam \u_cordic/[8].U/n206_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n205_1_s  (
	.I0(\u_cordic/z[8] [13]),
	.I1(GND),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n206_1_1 ),
	.COUT(\u_cordic/[8].U/n205_1_1 ),
	.SUM(\u_cordic/[8].U/n205_2 )
);
defparam \u_cordic/[8].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n204_1_s  (
	.I0(\u_cordic/z[8] [14]),
	.I1(GND),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n205_1_1 ),
	.COUT(\u_cordic/[8].U/n204_1_1 ),
	.SUM(\u_cordic/[8].U/n204_2 )
);
defparam \u_cordic/[8].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n203_1_s  (
	.I0(\u_cordic/z[8] [15]),
	.I1(GND),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n204_1_1 ),
	.COUT(\u_cordic/[8].U/n203_1_1 ),
	.SUM(\u_cordic/[8].U/n203_2 )
);
defparam \u_cordic/[8].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n202_1_s  (
	.I0(\u_cordic/z[8] [16]),
	.I1(GND),
	.I3(\u_cordic/z[8] [16]),
	.CIN(\u_cordic/[8].U/n203_1_1 ),
	.COUT(\u_cordic/[8].U/n202_1_0_COUT ),
	.SUM(\u_cordic/[8].U/n202_2 )
);
defparam \u_cordic/[8].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n201_1_s  (
	.I0(\u_cordic/y[8] [0]),
	.I1(\u_cordic/x[8] [8]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/z[8] [16]),
	.COUT(\u_cordic/[8].U/n201_1_1 ),
	.SUM(\u_cordic/[8].U/n201_2 )
);
defparam \u_cordic/[8].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n200_1_s  (
	.I0(\u_cordic/y[8] [1]),
	.I1(\u_cordic/x[8] [9]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n201_1_1 ),
	.COUT(\u_cordic/[8].U/n200_1_1 ),
	.SUM(\u_cordic/[8].U/n200_2 )
);
defparam \u_cordic/[8].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n199_1_s  (
	.I0(\u_cordic/y[8] [2]),
	.I1(\u_cordic/x[8] [10]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n200_1_1 ),
	.COUT(\u_cordic/[8].U/n199_1_1 ),
	.SUM(\u_cordic/[8].U/n199_2 )
);
defparam \u_cordic/[8].U/n199_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n198_1_s  (
	.I0(\u_cordic/y[8] [3]),
	.I1(\u_cordic/x[8] [11]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n199_1_1 ),
	.COUT(\u_cordic/[8].U/n198_1_1 ),
	.SUM(\u_cordic/[8].U/n198_2 )
);
defparam \u_cordic/[8].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n197_1_s  (
	.I0(\u_cordic/y[8] [4]),
	.I1(\u_cordic/x[8] [12]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n198_1_1 ),
	.COUT(\u_cordic/[8].U/n197_1_1 ),
	.SUM(\u_cordic/[8].U/n197_2 )
);
defparam \u_cordic/[8].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n196_1_s  (
	.I0(\u_cordic/y[8] [5]),
	.I1(\u_cordic/x[8] [13]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n197_1_1 ),
	.COUT(\u_cordic/[8].U/n196_1_1 ),
	.SUM(\u_cordic/[8].U/n196_2 )
);
defparam \u_cordic/[8].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n195_1_s  (
	.I0(\u_cordic/y[8] [6]),
	.I1(\u_cordic/x[8] [14]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n196_1_1 ),
	.COUT(\u_cordic/[8].U/n195_1_1 ),
	.SUM(\u_cordic/[8].U/n195_2 )
);
defparam \u_cordic/[8].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n194_1_s  (
	.I0(\u_cordic/y[8] [7]),
	.I1(\u_cordic/x[8] [15]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n195_1_1 ),
	.COUT(\u_cordic/[8].U/n194_1_1 ),
	.SUM(\u_cordic/[8].U/n194_2 )
);
defparam \u_cordic/[8].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n193_1_s  (
	.I0(\u_cordic/y[8] [8]),
	.I1(\u_cordic/x[8] [16]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n194_1_1 ),
	.COUT(\u_cordic/[8].U/n193_1_1 ),
	.SUM(\u_cordic/[8].U/n193_2 )
);
defparam \u_cordic/[8].U/n193_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n192_1_s  (
	.I0(\u_cordic/y[8] [9]),
	.I1(\u_cordic/x[8] [16]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n193_1_1 ),
	.COUT(\u_cordic/[8].U/n192_1_1 ),
	.SUM(\u_cordic/[8].U/n192_2 )
);
defparam \u_cordic/[8].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n191_1_s  (
	.I0(\u_cordic/y[8] [10]),
	.I1(\u_cordic/x[8] [16]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n192_1_1 ),
	.COUT(\u_cordic/[8].U/n191_1_1 ),
	.SUM(\u_cordic/[8].U/n191_2 )
);
defparam \u_cordic/[8].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n190_1_s  (
	.I0(\u_cordic/y[8] [11]),
	.I1(\u_cordic/x[8] [16]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n191_1_1 ),
	.COUT(\u_cordic/[8].U/n190_1_1 ),
	.SUM(\u_cordic/[8].U/n190_2 )
);
defparam \u_cordic/[8].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n189_1_s  (
	.I0(\u_cordic/y[8] [12]),
	.I1(\u_cordic/x[8] [16]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n190_1_1 ),
	.COUT(\u_cordic/[8].U/n189_1_1 ),
	.SUM(\u_cordic/[8].U/n189_2 )
);
defparam \u_cordic/[8].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n188_1_s  (
	.I0(\u_cordic/y[8] [13]),
	.I1(\u_cordic/x[8] [16]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n189_1_1 ),
	.COUT(\u_cordic/[8].U/n188_1_1 ),
	.SUM(\u_cordic/[8].U/n188_2 )
);
defparam \u_cordic/[8].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n187_1_s  (
	.I0(\u_cordic/y[8] [14]),
	.I1(\u_cordic/x[8] [16]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n188_1_1 ),
	.COUT(\u_cordic/[8].U/n187_1_1 ),
	.SUM(\u_cordic/[8].U/n187_2 )
);
defparam \u_cordic/[8].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n186_1_s  (
	.I0(\u_cordic/y[8] [15]),
	.I1(\u_cordic/x[8] [16]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n187_1_1 ),
	.COUT(\u_cordic/[8].U/n186_1_1 ),
	.SUM(\u_cordic/[8].U/n186_2 )
);
defparam \u_cordic/[8].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[8].U/n185_1_s  (
	.I0(\u_cordic/y[8] [16]),
	.I1(\u_cordic/x[8] [16]),
	.I3(\u_cordic/z[8][16]_1_5 ),
	.CIN(\u_cordic/[8].U/n186_1_1 ),
	.COUT(\u_cordic/[8].U/n185_1_0_COUT ),
	.SUM(\u_cordic/[8].U/n185_2 )
);
defparam \u_cordic/[8].U/n185_1_s .ALU_MODE=2;
LUT1 \u_cordic/[8].U/z[9][16]_1_s3  (
	.I0(\u_cordic/z[9] [16]),
	.F(\u_cordic/z[9][16]_1_5 )
);
defparam \u_cordic/[8].U/z[9][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[9].U/x_1_15_s0  (
	.D(\u_cordic/[9].U/n171_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [15])
);
defparam \u_cordic/[9].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_14_s0  (
	.D(\u_cordic/[9].U/n172_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [14])
);
defparam \u_cordic/[9].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_13_s0  (
	.D(\u_cordic/[9].U/n173_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [13])
);
defparam \u_cordic/[9].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_12_s0  (
	.D(\u_cordic/[9].U/n174_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [12])
);
defparam \u_cordic/[9].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_11_s0  (
	.D(\u_cordic/[9].U/n175_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [11])
);
defparam \u_cordic/[9].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_10_s0  (
	.D(\u_cordic/[9].U/n176_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [10])
);
defparam \u_cordic/[9].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_9_s0  (
	.D(\u_cordic/[9].U/n177_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [9])
);
defparam \u_cordic/[9].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_8_s0  (
	.D(\u_cordic/[9].U/n178_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [8])
);
defparam \u_cordic/[9].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_7_s0  (
	.D(\u_cordic/[9].U/n179_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [7])
);
defparam \u_cordic/[9].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_6_s0  (
	.D(\u_cordic/[9].U/n180_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [6])
);
defparam \u_cordic/[9].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_5_s0  (
	.D(\u_cordic/[9].U/n181_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [5])
);
defparam \u_cordic/[9].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_4_s0  (
	.D(\u_cordic/[9].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [4])
);
defparam \u_cordic/[9].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_3_s0  (
	.D(\u_cordic/[9].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [3])
);
defparam \u_cordic/[9].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_2_s0  (
	.D(\u_cordic/[9].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [2])
);
defparam \u_cordic/[9].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_1_s0  (
	.D(\u_cordic/[9].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [1])
);
defparam \u_cordic/[9].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_0_s0  (
	.D(\u_cordic/[9].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [0])
);
defparam \u_cordic/[9].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_16_s0  (
	.D(\u_cordic/[9].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [16])
);
defparam \u_cordic/[9].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_15_s0  (
	.D(\u_cordic/[9].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [15])
);
defparam \u_cordic/[9].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_14_s0  (
	.D(\u_cordic/[9].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [14])
);
defparam \u_cordic/[9].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_13_s0  (
	.D(\u_cordic/[9].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [13])
);
defparam \u_cordic/[9].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_12_s0  (
	.D(\u_cordic/[9].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [12])
);
defparam \u_cordic/[9].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_11_s0  (
	.D(\u_cordic/[9].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [11])
);
defparam \u_cordic/[9].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_10_s0  (
	.D(\u_cordic/[9].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [10])
);
defparam \u_cordic/[9].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_9_s0  (
	.D(\u_cordic/[9].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [9])
);
defparam \u_cordic/[9].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_8_s0  (
	.D(\u_cordic/[9].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [8])
);
defparam \u_cordic/[9].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_7_s0  (
	.D(\u_cordic/[9].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [7])
);
defparam \u_cordic/[9].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_6_s0  (
	.D(\u_cordic/[9].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [6])
);
defparam \u_cordic/[9].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_5_s0  (
	.D(\u_cordic/[9].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [5])
);
defparam \u_cordic/[9].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_4_s0  (
	.D(\u_cordic/[9].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [4])
);
defparam \u_cordic/[9].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_3_s0  (
	.D(\u_cordic/[9].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [3])
);
defparam \u_cordic/[9].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_2_s0  (
	.D(\u_cordic/[9].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [2])
);
defparam \u_cordic/[9].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_1_s0  (
	.D(\u_cordic/[9].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [1])
);
defparam \u_cordic/[9].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/y_1_0_s0  (
	.D(\u_cordic/[9].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[10] [0])
);
defparam \u_cordic/[9].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_16_s0  (
	.D(\u_cordic/[9].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [16])
);
defparam \u_cordic/[9].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_15_s0  (
	.D(\u_cordic/[9].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [15])
);
defparam \u_cordic/[9].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_14_s0  (
	.D(\u_cordic/[9].U/n206_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [14])
);
defparam \u_cordic/[9].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_13_s0  (
	.D(\u_cordic/[9].U/n207_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [13])
);
defparam \u_cordic/[9].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_12_s0  (
	.D(\u_cordic/[9].U/n208_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [12])
);
defparam \u_cordic/[9].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_11_s0  (
	.D(\u_cordic/[9].U/n209_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [11])
);
defparam \u_cordic/[9].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_10_s0  (
	.D(\u_cordic/[9].U/n210_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [10])
);
defparam \u_cordic/[9].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_9_s0  (
	.D(\u_cordic/[9].U/n211_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [9])
);
defparam \u_cordic/[9].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_8_s0  (
	.D(\u_cordic/[9].U/n212_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [8])
);
defparam \u_cordic/[9].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_7_s0  (
	.D(\u_cordic/[9].U/n213_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [7])
);
defparam \u_cordic/[9].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_6_s0  (
	.D(\u_cordic/[9].U/n214_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [6])
);
defparam \u_cordic/[9].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_5_s0  (
	.D(\u_cordic/z[9] [5]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [5])
);
defparam \u_cordic/[9].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_4_s0  (
	.D(\u_cordic/z[9] [4]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [4])
);
defparam \u_cordic/[9].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_3_s0  (
	.D(\u_cordic/z[9] [3]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [3])
);
defparam \u_cordic/[9].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_2_s0  (
	.D(\u_cordic/z[9] [2]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [2])
);
defparam \u_cordic/[9].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_1_s0  (
	.D(\u_cordic/z[9] [1]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [1])
);
defparam \u_cordic/[9].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/z_1_0_s0  (
	.D(\u_cordic/z[9] [0]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[10] [0])
);
defparam \u_cordic/[9].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[9].U/x_1_16_s0  (
	.D(\u_cordic/[9].U/n170_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[10] [16])
);
defparam \u_cordic/[9].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[9].U/n186_1_s  (
	.I0(\u_cordic/x[9] [0]),
	.I1(\u_cordic/y[9] [9]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/z[9][16]_1_5 ),
	.COUT(\u_cordic/[9].U/n186_1_1 ),
	.SUM(\u_cordic/[9].U/n186_2 )
);
defparam \u_cordic/[9].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n185_1_s  (
	.I0(\u_cordic/x[9] [1]),
	.I1(\u_cordic/y[9] [10]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n186_1_1 ),
	.COUT(\u_cordic/[9].U/n185_1_1 ),
	.SUM(\u_cordic/[9].U/n185_2 )
);
defparam \u_cordic/[9].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n184_1_s  (
	.I0(\u_cordic/x[9] [2]),
	.I1(\u_cordic/y[9] [11]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n185_1_1 ),
	.COUT(\u_cordic/[9].U/n184_1_1 ),
	.SUM(\u_cordic/[9].U/n184_2 )
);
defparam \u_cordic/[9].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n183_1_s  (
	.I0(\u_cordic/x[9] [3]),
	.I1(\u_cordic/y[9] [12]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n184_1_1 ),
	.COUT(\u_cordic/[9].U/n183_1_1 ),
	.SUM(\u_cordic/[9].U/n183_2 )
);
defparam \u_cordic/[9].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n182_1_s  (
	.I0(\u_cordic/x[9] [4]),
	.I1(\u_cordic/y[9] [13]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n183_1_1 ),
	.COUT(\u_cordic/[9].U/n182_1_1 ),
	.SUM(\u_cordic/[9].U/n182_2 )
);
defparam \u_cordic/[9].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n181_1_s  (
	.I0(\u_cordic/x[9] [5]),
	.I1(\u_cordic/y[9] [14]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n182_1_1 ),
	.COUT(\u_cordic/[9].U/n181_1_1 ),
	.SUM(\u_cordic/[9].U/n181_2 )
);
defparam \u_cordic/[9].U/n181_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n180_1_s  (
	.I0(\u_cordic/x[9] [6]),
	.I1(\u_cordic/y[9] [15]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n181_1_1 ),
	.COUT(\u_cordic/[9].U/n180_1_1 ),
	.SUM(\u_cordic/[9].U/n180_2 )
);
defparam \u_cordic/[9].U/n180_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n179_1_s  (
	.I0(\u_cordic/x[9] [7]),
	.I1(\u_cordic/y[9] [16]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n180_1_1 ),
	.COUT(\u_cordic/[9].U/n179_1_1 ),
	.SUM(\u_cordic/[9].U/n179_2 )
);
defparam \u_cordic/[9].U/n179_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n178_1_s  (
	.I0(\u_cordic/x[9] [8]),
	.I1(\u_cordic/y[9] [16]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n179_1_1 ),
	.COUT(\u_cordic/[9].U/n178_1_1 ),
	.SUM(\u_cordic/[9].U/n178_2 )
);
defparam \u_cordic/[9].U/n178_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n177_1_s  (
	.I0(\u_cordic/x[9] [9]),
	.I1(\u_cordic/y[9] [16]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n178_1_1 ),
	.COUT(\u_cordic/[9].U/n177_1_1 ),
	.SUM(\u_cordic/[9].U/n177_2 )
);
defparam \u_cordic/[9].U/n177_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n176_1_s  (
	.I0(\u_cordic/x[9] [10]),
	.I1(\u_cordic/y[9] [16]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n177_1_1 ),
	.COUT(\u_cordic/[9].U/n176_1_1 ),
	.SUM(\u_cordic/[9].U/n176_2 )
);
defparam \u_cordic/[9].U/n176_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n175_1_s  (
	.I0(\u_cordic/x[9] [11]),
	.I1(\u_cordic/y[9] [16]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n176_1_1 ),
	.COUT(\u_cordic/[9].U/n175_1_1 ),
	.SUM(\u_cordic/[9].U/n175_2 )
);
defparam \u_cordic/[9].U/n175_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n174_1_s  (
	.I0(\u_cordic/x[9] [12]),
	.I1(\u_cordic/y[9] [16]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n175_1_1 ),
	.COUT(\u_cordic/[9].U/n174_1_1 ),
	.SUM(\u_cordic/[9].U/n174_2 )
);
defparam \u_cordic/[9].U/n174_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n173_1_s  (
	.I0(\u_cordic/x[9] [13]),
	.I1(\u_cordic/y[9] [16]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n174_1_1 ),
	.COUT(\u_cordic/[9].U/n173_1_1 ),
	.SUM(\u_cordic/[9].U/n173_2 )
);
defparam \u_cordic/[9].U/n173_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n172_1_s  (
	.I0(\u_cordic/x[9] [14]),
	.I1(\u_cordic/y[9] [16]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n173_1_1 ),
	.COUT(\u_cordic/[9].U/n172_1_1 ),
	.SUM(\u_cordic/[9].U/n172_2 )
);
defparam \u_cordic/[9].U/n172_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n171_1_s  (
	.I0(\u_cordic/x[9] [15]),
	.I1(\u_cordic/y[9] [16]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n172_1_1 ),
	.COUT(\u_cordic/[9].U/n171_1_1 ),
	.SUM(\u_cordic/[9].U/n171_2 )
);
defparam \u_cordic/[9].U/n171_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n170_1_s  (
	.I0(\u_cordic/x[9] [16]),
	.I1(\u_cordic/y[9] [16]),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n171_1_1 ),
	.COUT(\u_cordic/[9].U/n170_1_0_COUT ),
	.SUM(\u_cordic/[9].U/n170_2 )
);
defparam \u_cordic/[9].U/n170_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n214_1_s  (
	.I0(\u_cordic/z[9] [6]),
	.I1(VCC),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/z[9][16]_1_5 ),
	.COUT(\u_cordic/[9].U/n214_1_1 ),
	.SUM(\u_cordic/[9].U/n214_2 )
);
defparam \u_cordic/[9].U/n214_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n213_1_s  (
	.I0(\u_cordic/z[9] [7]),
	.I1(GND),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n214_1_1 ),
	.COUT(\u_cordic/[9].U/n213_1_1 ),
	.SUM(\u_cordic/[9].U/n213_2 )
);
defparam \u_cordic/[9].U/n213_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n212_1_s  (
	.I0(\u_cordic/z[9] [8]),
	.I1(GND),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n213_1_1 ),
	.COUT(\u_cordic/[9].U/n212_1_1 ),
	.SUM(\u_cordic/[9].U/n212_2 )
);
defparam \u_cordic/[9].U/n212_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n211_1_s  (
	.I0(\u_cordic/z[9] [9]),
	.I1(GND),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n212_1_1 ),
	.COUT(\u_cordic/[9].U/n211_1_1 ),
	.SUM(\u_cordic/[9].U/n211_2 )
);
defparam \u_cordic/[9].U/n211_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n210_1_s  (
	.I0(\u_cordic/z[9] [10]),
	.I1(GND),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n211_1_1 ),
	.COUT(\u_cordic/[9].U/n210_1_1 ),
	.SUM(\u_cordic/[9].U/n210_2 )
);
defparam \u_cordic/[9].U/n210_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n209_1_s  (
	.I0(\u_cordic/z[9] [11]),
	.I1(GND),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n210_1_1 ),
	.COUT(\u_cordic/[9].U/n209_1_1 ),
	.SUM(\u_cordic/[9].U/n209_2 )
);
defparam \u_cordic/[9].U/n209_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n208_1_s  (
	.I0(\u_cordic/z[9] [12]),
	.I1(GND),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n209_1_1 ),
	.COUT(\u_cordic/[9].U/n208_1_1 ),
	.SUM(\u_cordic/[9].U/n208_2 )
);
defparam \u_cordic/[9].U/n208_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n207_1_s  (
	.I0(\u_cordic/z[9] [13]),
	.I1(GND),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n208_1_1 ),
	.COUT(\u_cordic/[9].U/n207_1_1 ),
	.SUM(\u_cordic/[9].U/n207_2 )
);
defparam \u_cordic/[9].U/n207_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n206_1_s  (
	.I0(\u_cordic/z[9] [14]),
	.I1(GND),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n207_1_1 ),
	.COUT(\u_cordic/[9].U/n206_1_1 ),
	.SUM(\u_cordic/[9].U/n206_2 )
);
defparam \u_cordic/[9].U/n206_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n205_1_s  (
	.I0(\u_cordic/z[9] [15]),
	.I1(GND),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n206_1_1 ),
	.COUT(\u_cordic/[9].U/n205_1_1 ),
	.SUM(\u_cordic/[9].U/n205_2 )
);
defparam \u_cordic/[9].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n204_1_s  (
	.I0(\u_cordic/z[9] [16]),
	.I1(GND),
	.I3(\u_cordic/z[9] [16]),
	.CIN(\u_cordic/[9].U/n205_1_1 ),
	.COUT(\u_cordic/[9].U/n204_1_0_COUT ),
	.SUM(\u_cordic/[9].U/n204_2 )
);
defparam \u_cordic/[9].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n203_1_s  (
	.I0(\u_cordic/y[9] [0]),
	.I1(\u_cordic/x[9] [9]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/z[9] [16]),
	.COUT(\u_cordic/[9].U/n203_1_1 ),
	.SUM(\u_cordic/[9].U/n203_2 )
);
defparam \u_cordic/[9].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n202_1_s  (
	.I0(\u_cordic/y[9] [1]),
	.I1(\u_cordic/x[9] [10]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n203_1_1 ),
	.COUT(\u_cordic/[9].U/n202_1_1 ),
	.SUM(\u_cordic/[9].U/n202_2 )
);
defparam \u_cordic/[9].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n201_1_s  (
	.I0(\u_cordic/y[9] [2]),
	.I1(\u_cordic/x[9] [11]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n202_1_1 ),
	.COUT(\u_cordic/[9].U/n201_1_1 ),
	.SUM(\u_cordic/[9].U/n201_2 )
);
defparam \u_cordic/[9].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n200_1_s  (
	.I0(\u_cordic/y[9] [3]),
	.I1(\u_cordic/x[9] [12]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n201_1_1 ),
	.COUT(\u_cordic/[9].U/n200_1_1 ),
	.SUM(\u_cordic/[9].U/n200_2 )
);
defparam \u_cordic/[9].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n199_1_s  (
	.I0(\u_cordic/y[9] [4]),
	.I1(\u_cordic/x[9] [13]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n200_1_1 ),
	.COUT(\u_cordic/[9].U/n199_1_1 ),
	.SUM(\u_cordic/[9].U/n199_2 )
);
defparam \u_cordic/[9].U/n199_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n198_1_s  (
	.I0(\u_cordic/y[9] [5]),
	.I1(\u_cordic/x[9] [14]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n199_1_1 ),
	.COUT(\u_cordic/[9].U/n198_1_1 ),
	.SUM(\u_cordic/[9].U/n198_2 )
);
defparam \u_cordic/[9].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n197_1_s  (
	.I0(\u_cordic/y[9] [6]),
	.I1(\u_cordic/x[9] [15]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n198_1_1 ),
	.COUT(\u_cordic/[9].U/n197_1_1 ),
	.SUM(\u_cordic/[9].U/n197_2 )
);
defparam \u_cordic/[9].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n196_1_s  (
	.I0(\u_cordic/y[9] [7]),
	.I1(\u_cordic/x[9] [16]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n197_1_1 ),
	.COUT(\u_cordic/[9].U/n196_1_1 ),
	.SUM(\u_cordic/[9].U/n196_2 )
);
defparam \u_cordic/[9].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n195_1_s  (
	.I0(\u_cordic/y[9] [8]),
	.I1(\u_cordic/x[9] [16]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n196_1_1 ),
	.COUT(\u_cordic/[9].U/n195_1_1 ),
	.SUM(\u_cordic/[9].U/n195_2 )
);
defparam \u_cordic/[9].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n194_1_s  (
	.I0(\u_cordic/y[9] [9]),
	.I1(\u_cordic/x[9] [16]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n195_1_1 ),
	.COUT(\u_cordic/[9].U/n194_1_1 ),
	.SUM(\u_cordic/[9].U/n194_2 )
);
defparam \u_cordic/[9].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n193_1_s  (
	.I0(\u_cordic/y[9] [10]),
	.I1(\u_cordic/x[9] [16]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n194_1_1 ),
	.COUT(\u_cordic/[9].U/n193_1_1 ),
	.SUM(\u_cordic/[9].U/n193_2 )
);
defparam \u_cordic/[9].U/n193_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n192_1_s  (
	.I0(\u_cordic/y[9] [11]),
	.I1(\u_cordic/x[9] [16]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n193_1_1 ),
	.COUT(\u_cordic/[9].U/n192_1_1 ),
	.SUM(\u_cordic/[9].U/n192_2 )
);
defparam \u_cordic/[9].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n191_1_s  (
	.I0(\u_cordic/y[9] [12]),
	.I1(\u_cordic/x[9] [16]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n192_1_1 ),
	.COUT(\u_cordic/[9].U/n191_1_1 ),
	.SUM(\u_cordic/[9].U/n191_2 )
);
defparam \u_cordic/[9].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n190_1_s  (
	.I0(\u_cordic/y[9] [13]),
	.I1(\u_cordic/x[9] [16]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n191_1_1 ),
	.COUT(\u_cordic/[9].U/n190_1_1 ),
	.SUM(\u_cordic/[9].U/n190_2 )
);
defparam \u_cordic/[9].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n189_1_s  (
	.I0(\u_cordic/y[9] [14]),
	.I1(\u_cordic/x[9] [16]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n190_1_1 ),
	.COUT(\u_cordic/[9].U/n189_1_1 ),
	.SUM(\u_cordic/[9].U/n189_2 )
);
defparam \u_cordic/[9].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n188_1_s  (
	.I0(\u_cordic/y[9] [15]),
	.I1(\u_cordic/x[9] [16]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n189_1_1 ),
	.COUT(\u_cordic/[9].U/n188_1_1 ),
	.SUM(\u_cordic/[9].U/n188_2 )
);
defparam \u_cordic/[9].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[9].U/n187_1_s  (
	.I0(\u_cordic/y[9] [16]),
	.I1(\u_cordic/x[9] [16]),
	.I3(\u_cordic/z[9][16]_1_5 ),
	.CIN(\u_cordic/[9].U/n188_1_1 ),
	.COUT(\u_cordic/[9].U/n187_1_0_COUT ),
	.SUM(\u_cordic/[9].U/n187_2 )
);
defparam \u_cordic/[9].U/n187_1_s .ALU_MODE=2;
LUT1 \u_cordic/[9].U/z[10][16]_1_s3  (
	.I0(\u_cordic/z[10] [16]),
	.F(\u_cordic/z[10][16]_1_5 )
);
defparam \u_cordic/[9].U/z[10][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[10].U/x_1_15_s0  (
	.D(\u_cordic/[10].U/n173_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [15])
);
defparam \u_cordic/[10].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_14_s0  (
	.D(\u_cordic/[10].U/n174_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [14])
);
defparam \u_cordic/[10].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_13_s0  (
	.D(\u_cordic/[10].U/n175_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [13])
);
defparam \u_cordic/[10].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_12_s0  (
	.D(\u_cordic/[10].U/n176_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [12])
);
defparam \u_cordic/[10].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_11_s0  (
	.D(\u_cordic/[10].U/n177_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [11])
);
defparam \u_cordic/[10].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_10_s0  (
	.D(\u_cordic/[10].U/n178_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [10])
);
defparam \u_cordic/[10].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_9_s0  (
	.D(\u_cordic/[10].U/n179_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [9])
);
defparam \u_cordic/[10].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_8_s0  (
	.D(\u_cordic/[10].U/n180_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [8])
);
defparam \u_cordic/[10].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_7_s0  (
	.D(\u_cordic/[10].U/n181_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [7])
);
defparam \u_cordic/[10].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_6_s0  (
	.D(\u_cordic/[10].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [6])
);
defparam \u_cordic/[10].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_5_s0  (
	.D(\u_cordic/[10].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [5])
);
defparam \u_cordic/[10].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_4_s0  (
	.D(\u_cordic/[10].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [4])
);
defparam \u_cordic/[10].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_3_s0  (
	.D(\u_cordic/[10].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [3])
);
defparam \u_cordic/[10].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_2_s0  (
	.D(\u_cordic/[10].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [2])
);
defparam \u_cordic/[10].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_1_s0  (
	.D(\u_cordic/[10].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [1])
);
defparam \u_cordic/[10].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_0_s0  (
	.D(\u_cordic/[10].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [0])
);
defparam \u_cordic/[10].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_16_s0  (
	.D(\u_cordic/[10].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [16])
);
defparam \u_cordic/[10].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_15_s0  (
	.D(\u_cordic/[10].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [15])
);
defparam \u_cordic/[10].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_14_s0  (
	.D(\u_cordic/[10].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [14])
);
defparam \u_cordic/[10].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_13_s0  (
	.D(\u_cordic/[10].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [13])
);
defparam \u_cordic/[10].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_12_s0  (
	.D(\u_cordic/[10].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [12])
);
defparam \u_cordic/[10].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_11_s0  (
	.D(\u_cordic/[10].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [11])
);
defparam \u_cordic/[10].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_10_s0  (
	.D(\u_cordic/[10].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [10])
);
defparam \u_cordic/[10].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_9_s0  (
	.D(\u_cordic/[10].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [9])
);
defparam \u_cordic/[10].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_8_s0  (
	.D(\u_cordic/[10].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [8])
);
defparam \u_cordic/[10].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_7_s0  (
	.D(\u_cordic/[10].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [7])
);
defparam \u_cordic/[10].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_6_s0  (
	.D(\u_cordic/[10].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [6])
);
defparam \u_cordic/[10].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_5_s0  (
	.D(\u_cordic/[10].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [5])
);
defparam \u_cordic/[10].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_4_s0  (
	.D(\u_cordic/[10].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [4])
);
defparam \u_cordic/[10].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_3_s0  (
	.D(\u_cordic/[10].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [3])
);
defparam \u_cordic/[10].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_2_s0  (
	.D(\u_cordic/[10].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [2])
);
defparam \u_cordic/[10].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_1_s0  (
	.D(\u_cordic/[10].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [1])
);
defparam \u_cordic/[10].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/y_1_0_s0  (
	.D(\u_cordic/[10].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[11] [0])
);
defparam \u_cordic/[10].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_16_s0  (
	.D(\u_cordic/[10].U/n206_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [16])
);
defparam \u_cordic/[10].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_15_s0  (
	.D(\u_cordic/[10].U/n207_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [15])
);
defparam \u_cordic/[10].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_14_s0  (
	.D(\u_cordic/[10].U/n208_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [14])
);
defparam \u_cordic/[10].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_13_s0  (
	.D(\u_cordic/[10].U/n209_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [13])
);
defparam \u_cordic/[10].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_12_s0  (
	.D(\u_cordic/[10].U/n210_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [12])
);
defparam \u_cordic/[10].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_11_s0  (
	.D(\u_cordic/[10].U/n211_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [11])
);
defparam \u_cordic/[10].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_10_s0  (
	.D(\u_cordic/[10].U/n212_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [10])
);
defparam \u_cordic/[10].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_9_s0  (
	.D(\u_cordic/[10].U/n213_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [9])
);
defparam \u_cordic/[10].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_8_s0  (
	.D(\u_cordic/[10].U/n214_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [8])
);
defparam \u_cordic/[10].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_7_s0  (
	.D(\u_cordic/[10].U/n215_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [7])
);
defparam \u_cordic/[10].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_6_s0  (
	.D(\u_cordic/[10].U/n216_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [6])
);
defparam \u_cordic/[10].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_5_s0  (
	.D(\u_cordic/[10].U/n217_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [5])
);
defparam \u_cordic/[10].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_4_s0  (
	.D(\u_cordic/z[10] [4]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [4])
);
defparam \u_cordic/[10].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_3_s0  (
	.D(\u_cordic/z[10] [3]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [3])
);
defparam \u_cordic/[10].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_2_s0  (
	.D(\u_cordic/z[10] [2]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [2])
);
defparam \u_cordic/[10].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_1_s0  (
	.D(\u_cordic/z[10] [1]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [1])
);
defparam \u_cordic/[10].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/z_1_0_s0  (
	.D(\u_cordic/z[10] [0]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[11] [0])
);
defparam \u_cordic/[10].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[10].U/x_1_16_s0  (
	.D(\u_cordic/[10].U/n172_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[11] [16])
);
defparam \u_cordic/[10].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[10].U/n188_1_s  (
	.I0(\u_cordic/x[10] [0]),
	.I1(\u_cordic/y[10] [10]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/z[10][16]_1_5 ),
	.COUT(\u_cordic/[10].U/n188_1_1 ),
	.SUM(\u_cordic/[10].U/n188_2 )
);
defparam \u_cordic/[10].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n187_1_s  (
	.I0(\u_cordic/x[10] [1]),
	.I1(\u_cordic/y[10] [11]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n188_1_1 ),
	.COUT(\u_cordic/[10].U/n187_1_1 ),
	.SUM(\u_cordic/[10].U/n187_2 )
);
defparam \u_cordic/[10].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n186_1_s  (
	.I0(\u_cordic/x[10] [2]),
	.I1(\u_cordic/y[10] [12]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n187_1_1 ),
	.COUT(\u_cordic/[10].U/n186_1_1 ),
	.SUM(\u_cordic/[10].U/n186_2 )
);
defparam \u_cordic/[10].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n185_1_s  (
	.I0(\u_cordic/x[10] [3]),
	.I1(\u_cordic/y[10] [13]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n186_1_1 ),
	.COUT(\u_cordic/[10].U/n185_1_1 ),
	.SUM(\u_cordic/[10].U/n185_2 )
);
defparam \u_cordic/[10].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n184_1_s  (
	.I0(\u_cordic/x[10] [4]),
	.I1(\u_cordic/y[10] [14]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n185_1_1 ),
	.COUT(\u_cordic/[10].U/n184_1_1 ),
	.SUM(\u_cordic/[10].U/n184_2 )
);
defparam \u_cordic/[10].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n183_1_s  (
	.I0(\u_cordic/x[10] [5]),
	.I1(\u_cordic/y[10] [15]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n184_1_1 ),
	.COUT(\u_cordic/[10].U/n183_1_1 ),
	.SUM(\u_cordic/[10].U/n183_2 )
);
defparam \u_cordic/[10].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n182_1_s  (
	.I0(\u_cordic/x[10] [6]),
	.I1(\u_cordic/y[10] [16]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n183_1_1 ),
	.COUT(\u_cordic/[10].U/n182_1_1 ),
	.SUM(\u_cordic/[10].U/n182_2 )
);
defparam \u_cordic/[10].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n181_1_s  (
	.I0(\u_cordic/x[10] [7]),
	.I1(\u_cordic/y[10] [16]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n182_1_1 ),
	.COUT(\u_cordic/[10].U/n181_1_1 ),
	.SUM(\u_cordic/[10].U/n181_2 )
);
defparam \u_cordic/[10].U/n181_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n180_1_s  (
	.I0(\u_cordic/x[10] [8]),
	.I1(\u_cordic/y[10] [16]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n181_1_1 ),
	.COUT(\u_cordic/[10].U/n180_1_1 ),
	.SUM(\u_cordic/[10].U/n180_2 )
);
defparam \u_cordic/[10].U/n180_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n179_1_s  (
	.I0(\u_cordic/x[10] [9]),
	.I1(\u_cordic/y[10] [16]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n180_1_1 ),
	.COUT(\u_cordic/[10].U/n179_1_1 ),
	.SUM(\u_cordic/[10].U/n179_2 )
);
defparam \u_cordic/[10].U/n179_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n178_1_s  (
	.I0(\u_cordic/x[10] [10]),
	.I1(\u_cordic/y[10] [16]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n179_1_1 ),
	.COUT(\u_cordic/[10].U/n178_1_1 ),
	.SUM(\u_cordic/[10].U/n178_2 )
);
defparam \u_cordic/[10].U/n178_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n177_1_s  (
	.I0(\u_cordic/x[10] [11]),
	.I1(\u_cordic/y[10] [16]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n178_1_1 ),
	.COUT(\u_cordic/[10].U/n177_1_1 ),
	.SUM(\u_cordic/[10].U/n177_2 )
);
defparam \u_cordic/[10].U/n177_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n176_1_s  (
	.I0(\u_cordic/x[10] [12]),
	.I1(\u_cordic/y[10] [16]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n177_1_1 ),
	.COUT(\u_cordic/[10].U/n176_1_1 ),
	.SUM(\u_cordic/[10].U/n176_2 )
);
defparam \u_cordic/[10].U/n176_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n175_1_s  (
	.I0(\u_cordic/x[10] [13]),
	.I1(\u_cordic/y[10] [16]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n176_1_1 ),
	.COUT(\u_cordic/[10].U/n175_1_1 ),
	.SUM(\u_cordic/[10].U/n175_2 )
);
defparam \u_cordic/[10].U/n175_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n174_1_s  (
	.I0(\u_cordic/x[10] [14]),
	.I1(\u_cordic/y[10] [16]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n175_1_1 ),
	.COUT(\u_cordic/[10].U/n174_1_1 ),
	.SUM(\u_cordic/[10].U/n174_2 )
);
defparam \u_cordic/[10].U/n174_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n173_1_s  (
	.I0(\u_cordic/x[10] [15]),
	.I1(\u_cordic/y[10] [16]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n174_1_1 ),
	.COUT(\u_cordic/[10].U/n173_1_1 ),
	.SUM(\u_cordic/[10].U/n173_2 )
);
defparam \u_cordic/[10].U/n173_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n172_1_s  (
	.I0(\u_cordic/x[10] [16]),
	.I1(\u_cordic/y[10] [16]),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n173_1_1 ),
	.COUT(\u_cordic/[10].U/n172_1_0_COUT ),
	.SUM(\u_cordic/[10].U/n172_2 )
);
defparam \u_cordic/[10].U/n172_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n217_1_s  (
	.I0(\u_cordic/z[10] [5]),
	.I1(VCC),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/z[10][16]_1_5 ),
	.COUT(\u_cordic/[10].U/n217_1_1 ),
	.SUM(\u_cordic/[10].U/n217_2 )
);
defparam \u_cordic/[10].U/n217_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n216_1_s  (
	.I0(\u_cordic/z[10] [6]),
	.I1(GND),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n217_1_1 ),
	.COUT(\u_cordic/[10].U/n216_1_1 ),
	.SUM(\u_cordic/[10].U/n216_2 )
);
defparam \u_cordic/[10].U/n216_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n215_1_s  (
	.I0(\u_cordic/z[10] [7]),
	.I1(GND),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n216_1_1 ),
	.COUT(\u_cordic/[10].U/n215_1_1 ),
	.SUM(\u_cordic/[10].U/n215_2 )
);
defparam \u_cordic/[10].U/n215_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n214_1_s  (
	.I0(\u_cordic/z[10] [8]),
	.I1(GND),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n215_1_1 ),
	.COUT(\u_cordic/[10].U/n214_1_1 ),
	.SUM(\u_cordic/[10].U/n214_2 )
);
defparam \u_cordic/[10].U/n214_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n213_1_s  (
	.I0(\u_cordic/z[10] [9]),
	.I1(GND),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n214_1_1 ),
	.COUT(\u_cordic/[10].U/n213_1_1 ),
	.SUM(\u_cordic/[10].U/n213_2 )
);
defparam \u_cordic/[10].U/n213_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n212_1_s  (
	.I0(\u_cordic/z[10] [10]),
	.I1(GND),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n213_1_1 ),
	.COUT(\u_cordic/[10].U/n212_1_1 ),
	.SUM(\u_cordic/[10].U/n212_2 )
);
defparam \u_cordic/[10].U/n212_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n211_1_s  (
	.I0(\u_cordic/z[10] [11]),
	.I1(GND),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n212_1_1 ),
	.COUT(\u_cordic/[10].U/n211_1_1 ),
	.SUM(\u_cordic/[10].U/n211_2 )
);
defparam \u_cordic/[10].U/n211_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n210_1_s  (
	.I0(\u_cordic/z[10] [12]),
	.I1(GND),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n211_1_1 ),
	.COUT(\u_cordic/[10].U/n210_1_1 ),
	.SUM(\u_cordic/[10].U/n210_2 )
);
defparam \u_cordic/[10].U/n210_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n209_1_s  (
	.I0(\u_cordic/z[10] [13]),
	.I1(GND),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n210_1_1 ),
	.COUT(\u_cordic/[10].U/n209_1_1 ),
	.SUM(\u_cordic/[10].U/n209_2 )
);
defparam \u_cordic/[10].U/n209_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n208_1_s  (
	.I0(\u_cordic/z[10] [14]),
	.I1(GND),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n209_1_1 ),
	.COUT(\u_cordic/[10].U/n208_1_1 ),
	.SUM(\u_cordic/[10].U/n208_2 )
);
defparam \u_cordic/[10].U/n208_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n207_1_s  (
	.I0(\u_cordic/z[10] [15]),
	.I1(GND),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n208_1_1 ),
	.COUT(\u_cordic/[10].U/n207_1_1 ),
	.SUM(\u_cordic/[10].U/n207_2 )
);
defparam \u_cordic/[10].U/n207_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n206_1_s  (
	.I0(\u_cordic/z[10] [16]),
	.I1(GND),
	.I3(\u_cordic/z[10] [16]),
	.CIN(\u_cordic/[10].U/n207_1_1 ),
	.COUT(\u_cordic/[10].U/n206_1_0_COUT ),
	.SUM(\u_cordic/[10].U/n206_2 )
);
defparam \u_cordic/[10].U/n206_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n205_1_s  (
	.I0(\u_cordic/y[10] [0]),
	.I1(\u_cordic/x[10] [10]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/z[10] [16]),
	.COUT(\u_cordic/[10].U/n205_1_1 ),
	.SUM(\u_cordic/[10].U/n205_2 )
);
defparam \u_cordic/[10].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n204_1_s  (
	.I0(\u_cordic/y[10] [1]),
	.I1(\u_cordic/x[10] [11]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n205_1_1 ),
	.COUT(\u_cordic/[10].U/n204_1_1 ),
	.SUM(\u_cordic/[10].U/n204_2 )
);
defparam \u_cordic/[10].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n203_1_s  (
	.I0(\u_cordic/y[10] [2]),
	.I1(\u_cordic/x[10] [12]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n204_1_1 ),
	.COUT(\u_cordic/[10].U/n203_1_1 ),
	.SUM(\u_cordic/[10].U/n203_2 )
);
defparam \u_cordic/[10].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n202_1_s  (
	.I0(\u_cordic/y[10] [3]),
	.I1(\u_cordic/x[10] [13]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n203_1_1 ),
	.COUT(\u_cordic/[10].U/n202_1_1 ),
	.SUM(\u_cordic/[10].U/n202_2 )
);
defparam \u_cordic/[10].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n201_1_s  (
	.I0(\u_cordic/y[10] [4]),
	.I1(\u_cordic/x[10] [14]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n202_1_1 ),
	.COUT(\u_cordic/[10].U/n201_1_1 ),
	.SUM(\u_cordic/[10].U/n201_2 )
);
defparam \u_cordic/[10].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n200_1_s  (
	.I0(\u_cordic/y[10] [5]),
	.I1(\u_cordic/x[10] [15]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n201_1_1 ),
	.COUT(\u_cordic/[10].U/n200_1_1 ),
	.SUM(\u_cordic/[10].U/n200_2 )
);
defparam \u_cordic/[10].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n199_1_s  (
	.I0(\u_cordic/y[10] [6]),
	.I1(\u_cordic/x[10] [16]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n200_1_1 ),
	.COUT(\u_cordic/[10].U/n199_1_1 ),
	.SUM(\u_cordic/[10].U/n199_2 )
);
defparam \u_cordic/[10].U/n199_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n198_1_s  (
	.I0(\u_cordic/y[10] [7]),
	.I1(\u_cordic/x[10] [16]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n199_1_1 ),
	.COUT(\u_cordic/[10].U/n198_1_1 ),
	.SUM(\u_cordic/[10].U/n198_2 )
);
defparam \u_cordic/[10].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n197_1_s  (
	.I0(\u_cordic/y[10] [8]),
	.I1(\u_cordic/x[10] [16]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n198_1_1 ),
	.COUT(\u_cordic/[10].U/n197_1_1 ),
	.SUM(\u_cordic/[10].U/n197_2 )
);
defparam \u_cordic/[10].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n196_1_s  (
	.I0(\u_cordic/y[10] [9]),
	.I1(\u_cordic/x[10] [16]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n197_1_1 ),
	.COUT(\u_cordic/[10].U/n196_1_1 ),
	.SUM(\u_cordic/[10].U/n196_2 )
);
defparam \u_cordic/[10].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n195_1_s  (
	.I0(\u_cordic/y[10] [10]),
	.I1(\u_cordic/x[10] [16]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n196_1_1 ),
	.COUT(\u_cordic/[10].U/n195_1_1 ),
	.SUM(\u_cordic/[10].U/n195_2 )
);
defparam \u_cordic/[10].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n194_1_s  (
	.I0(\u_cordic/y[10] [11]),
	.I1(\u_cordic/x[10] [16]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n195_1_1 ),
	.COUT(\u_cordic/[10].U/n194_1_1 ),
	.SUM(\u_cordic/[10].U/n194_2 )
);
defparam \u_cordic/[10].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n193_1_s  (
	.I0(\u_cordic/y[10] [12]),
	.I1(\u_cordic/x[10] [16]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n194_1_1 ),
	.COUT(\u_cordic/[10].U/n193_1_1 ),
	.SUM(\u_cordic/[10].U/n193_2 )
);
defparam \u_cordic/[10].U/n193_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n192_1_s  (
	.I0(\u_cordic/y[10] [13]),
	.I1(\u_cordic/x[10] [16]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n193_1_1 ),
	.COUT(\u_cordic/[10].U/n192_1_1 ),
	.SUM(\u_cordic/[10].U/n192_2 )
);
defparam \u_cordic/[10].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n191_1_s  (
	.I0(\u_cordic/y[10] [14]),
	.I1(\u_cordic/x[10] [16]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n192_1_1 ),
	.COUT(\u_cordic/[10].U/n191_1_1 ),
	.SUM(\u_cordic/[10].U/n191_2 )
);
defparam \u_cordic/[10].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n190_1_s  (
	.I0(\u_cordic/y[10] [15]),
	.I1(\u_cordic/x[10] [16]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n191_1_1 ),
	.COUT(\u_cordic/[10].U/n190_1_1 ),
	.SUM(\u_cordic/[10].U/n190_2 )
);
defparam \u_cordic/[10].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[10].U/n189_1_s  (
	.I0(\u_cordic/y[10] [16]),
	.I1(\u_cordic/x[10] [16]),
	.I3(\u_cordic/z[10][16]_1_5 ),
	.CIN(\u_cordic/[10].U/n190_1_1 ),
	.COUT(\u_cordic/[10].U/n189_1_0_COUT ),
	.SUM(\u_cordic/[10].U/n189_2 )
);
defparam \u_cordic/[10].U/n189_1_s .ALU_MODE=2;
LUT1 \u_cordic/[10].U/z[11][16]_1_s3  (
	.I0(\u_cordic/z[11] [16]),
	.F(\u_cordic/z[11][16]_1_5 )
);
defparam \u_cordic/[10].U/z[11][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[11].U/x_1_15_s0  (
	.D(\u_cordic/[11].U/n175_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [15])
);
defparam \u_cordic/[11].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_14_s0  (
	.D(\u_cordic/[11].U/n176_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [14])
);
defparam \u_cordic/[11].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_13_s0  (
	.D(\u_cordic/[11].U/n177_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [13])
);
defparam \u_cordic/[11].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_12_s0  (
	.D(\u_cordic/[11].U/n178_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [12])
);
defparam \u_cordic/[11].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_11_s0  (
	.D(\u_cordic/[11].U/n179_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [11])
);
defparam \u_cordic/[11].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_10_s0  (
	.D(\u_cordic/[11].U/n180_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [10])
);
defparam \u_cordic/[11].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_9_s0  (
	.D(\u_cordic/[11].U/n181_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [9])
);
defparam \u_cordic/[11].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_8_s0  (
	.D(\u_cordic/[11].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [8])
);
defparam \u_cordic/[11].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_7_s0  (
	.D(\u_cordic/[11].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [7])
);
defparam \u_cordic/[11].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_6_s0  (
	.D(\u_cordic/[11].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [6])
);
defparam \u_cordic/[11].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_5_s0  (
	.D(\u_cordic/[11].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [5])
);
defparam \u_cordic/[11].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_4_s0  (
	.D(\u_cordic/[11].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [4])
);
defparam \u_cordic/[11].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_3_s0  (
	.D(\u_cordic/[11].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [3])
);
defparam \u_cordic/[11].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_2_s0  (
	.D(\u_cordic/[11].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [2])
);
defparam \u_cordic/[11].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_1_s0  (
	.D(\u_cordic/[11].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [1])
);
defparam \u_cordic/[11].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_0_s0  (
	.D(\u_cordic/[11].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [0])
);
defparam \u_cordic/[11].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_16_s0  (
	.D(\u_cordic/[11].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [16])
);
defparam \u_cordic/[11].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_15_s0  (
	.D(\u_cordic/[11].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [15])
);
defparam \u_cordic/[11].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_14_s0  (
	.D(\u_cordic/[11].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [14])
);
defparam \u_cordic/[11].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_13_s0  (
	.D(\u_cordic/[11].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [13])
);
defparam \u_cordic/[11].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_12_s0  (
	.D(\u_cordic/[11].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [12])
);
defparam \u_cordic/[11].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_11_s0  (
	.D(\u_cordic/[11].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [11])
);
defparam \u_cordic/[11].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_10_s0  (
	.D(\u_cordic/[11].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [10])
);
defparam \u_cordic/[11].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_9_s0  (
	.D(\u_cordic/[11].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [9])
);
defparam \u_cordic/[11].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_8_s0  (
	.D(\u_cordic/[11].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [8])
);
defparam \u_cordic/[11].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_7_s0  (
	.D(\u_cordic/[11].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [7])
);
defparam \u_cordic/[11].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_6_s0  (
	.D(\u_cordic/[11].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [6])
);
defparam \u_cordic/[11].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_5_s0  (
	.D(\u_cordic/[11].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [5])
);
defparam \u_cordic/[11].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_4_s0  (
	.D(\u_cordic/[11].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [4])
);
defparam \u_cordic/[11].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_3_s0  (
	.D(\u_cordic/[11].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [3])
);
defparam \u_cordic/[11].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_2_s0  (
	.D(\u_cordic/[11].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [2])
);
defparam \u_cordic/[11].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_1_s0  (
	.D(\u_cordic/[11].U/n206_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [1])
);
defparam \u_cordic/[11].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/y_1_0_s0  (
	.D(\u_cordic/[11].U/n207_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[12] [0])
);
defparam \u_cordic/[11].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_16_s0  (
	.D(\u_cordic/[11].U/n208_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [16])
);
defparam \u_cordic/[11].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_15_s0  (
	.D(\u_cordic/[11].U/n209_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [15])
);
defparam \u_cordic/[11].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_14_s0  (
	.D(\u_cordic/[11].U/n210_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [14])
);
defparam \u_cordic/[11].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_13_s0  (
	.D(\u_cordic/[11].U/n211_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [13])
);
defparam \u_cordic/[11].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_12_s0  (
	.D(\u_cordic/[11].U/n212_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [12])
);
defparam \u_cordic/[11].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_11_s0  (
	.D(\u_cordic/[11].U/n213_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [11])
);
defparam \u_cordic/[11].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_10_s0  (
	.D(\u_cordic/[11].U/n214_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [10])
);
defparam \u_cordic/[11].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_9_s0  (
	.D(\u_cordic/[11].U/n215_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [9])
);
defparam \u_cordic/[11].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_8_s0  (
	.D(\u_cordic/[11].U/n216_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [8])
);
defparam \u_cordic/[11].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_7_s0  (
	.D(\u_cordic/[11].U/n217_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [7])
);
defparam \u_cordic/[11].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_6_s0  (
	.D(\u_cordic/[11].U/n218_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [6])
);
defparam \u_cordic/[11].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_5_s0  (
	.D(\u_cordic/[11].U/n219_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [5])
);
defparam \u_cordic/[11].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_4_s0  (
	.D(\u_cordic/[11].U/n220_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [4])
);
defparam \u_cordic/[11].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_3_s0  (
	.D(\u_cordic/z[11] [3]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [3])
);
defparam \u_cordic/[11].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_2_s0  (
	.D(\u_cordic/z[11] [2]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [2])
);
defparam \u_cordic/[11].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_1_s0  (
	.D(\u_cordic/z[11] [1]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [1])
);
defparam \u_cordic/[11].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/z_1_0_s0  (
	.D(\u_cordic/z[11] [0]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[12] [0])
);
defparam \u_cordic/[11].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[11].U/x_1_16_s0  (
	.D(\u_cordic/[11].U/n174_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[12] [16])
);
defparam \u_cordic/[11].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[11].U/n190_1_s  (
	.I0(\u_cordic/x[11] [0]),
	.I1(\u_cordic/y[11] [11]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/z[11][16]_1_5 ),
	.COUT(\u_cordic/[11].U/n190_1_1 ),
	.SUM(\u_cordic/[11].U/n190_2 )
);
defparam \u_cordic/[11].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n189_1_s  (
	.I0(\u_cordic/x[11] [1]),
	.I1(\u_cordic/y[11] [12]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n190_1_1 ),
	.COUT(\u_cordic/[11].U/n189_1_1 ),
	.SUM(\u_cordic/[11].U/n189_2 )
);
defparam \u_cordic/[11].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n188_1_s  (
	.I0(\u_cordic/x[11] [2]),
	.I1(\u_cordic/y[11] [13]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n189_1_1 ),
	.COUT(\u_cordic/[11].U/n188_1_1 ),
	.SUM(\u_cordic/[11].U/n188_2 )
);
defparam \u_cordic/[11].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n187_1_s  (
	.I0(\u_cordic/x[11] [3]),
	.I1(\u_cordic/y[11] [14]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n188_1_1 ),
	.COUT(\u_cordic/[11].U/n187_1_1 ),
	.SUM(\u_cordic/[11].U/n187_2 )
);
defparam \u_cordic/[11].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n186_1_s  (
	.I0(\u_cordic/x[11] [4]),
	.I1(\u_cordic/y[11] [15]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n187_1_1 ),
	.COUT(\u_cordic/[11].U/n186_1_1 ),
	.SUM(\u_cordic/[11].U/n186_2 )
);
defparam \u_cordic/[11].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n185_1_s  (
	.I0(\u_cordic/x[11] [5]),
	.I1(\u_cordic/y[11] [16]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n186_1_1 ),
	.COUT(\u_cordic/[11].U/n185_1_1 ),
	.SUM(\u_cordic/[11].U/n185_2 )
);
defparam \u_cordic/[11].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n184_1_s  (
	.I0(\u_cordic/x[11] [6]),
	.I1(\u_cordic/y[11] [16]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n185_1_1 ),
	.COUT(\u_cordic/[11].U/n184_1_1 ),
	.SUM(\u_cordic/[11].U/n184_2 )
);
defparam \u_cordic/[11].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n183_1_s  (
	.I0(\u_cordic/x[11] [7]),
	.I1(\u_cordic/y[11] [16]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n184_1_1 ),
	.COUT(\u_cordic/[11].U/n183_1_1 ),
	.SUM(\u_cordic/[11].U/n183_2 )
);
defparam \u_cordic/[11].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n182_1_s  (
	.I0(\u_cordic/x[11] [8]),
	.I1(\u_cordic/y[11] [16]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n183_1_1 ),
	.COUT(\u_cordic/[11].U/n182_1_1 ),
	.SUM(\u_cordic/[11].U/n182_2 )
);
defparam \u_cordic/[11].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n181_1_s  (
	.I0(\u_cordic/x[11] [9]),
	.I1(\u_cordic/y[11] [16]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n182_1_1 ),
	.COUT(\u_cordic/[11].U/n181_1_1 ),
	.SUM(\u_cordic/[11].U/n181_2 )
);
defparam \u_cordic/[11].U/n181_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n180_1_s  (
	.I0(\u_cordic/x[11] [10]),
	.I1(\u_cordic/y[11] [16]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n181_1_1 ),
	.COUT(\u_cordic/[11].U/n180_1_1 ),
	.SUM(\u_cordic/[11].U/n180_2 )
);
defparam \u_cordic/[11].U/n180_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n179_1_s  (
	.I0(\u_cordic/x[11] [11]),
	.I1(\u_cordic/y[11] [16]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n180_1_1 ),
	.COUT(\u_cordic/[11].U/n179_1_1 ),
	.SUM(\u_cordic/[11].U/n179_2 )
);
defparam \u_cordic/[11].U/n179_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n178_1_s  (
	.I0(\u_cordic/x[11] [12]),
	.I1(\u_cordic/y[11] [16]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n179_1_1 ),
	.COUT(\u_cordic/[11].U/n178_1_1 ),
	.SUM(\u_cordic/[11].U/n178_2 )
);
defparam \u_cordic/[11].U/n178_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n177_1_s  (
	.I0(\u_cordic/x[11] [13]),
	.I1(\u_cordic/y[11] [16]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n178_1_1 ),
	.COUT(\u_cordic/[11].U/n177_1_1 ),
	.SUM(\u_cordic/[11].U/n177_2 )
);
defparam \u_cordic/[11].U/n177_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n176_1_s  (
	.I0(\u_cordic/x[11] [14]),
	.I1(\u_cordic/y[11] [16]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n177_1_1 ),
	.COUT(\u_cordic/[11].U/n176_1_1 ),
	.SUM(\u_cordic/[11].U/n176_2 )
);
defparam \u_cordic/[11].U/n176_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n175_1_s  (
	.I0(\u_cordic/x[11] [15]),
	.I1(\u_cordic/y[11] [16]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n176_1_1 ),
	.COUT(\u_cordic/[11].U/n175_1_1 ),
	.SUM(\u_cordic/[11].U/n175_2 )
);
defparam \u_cordic/[11].U/n175_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n174_1_s  (
	.I0(\u_cordic/x[11] [16]),
	.I1(\u_cordic/y[11] [16]),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n175_1_1 ),
	.COUT(\u_cordic/[11].U/n174_1_0_COUT ),
	.SUM(\u_cordic/[11].U/n174_2 )
);
defparam \u_cordic/[11].U/n174_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n220_1_s  (
	.I0(\u_cordic/z[11] [4]),
	.I1(VCC),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/z[11][16]_1_5 ),
	.COUT(\u_cordic/[11].U/n220_1_1 ),
	.SUM(\u_cordic/[11].U/n220_2 )
);
defparam \u_cordic/[11].U/n220_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n219_1_s  (
	.I0(\u_cordic/z[11] [5]),
	.I1(GND),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n220_1_1 ),
	.COUT(\u_cordic/[11].U/n219_1_1 ),
	.SUM(\u_cordic/[11].U/n219_2 )
);
defparam \u_cordic/[11].U/n219_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n218_1_s  (
	.I0(\u_cordic/z[11] [6]),
	.I1(GND),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n219_1_1 ),
	.COUT(\u_cordic/[11].U/n218_1_1 ),
	.SUM(\u_cordic/[11].U/n218_2 )
);
defparam \u_cordic/[11].U/n218_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n217_1_s  (
	.I0(\u_cordic/z[11] [7]),
	.I1(GND),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n218_1_1 ),
	.COUT(\u_cordic/[11].U/n217_1_1 ),
	.SUM(\u_cordic/[11].U/n217_2 )
);
defparam \u_cordic/[11].U/n217_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n216_1_s  (
	.I0(\u_cordic/z[11] [8]),
	.I1(GND),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n217_1_1 ),
	.COUT(\u_cordic/[11].U/n216_1_1 ),
	.SUM(\u_cordic/[11].U/n216_2 )
);
defparam \u_cordic/[11].U/n216_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n215_1_s  (
	.I0(\u_cordic/z[11] [9]),
	.I1(GND),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n216_1_1 ),
	.COUT(\u_cordic/[11].U/n215_1_1 ),
	.SUM(\u_cordic/[11].U/n215_2 )
);
defparam \u_cordic/[11].U/n215_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n214_1_s  (
	.I0(\u_cordic/z[11] [10]),
	.I1(GND),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n215_1_1 ),
	.COUT(\u_cordic/[11].U/n214_1_1 ),
	.SUM(\u_cordic/[11].U/n214_2 )
);
defparam \u_cordic/[11].U/n214_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n213_1_s  (
	.I0(\u_cordic/z[11] [11]),
	.I1(GND),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n214_1_1 ),
	.COUT(\u_cordic/[11].U/n213_1_1 ),
	.SUM(\u_cordic/[11].U/n213_2 )
);
defparam \u_cordic/[11].U/n213_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n212_1_s  (
	.I0(\u_cordic/z[11] [12]),
	.I1(GND),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n213_1_1 ),
	.COUT(\u_cordic/[11].U/n212_1_1 ),
	.SUM(\u_cordic/[11].U/n212_2 )
);
defparam \u_cordic/[11].U/n212_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n211_1_s  (
	.I0(\u_cordic/z[11] [13]),
	.I1(GND),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n212_1_1 ),
	.COUT(\u_cordic/[11].U/n211_1_1 ),
	.SUM(\u_cordic/[11].U/n211_2 )
);
defparam \u_cordic/[11].U/n211_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n210_1_s  (
	.I0(\u_cordic/z[11] [14]),
	.I1(GND),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n211_1_1 ),
	.COUT(\u_cordic/[11].U/n210_1_1 ),
	.SUM(\u_cordic/[11].U/n210_2 )
);
defparam \u_cordic/[11].U/n210_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n209_1_s  (
	.I0(\u_cordic/z[11] [15]),
	.I1(GND),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n210_1_1 ),
	.COUT(\u_cordic/[11].U/n209_1_1 ),
	.SUM(\u_cordic/[11].U/n209_2 )
);
defparam \u_cordic/[11].U/n209_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n208_1_s  (
	.I0(\u_cordic/z[11] [16]),
	.I1(GND),
	.I3(\u_cordic/z[11] [16]),
	.CIN(\u_cordic/[11].U/n209_1_1 ),
	.COUT(\u_cordic/[11].U/n208_1_0_COUT ),
	.SUM(\u_cordic/[11].U/n208_2 )
);
defparam \u_cordic/[11].U/n208_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n207_1_s  (
	.I0(\u_cordic/y[11] [0]),
	.I1(\u_cordic/x[11] [11]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/z[11] [16]),
	.COUT(\u_cordic/[11].U/n207_1_1 ),
	.SUM(\u_cordic/[11].U/n207_2 )
);
defparam \u_cordic/[11].U/n207_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n206_1_s  (
	.I0(\u_cordic/y[11] [1]),
	.I1(\u_cordic/x[11] [12]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n207_1_1 ),
	.COUT(\u_cordic/[11].U/n206_1_1 ),
	.SUM(\u_cordic/[11].U/n206_2 )
);
defparam \u_cordic/[11].U/n206_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n205_1_s  (
	.I0(\u_cordic/y[11] [2]),
	.I1(\u_cordic/x[11] [13]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n206_1_1 ),
	.COUT(\u_cordic/[11].U/n205_1_1 ),
	.SUM(\u_cordic/[11].U/n205_2 )
);
defparam \u_cordic/[11].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n204_1_s  (
	.I0(\u_cordic/y[11] [3]),
	.I1(\u_cordic/x[11] [14]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n205_1_1 ),
	.COUT(\u_cordic/[11].U/n204_1_1 ),
	.SUM(\u_cordic/[11].U/n204_2 )
);
defparam \u_cordic/[11].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n203_1_s  (
	.I0(\u_cordic/y[11] [4]),
	.I1(\u_cordic/x[11] [15]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n204_1_1 ),
	.COUT(\u_cordic/[11].U/n203_1_1 ),
	.SUM(\u_cordic/[11].U/n203_2 )
);
defparam \u_cordic/[11].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n202_1_s  (
	.I0(\u_cordic/y[11] [5]),
	.I1(\u_cordic/x[11] [16]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n203_1_1 ),
	.COUT(\u_cordic/[11].U/n202_1_1 ),
	.SUM(\u_cordic/[11].U/n202_2 )
);
defparam \u_cordic/[11].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n201_1_s  (
	.I0(\u_cordic/y[11] [6]),
	.I1(\u_cordic/x[11] [16]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n202_1_1 ),
	.COUT(\u_cordic/[11].U/n201_1_1 ),
	.SUM(\u_cordic/[11].U/n201_2 )
);
defparam \u_cordic/[11].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n200_1_s  (
	.I0(\u_cordic/y[11] [7]),
	.I1(\u_cordic/x[11] [16]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n201_1_1 ),
	.COUT(\u_cordic/[11].U/n200_1_1 ),
	.SUM(\u_cordic/[11].U/n200_2 )
);
defparam \u_cordic/[11].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n199_1_s  (
	.I0(\u_cordic/y[11] [8]),
	.I1(\u_cordic/x[11] [16]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n200_1_1 ),
	.COUT(\u_cordic/[11].U/n199_1_1 ),
	.SUM(\u_cordic/[11].U/n199_2 )
);
defparam \u_cordic/[11].U/n199_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n198_1_s  (
	.I0(\u_cordic/y[11] [9]),
	.I1(\u_cordic/x[11] [16]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n199_1_1 ),
	.COUT(\u_cordic/[11].U/n198_1_1 ),
	.SUM(\u_cordic/[11].U/n198_2 )
);
defparam \u_cordic/[11].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n197_1_s  (
	.I0(\u_cordic/y[11] [10]),
	.I1(\u_cordic/x[11] [16]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n198_1_1 ),
	.COUT(\u_cordic/[11].U/n197_1_1 ),
	.SUM(\u_cordic/[11].U/n197_2 )
);
defparam \u_cordic/[11].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n196_1_s  (
	.I0(\u_cordic/y[11] [11]),
	.I1(\u_cordic/x[11] [16]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n197_1_1 ),
	.COUT(\u_cordic/[11].U/n196_1_1 ),
	.SUM(\u_cordic/[11].U/n196_2 )
);
defparam \u_cordic/[11].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n195_1_s  (
	.I0(\u_cordic/y[11] [12]),
	.I1(\u_cordic/x[11] [16]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n196_1_1 ),
	.COUT(\u_cordic/[11].U/n195_1_1 ),
	.SUM(\u_cordic/[11].U/n195_2 )
);
defparam \u_cordic/[11].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n194_1_s  (
	.I0(\u_cordic/y[11] [13]),
	.I1(\u_cordic/x[11] [16]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n195_1_1 ),
	.COUT(\u_cordic/[11].U/n194_1_1 ),
	.SUM(\u_cordic/[11].U/n194_2 )
);
defparam \u_cordic/[11].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n193_1_s  (
	.I0(\u_cordic/y[11] [14]),
	.I1(\u_cordic/x[11] [16]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n194_1_1 ),
	.COUT(\u_cordic/[11].U/n193_1_1 ),
	.SUM(\u_cordic/[11].U/n193_2 )
);
defparam \u_cordic/[11].U/n193_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n192_1_s  (
	.I0(\u_cordic/y[11] [15]),
	.I1(\u_cordic/x[11] [16]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n193_1_1 ),
	.COUT(\u_cordic/[11].U/n192_1_1 ),
	.SUM(\u_cordic/[11].U/n192_2 )
);
defparam \u_cordic/[11].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[11].U/n191_1_s  (
	.I0(\u_cordic/y[11] [16]),
	.I1(\u_cordic/x[11] [16]),
	.I3(\u_cordic/z[11][16]_1_5 ),
	.CIN(\u_cordic/[11].U/n192_1_1 ),
	.COUT(\u_cordic/[11].U/n191_1_0_COUT ),
	.SUM(\u_cordic/[11].U/n191_2 )
);
defparam \u_cordic/[11].U/n191_1_s .ALU_MODE=2;
LUT1 \u_cordic/[11].U/z[12][16]_1_s3  (
	.I0(\u_cordic/z[12] [16]),
	.F(\u_cordic/z[12][16]_1_5 )
);
defparam \u_cordic/[11].U/z[12][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[12].U/x_1_15_s0  (
	.D(\u_cordic/[12].U/n177_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [15])
);
defparam \u_cordic/[12].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_14_s0  (
	.D(\u_cordic/[12].U/n178_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [14])
);
defparam \u_cordic/[12].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_13_s0  (
	.D(\u_cordic/[12].U/n179_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [13])
);
defparam \u_cordic/[12].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_12_s0  (
	.D(\u_cordic/[12].U/n180_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [12])
);
defparam \u_cordic/[12].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_11_s0  (
	.D(\u_cordic/[12].U/n181_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [11])
);
defparam \u_cordic/[12].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_10_s0  (
	.D(\u_cordic/[12].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [10])
);
defparam \u_cordic/[12].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_9_s0  (
	.D(\u_cordic/[12].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [9])
);
defparam \u_cordic/[12].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_8_s0  (
	.D(\u_cordic/[12].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [8])
);
defparam \u_cordic/[12].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_7_s0  (
	.D(\u_cordic/[12].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [7])
);
defparam \u_cordic/[12].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_6_s0  (
	.D(\u_cordic/[12].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [6])
);
defparam \u_cordic/[12].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_5_s0  (
	.D(\u_cordic/[12].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [5])
);
defparam \u_cordic/[12].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_4_s0  (
	.D(\u_cordic/[12].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [4])
);
defparam \u_cordic/[12].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_3_s0  (
	.D(\u_cordic/[12].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [3])
);
defparam \u_cordic/[12].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_2_s0  (
	.D(\u_cordic/[12].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [2])
);
defparam \u_cordic/[12].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_1_s0  (
	.D(\u_cordic/[12].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [1])
);
defparam \u_cordic/[12].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_0_s0  (
	.D(\u_cordic/[12].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [0])
);
defparam \u_cordic/[12].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_16_s0  (
	.D(\u_cordic/[12].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [16])
);
defparam \u_cordic/[12].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_15_s0  (
	.D(\u_cordic/[12].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [15])
);
defparam \u_cordic/[12].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_14_s0  (
	.D(\u_cordic/[12].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [14])
);
defparam \u_cordic/[12].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_13_s0  (
	.D(\u_cordic/[12].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [13])
);
defparam \u_cordic/[12].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_12_s0  (
	.D(\u_cordic/[12].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [12])
);
defparam \u_cordic/[12].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_11_s0  (
	.D(\u_cordic/[12].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [11])
);
defparam \u_cordic/[12].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_10_s0  (
	.D(\u_cordic/[12].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [10])
);
defparam \u_cordic/[12].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_9_s0  (
	.D(\u_cordic/[12].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [9])
);
defparam \u_cordic/[12].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_8_s0  (
	.D(\u_cordic/[12].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [8])
);
defparam \u_cordic/[12].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_7_s0  (
	.D(\u_cordic/[12].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [7])
);
defparam \u_cordic/[12].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_6_s0  (
	.D(\u_cordic/[12].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [6])
);
defparam \u_cordic/[12].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_5_s0  (
	.D(\u_cordic/[12].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [5])
);
defparam \u_cordic/[12].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_4_s0  (
	.D(\u_cordic/[12].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [4])
);
defparam \u_cordic/[12].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_3_s0  (
	.D(\u_cordic/[12].U/n206_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [3])
);
defparam \u_cordic/[12].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_2_s0  (
	.D(\u_cordic/[12].U/n207_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [2])
);
defparam \u_cordic/[12].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_1_s0  (
	.D(\u_cordic/[12].U/n208_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [1])
);
defparam \u_cordic/[12].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/y_1_0_s0  (
	.D(\u_cordic/[12].U/n209_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[13] [0])
);
defparam \u_cordic/[12].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_16_s0  (
	.D(\u_cordic/[12].U/n210_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [16])
);
defparam \u_cordic/[12].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_15_s0  (
	.D(\u_cordic/[12].U/n211_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [15])
);
defparam \u_cordic/[12].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_14_s0  (
	.D(\u_cordic/[12].U/n212_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [14])
);
defparam \u_cordic/[12].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_13_s0  (
	.D(\u_cordic/[12].U/n213_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [13])
);
defparam \u_cordic/[12].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_12_s0  (
	.D(\u_cordic/[12].U/n214_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [12])
);
defparam \u_cordic/[12].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_11_s0  (
	.D(\u_cordic/[12].U/n215_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [11])
);
defparam \u_cordic/[12].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_10_s0  (
	.D(\u_cordic/[12].U/n216_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [10])
);
defparam \u_cordic/[12].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_9_s0  (
	.D(\u_cordic/[12].U/n217_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [9])
);
defparam \u_cordic/[12].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_8_s0  (
	.D(\u_cordic/[12].U/n218_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [8])
);
defparam \u_cordic/[12].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_7_s0  (
	.D(\u_cordic/[12].U/n219_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [7])
);
defparam \u_cordic/[12].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_6_s0  (
	.D(\u_cordic/[12].U/n220_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [6])
);
defparam \u_cordic/[12].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_5_s0  (
	.D(\u_cordic/[12].U/n221_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [5])
);
defparam \u_cordic/[12].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_4_s0  (
	.D(\u_cordic/[12].U/n222_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [4])
);
defparam \u_cordic/[12].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_3_s0  (
	.D(\u_cordic/[12].U/n223_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [3])
);
defparam \u_cordic/[12].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_2_s0  (
	.D(\u_cordic/z[12] [2]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [2])
);
defparam \u_cordic/[12].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_1_s0  (
	.D(\u_cordic/z[12] [1]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [1])
);
defparam \u_cordic/[12].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/z_1_0_s0  (
	.D(\u_cordic/z[12] [0]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[13] [0])
);
defparam \u_cordic/[12].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[12].U/x_1_16_s0  (
	.D(\u_cordic/[12].U/n176_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[13] [16])
);
defparam \u_cordic/[12].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[12].U/n192_1_s  (
	.I0(\u_cordic/x[12] [0]),
	.I1(\u_cordic/y[12] [12]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/z[12][16]_1_5 ),
	.COUT(\u_cordic/[12].U/n192_1_1 ),
	.SUM(\u_cordic/[12].U/n192_2 )
);
defparam \u_cordic/[12].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n191_1_s  (
	.I0(\u_cordic/x[12] [1]),
	.I1(\u_cordic/y[12] [13]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n192_1_1 ),
	.COUT(\u_cordic/[12].U/n191_1_1 ),
	.SUM(\u_cordic/[12].U/n191_2 )
);
defparam \u_cordic/[12].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n190_1_s  (
	.I0(\u_cordic/x[12] [2]),
	.I1(\u_cordic/y[12] [14]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n191_1_1 ),
	.COUT(\u_cordic/[12].U/n190_1_1 ),
	.SUM(\u_cordic/[12].U/n190_2 )
);
defparam \u_cordic/[12].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n189_1_s  (
	.I0(\u_cordic/x[12] [3]),
	.I1(\u_cordic/y[12] [15]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n190_1_1 ),
	.COUT(\u_cordic/[12].U/n189_1_1 ),
	.SUM(\u_cordic/[12].U/n189_2 )
);
defparam \u_cordic/[12].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n188_1_s  (
	.I0(\u_cordic/x[12] [4]),
	.I1(\u_cordic/y[12] [16]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n189_1_1 ),
	.COUT(\u_cordic/[12].U/n188_1_1 ),
	.SUM(\u_cordic/[12].U/n188_2 )
);
defparam \u_cordic/[12].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n187_1_s  (
	.I0(\u_cordic/x[12] [5]),
	.I1(\u_cordic/y[12] [16]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n188_1_1 ),
	.COUT(\u_cordic/[12].U/n187_1_1 ),
	.SUM(\u_cordic/[12].U/n187_2 )
);
defparam \u_cordic/[12].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n186_1_s  (
	.I0(\u_cordic/x[12] [6]),
	.I1(\u_cordic/y[12] [16]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n187_1_1 ),
	.COUT(\u_cordic/[12].U/n186_1_1 ),
	.SUM(\u_cordic/[12].U/n186_2 )
);
defparam \u_cordic/[12].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n185_1_s  (
	.I0(\u_cordic/x[12] [7]),
	.I1(\u_cordic/y[12] [16]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n186_1_1 ),
	.COUT(\u_cordic/[12].U/n185_1_1 ),
	.SUM(\u_cordic/[12].U/n185_2 )
);
defparam \u_cordic/[12].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n184_1_s  (
	.I0(\u_cordic/x[12] [8]),
	.I1(\u_cordic/y[12] [16]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n185_1_1 ),
	.COUT(\u_cordic/[12].U/n184_1_1 ),
	.SUM(\u_cordic/[12].U/n184_2 )
);
defparam \u_cordic/[12].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n183_1_s  (
	.I0(\u_cordic/x[12] [9]),
	.I1(\u_cordic/y[12] [16]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n184_1_1 ),
	.COUT(\u_cordic/[12].U/n183_1_1 ),
	.SUM(\u_cordic/[12].U/n183_2 )
);
defparam \u_cordic/[12].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n182_1_s  (
	.I0(\u_cordic/x[12] [10]),
	.I1(\u_cordic/y[12] [16]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n183_1_1 ),
	.COUT(\u_cordic/[12].U/n182_1_1 ),
	.SUM(\u_cordic/[12].U/n182_2 )
);
defparam \u_cordic/[12].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n181_1_s  (
	.I0(\u_cordic/x[12] [11]),
	.I1(\u_cordic/y[12] [16]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n182_1_1 ),
	.COUT(\u_cordic/[12].U/n181_1_1 ),
	.SUM(\u_cordic/[12].U/n181_2 )
);
defparam \u_cordic/[12].U/n181_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n180_1_s  (
	.I0(\u_cordic/x[12] [12]),
	.I1(\u_cordic/y[12] [16]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n181_1_1 ),
	.COUT(\u_cordic/[12].U/n180_1_1 ),
	.SUM(\u_cordic/[12].U/n180_2 )
);
defparam \u_cordic/[12].U/n180_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n179_1_s  (
	.I0(\u_cordic/x[12] [13]),
	.I1(\u_cordic/y[12] [16]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n180_1_1 ),
	.COUT(\u_cordic/[12].U/n179_1_1 ),
	.SUM(\u_cordic/[12].U/n179_2 )
);
defparam \u_cordic/[12].U/n179_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n178_1_s  (
	.I0(\u_cordic/x[12] [14]),
	.I1(\u_cordic/y[12] [16]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n179_1_1 ),
	.COUT(\u_cordic/[12].U/n178_1_1 ),
	.SUM(\u_cordic/[12].U/n178_2 )
);
defparam \u_cordic/[12].U/n178_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n177_1_s  (
	.I0(\u_cordic/x[12] [15]),
	.I1(\u_cordic/y[12] [16]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n178_1_1 ),
	.COUT(\u_cordic/[12].U/n177_1_1 ),
	.SUM(\u_cordic/[12].U/n177_2 )
);
defparam \u_cordic/[12].U/n177_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n176_1_s  (
	.I0(\u_cordic/x[12] [16]),
	.I1(\u_cordic/y[12] [16]),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n177_1_1 ),
	.COUT(\u_cordic/[12].U/n176_1_0_COUT ),
	.SUM(\u_cordic/[12].U/n176_2 )
);
defparam \u_cordic/[12].U/n176_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n223_1_s  (
	.I0(\u_cordic/z[12] [3]),
	.I1(VCC),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/z[12][16]_1_5 ),
	.COUT(\u_cordic/[12].U/n223_1_1 ),
	.SUM(\u_cordic/[12].U/n223_2 )
);
defparam \u_cordic/[12].U/n223_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n222_1_s  (
	.I0(\u_cordic/z[12] [4]),
	.I1(GND),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n223_1_1 ),
	.COUT(\u_cordic/[12].U/n222_1_1 ),
	.SUM(\u_cordic/[12].U/n222_2 )
);
defparam \u_cordic/[12].U/n222_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n221_1_s  (
	.I0(\u_cordic/z[12] [5]),
	.I1(GND),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n222_1_1 ),
	.COUT(\u_cordic/[12].U/n221_1_1 ),
	.SUM(\u_cordic/[12].U/n221_2 )
);
defparam \u_cordic/[12].U/n221_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n220_1_s  (
	.I0(\u_cordic/z[12] [6]),
	.I1(GND),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n221_1_1 ),
	.COUT(\u_cordic/[12].U/n220_1_1 ),
	.SUM(\u_cordic/[12].U/n220_2 )
);
defparam \u_cordic/[12].U/n220_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n219_1_s  (
	.I0(\u_cordic/z[12] [7]),
	.I1(GND),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n220_1_1 ),
	.COUT(\u_cordic/[12].U/n219_1_1 ),
	.SUM(\u_cordic/[12].U/n219_2 )
);
defparam \u_cordic/[12].U/n219_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n218_1_s  (
	.I0(\u_cordic/z[12] [8]),
	.I1(GND),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n219_1_1 ),
	.COUT(\u_cordic/[12].U/n218_1_1 ),
	.SUM(\u_cordic/[12].U/n218_2 )
);
defparam \u_cordic/[12].U/n218_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n217_1_s  (
	.I0(\u_cordic/z[12] [9]),
	.I1(GND),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n218_1_1 ),
	.COUT(\u_cordic/[12].U/n217_1_1 ),
	.SUM(\u_cordic/[12].U/n217_2 )
);
defparam \u_cordic/[12].U/n217_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n216_1_s  (
	.I0(\u_cordic/z[12] [10]),
	.I1(GND),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n217_1_1 ),
	.COUT(\u_cordic/[12].U/n216_1_1 ),
	.SUM(\u_cordic/[12].U/n216_2 )
);
defparam \u_cordic/[12].U/n216_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n215_1_s  (
	.I0(\u_cordic/z[12] [11]),
	.I1(GND),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n216_1_1 ),
	.COUT(\u_cordic/[12].U/n215_1_1 ),
	.SUM(\u_cordic/[12].U/n215_2 )
);
defparam \u_cordic/[12].U/n215_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n214_1_s  (
	.I0(\u_cordic/z[12] [12]),
	.I1(GND),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n215_1_1 ),
	.COUT(\u_cordic/[12].U/n214_1_1 ),
	.SUM(\u_cordic/[12].U/n214_2 )
);
defparam \u_cordic/[12].U/n214_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n213_1_s  (
	.I0(\u_cordic/z[12] [13]),
	.I1(GND),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n214_1_1 ),
	.COUT(\u_cordic/[12].U/n213_1_1 ),
	.SUM(\u_cordic/[12].U/n213_2 )
);
defparam \u_cordic/[12].U/n213_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n212_1_s  (
	.I0(\u_cordic/z[12] [14]),
	.I1(GND),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n213_1_1 ),
	.COUT(\u_cordic/[12].U/n212_1_1 ),
	.SUM(\u_cordic/[12].U/n212_2 )
);
defparam \u_cordic/[12].U/n212_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n211_1_s  (
	.I0(\u_cordic/z[12] [15]),
	.I1(GND),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n212_1_1 ),
	.COUT(\u_cordic/[12].U/n211_1_1 ),
	.SUM(\u_cordic/[12].U/n211_2 )
);
defparam \u_cordic/[12].U/n211_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n210_1_s  (
	.I0(\u_cordic/z[12] [16]),
	.I1(GND),
	.I3(\u_cordic/z[12] [16]),
	.CIN(\u_cordic/[12].U/n211_1_1 ),
	.COUT(\u_cordic/[12].U/n210_1_0_COUT ),
	.SUM(\u_cordic/[12].U/n210_2 )
);
defparam \u_cordic/[12].U/n210_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n209_1_s  (
	.I0(\u_cordic/y[12] [0]),
	.I1(\u_cordic/x[12] [12]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/z[12] [16]),
	.COUT(\u_cordic/[12].U/n209_1_1 ),
	.SUM(\u_cordic/[12].U/n209_2 )
);
defparam \u_cordic/[12].U/n209_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n208_1_s  (
	.I0(\u_cordic/y[12] [1]),
	.I1(\u_cordic/x[12] [13]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n209_1_1 ),
	.COUT(\u_cordic/[12].U/n208_1_1 ),
	.SUM(\u_cordic/[12].U/n208_2 )
);
defparam \u_cordic/[12].U/n208_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n207_1_s  (
	.I0(\u_cordic/y[12] [2]),
	.I1(\u_cordic/x[12] [14]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n208_1_1 ),
	.COUT(\u_cordic/[12].U/n207_1_1 ),
	.SUM(\u_cordic/[12].U/n207_2 )
);
defparam \u_cordic/[12].U/n207_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n206_1_s  (
	.I0(\u_cordic/y[12] [3]),
	.I1(\u_cordic/x[12] [15]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n207_1_1 ),
	.COUT(\u_cordic/[12].U/n206_1_1 ),
	.SUM(\u_cordic/[12].U/n206_2 )
);
defparam \u_cordic/[12].U/n206_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n205_1_s  (
	.I0(\u_cordic/y[12] [4]),
	.I1(\u_cordic/x[12] [16]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n206_1_1 ),
	.COUT(\u_cordic/[12].U/n205_1_1 ),
	.SUM(\u_cordic/[12].U/n205_2 )
);
defparam \u_cordic/[12].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n204_1_s  (
	.I0(\u_cordic/y[12] [5]),
	.I1(\u_cordic/x[12] [16]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n205_1_1 ),
	.COUT(\u_cordic/[12].U/n204_1_1 ),
	.SUM(\u_cordic/[12].U/n204_2 )
);
defparam \u_cordic/[12].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n203_1_s  (
	.I0(\u_cordic/y[12] [6]),
	.I1(\u_cordic/x[12] [16]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n204_1_1 ),
	.COUT(\u_cordic/[12].U/n203_1_1 ),
	.SUM(\u_cordic/[12].U/n203_2 )
);
defparam \u_cordic/[12].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n202_1_s  (
	.I0(\u_cordic/y[12] [7]),
	.I1(\u_cordic/x[12] [16]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n203_1_1 ),
	.COUT(\u_cordic/[12].U/n202_1_1 ),
	.SUM(\u_cordic/[12].U/n202_2 )
);
defparam \u_cordic/[12].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n201_1_s  (
	.I0(\u_cordic/y[12] [8]),
	.I1(\u_cordic/x[12] [16]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n202_1_1 ),
	.COUT(\u_cordic/[12].U/n201_1_1 ),
	.SUM(\u_cordic/[12].U/n201_2 )
);
defparam \u_cordic/[12].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n200_1_s  (
	.I0(\u_cordic/y[12] [9]),
	.I1(\u_cordic/x[12] [16]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n201_1_1 ),
	.COUT(\u_cordic/[12].U/n200_1_1 ),
	.SUM(\u_cordic/[12].U/n200_2 )
);
defparam \u_cordic/[12].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n199_1_s  (
	.I0(\u_cordic/y[12] [10]),
	.I1(\u_cordic/x[12] [16]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n200_1_1 ),
	.COUT(\u_cordic/[12].U/n199_1_1 ),
	.SUM(\u_cordic/[12].U/n199_2 )
);
defparam \u_cordic/[12].U/n199_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n198_1_s  (
	.I0(\u_cordic/y[12] [11]),
	.I1(\u_cordic/x[12] [16]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n199_1_1 ),
	.COUT(\u_cordic/[12].U/n198_1_1 ),
	.SUM(\u_cordic/[12].U/n198_2 )
);
defparam \u_cordic/[12].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n197_1_s  (
	.I0(\u_cordic/y[12] [12]),
	.I1(\u_cordic/x[12] [16]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n198_1_1 ),
	.COUT(\u_cordic/[12].U/n197_1_1 ),
	.SUM(\u_cordic/[12].U/n197_2 )
);
defparam \u_cordic/[12].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n196_1_s  (
	.I0(\u_cordic/y[12] [13]),
	.I1(\u_cordic/x[12] [16]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n197_1_1 ),
	.COUT(\u_cordic/[12].U/n196_1_1 ),
	.SUM(\u_cordic/[12].U/n196_2 )
);
defparam \u_cordic/[12].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n195_1_s  (
	.I0(\u_cordic/y[12] [14]),
	.I1(\u_cordic/x[12] [16]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n196_1_1 ),
	.COUT(\u_cordic/[12].U/n195_1_1 ),
	.SUM(\u_cordic/[12].U/n195_2 )
);
defparam \u_cordic/[12].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n194_1_s  (
	.I0(\u_cordic/y[12] [15]),
	.I1(\u_cordic/x[12] [16]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n195_1_1 ),
	.COUT(\u_cordic/[12].U/n194_1_1 ),
	.SUM(\u_cordic/[12].U/n194_2 )
);
defparam \u_cordic/[12].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[12].U/n193_1_s  (
	.I0(\u_cordic/y[12] [16]),
	.I1(\u_cordic/x[12] [16]),
	.I3(\u_cordic/z[12][16]_1_5 ),
	.CIN(\u_cordic/[12].U/n194_1_1 ),
	.COUT(\u_cordic/[12].U/n193_1_0_COUT ),
	.SUM(\u_cordic/[12].U/n193_2 )
);
defparam \u_cordic/[12].U/n193_1_s .ALU_MODE=2;
LUT1 \u_cordic/[12].U/z[13][16]_1_s3  (
	.I0(\u_cordic/z[13] [16]),
	.F(\u_cordic/z[13][16]_1_5 )
);
defparam \u_cordic/[12].U/z[13][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[13].U/x_1_15_s0  (
	.D(\u_cordic/[13].U/n179_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [15])
);
defparam \u_cordic/[13].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_14_s0  (
	.D(\u_cordic/[13].U/n180_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [14])
);
defparam \u_cordic/[13].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_13_s0  (
	.D(\u_cordic/[13].U/n181_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [13])
);
defparam \u_cordic/[13].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_12_s0  (
	.D(\u_cordic/[13].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [12])
);
defparam \u_cordic/[13].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_11_s0  (
	.D(\u_cordic/[13].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [11])
);
defparam \u_cordic/[13].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_10_s0  (
	.D(\u_cordic/[13].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [10])
);
defparam \u_cordic/[13].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_9_s0  (
	.D(\u_cordic/[13].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [9])
);
defparam \u_cordic/[13].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_8_s0  (
	.D(\u_cordic/[13].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [8])
);
defparam \u_cordic/[13].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_7_s0  (
	.D(\u_cordic/[13].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [7])
);
defparam \u_cordic/[13].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_6_s0  (
	.D(\u_cordic/[13].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [6])
);
defparam \u_cordic/[13].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_5_s0  (
	.D(\u_cordic/[13].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [5])
);
defparam \u_cordic/[13].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_4_s0  (
	.D(\u_cordic/[13].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [4])
);
defparam \u_cordic/[13].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_3_s0  (
	.D(\u_cordic/[13].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [3])
);
defparam \u_cordic/[13].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_2_s0  (
	.D(\u_cordic/[13].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [2])
);
defparam \u_cordic/[13].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_1_s0  (
	.D(\u_cordic/[13].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [1])
);
defparam \u_cordic/[13].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_0_s0  (
	.D(\u_cordic/[13].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [0])
);
defparam \u_cordic/[13].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_16_s0  (
	.D(\u_cordic/[13].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [16])
);
defparam \u_cordic/[13].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_15_s0  (
	.D(\u_cordic/[13].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [15])
);
defparam \u_cordic/[13].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_14_s0  (
	.D(\u_cordic/[13].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [14])
);
defparam \u_cordic/[13].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_13_s0  (
	.D(\u_cordic/[13].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [13])
);
defparam \u_cordic/[13].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_12_s0  (
	.D(\u_cordic/[13].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [12])
);
defparam \u_cordic/[13].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_11_s0  (
	.D(\u_cordic/[13].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [11])
);
defparam \u_cordic/[13].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_10_s0  (
	.D(\u_cordic/[13].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [10])
);
defparam \u_cordic/[13].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_9_s0  (
	.D(\u_cordic/[13].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [9])
);
defparam \u_cordic/[13].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_8_s0  (
	.D(\u_cordic/[13].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [8])
);
defparam \u_cordic/[13].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_7_s0  (
	.D(\u_cordic/[13].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [7])
);
defparam \u_cordic/[13].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_6_s0  (
	.D(\u_cordic/[13].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [6])
);
defparam \u_cordic/[13].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_5_s0  (
	.D(\u_cordic/[13].U/n206_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [5])
);
defparam \u_cordic/[13].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_4_s0  (
	.D(\u_cordic/[13].U/n207_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [4])
);
defparam \u_cordic/[13].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_3_s0  (
	.D(\u_cordic/[13].U/n208_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [3])
);
defparam \u_cordic/[13].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_2_s0  (
	.D(\u_cordic/[13].U/n209_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [2])
);
defparam \u_cordic/[13].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_1_s0  (
	.D(\u_cordic/[13].U/n210_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [1])
);
defparam \u_cordic/[13].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/y_1_0_s0  (
	.D(\u_cordic/[13].U/n211_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[14] [0])
);
defparam \u_cordic/[13].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_16_s0  (
	.D(\u_cordic/[13].U/n212_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [16])
);
defparam \u_cordic/[13].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_15_s0  (
	.D(\u_cordic/[13].U/n213_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [15])
);
defparam \u_cordic/[13].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_14_s0  (
	.D(\u_cordic/[13].U/n214_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [14])
);
defparam \u_cordic/[13].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_13_s0  (
	.D(\u_cordic/[13].U/n215_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [13])
);
defparam \u_cordic/[13].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_12_s0  (
	.D(\u_cordic/[13].U/n216_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [12])
);
defparam \u_cordic/[13].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_11_s0  (
	.D(\u_cordic/[13].U/n217_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [11])
);
defparam \u_cordic/[13].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_10_s0  (
	.D(\u_cordic/[13].U/n218_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [10])
);
defparam \u_cordic/[13].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_9_s0  (
	.D(\u_cordic/[13].U/n219_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [9])
);
defparam \u_cordic/[13].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_8_s0  (
	.D(\u_cordic/[13].U/n220_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [8])
);
defparam \u_cordic/[13].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_7_s0  (
	.D(\u_cordic/[13].U/n221_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [7])
);
defparam \u_cordic/[13].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_6_s0  (
	.D(\u_cordic/[13].U/n222_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [6])
);
defparam \u_cordic/[13].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_5_s0  (
	.D(\u_cordic/[13].U/n223_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [5])
);
defparam \u_cordic/[13].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_4_s0  (
	.D(\u_cordic/[13].U/n224_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [4])
);
defparam \u_cordic/[13].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_3_s0  (
	.D(\u_cordic/[13].U/n225_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [3])
);
defparam \u_cordic/[13].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_2_s0  (
	.D(\u_cordic/[13].U/n226_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [2])
);
defparam \u_cordic/[13].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_1_s0  (
	.D(\u_cordic/z[13] [1]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [1])
);
defparam \u_cordic/[13].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/z_1_0_s0  (
	.D(\u_cordic/z[13] [0]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[14] [0])
);
defparam \u_cordic/[13].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[13].U/x_1_16_s0  (
	.D(\u_cordic/[13].U/n178_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[14] [16])
);
defparam \u_cordic/[13].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[13].U/n194_1_s  (
	.I0(\u_cordic/x[13] [0]),
	.I1(\u_cordic/y[13] [13]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/z[13][16]_1_5 ),
	.COUT(\u_cordic/[13].U/n194_1_1 ),
	.SUM(\u_cordic/[13].U/n194_2 )
);
defparam \u_cordic/[13].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n193_1_s  (
	.I0(\u_cordic/x[13] [1]),
	.I1(\u_cordic/y[13] [14]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n194_1_1 ),
	.COUT(\u_cordic/[13].U/n193_1_1 ),
	.SUM(\u_cordic/[13].U/n193_2 )
);
defparam \u_cordic/[13].U/n193_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n192_1_s  (
	.I0(\u_cordic/x[13] [2]),
	.I1(\u_cordic/y[13] [15]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n193_1_1 ),
	.COUT(\u_cordic/[13].U/n192_1_1 ),
	.SUM(\u_cordic/[13].U/n192_2 )
);
defparam \u_cordic/[13].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n191_1_s  (
	.I0(\u_cordic/x[13] [3]),
	.I1(\u_cordic/y[13] [16]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n192_1_1 ),
	.COUT(\u_cordic/[13].U/n191_1_1 ),
	.SUM(\u_cordic/[13].U/n191_2 )
);
defparam \u_cordic/[13].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n190_1_s  (
	.I0(\u_cordic/x[13] [4]),
	.I1(\u_cordic/y[13] [16]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n191_1_1 ),
	.COUT(\u_cordic/[13].U/n190_1_1 ),
	.SUM(\u_cordic/[13].U/n190_2 )
);
defparam \u_cordic/[13].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n189_1_s  (
	.I0(\u_cordic/x[13] [5]),
	.I1(\u_cordic/y[13] [16]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n190_1_1 ),
	.COUT(\u_cordic/[13].U/n189_1_1 ),
	.SUM(\u_cordic/[13].U/n189_2 )
);
defparam \u_cordic/[13].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n188_1_s  (
	.I0(\u_cordic/x[13] [6]),
	.I1(\u_cordic/y[13] [16]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n189_1_1 ),
	.COUT(\u_cordic/[13].U/n188_1_1 ),
	.SUM(\u_cordic/[13].U/n188_2 )
);
defparam \u_cordic/[13].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n187_1_s  (
	.I0(\u_cordic/x[13] [7]),
	.I1(\u_cordic/y[13] [16]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n188_1_1 ),
	.COUT(\u_cordic/[13].U/n187_1_1 ),
	.SUM(\u_cordic/[13].U/n187_2 )
);
defparam \u_cordic/[13].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n186_1_s  (
	.I0(\u_cordic/x[13] [8]),
	.I1(\u_cordic/y[13] [16]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n187_1_1 ),
	.COUT(\u_cordic/[13].U/n186_1_1 ),
	.SUM(\u_cordic/[13].U/n186_2 )
);
defparam \u_cordic/[13].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n185_1_s  (
	.I0(\u_cordic/x[13] [9]),
	.I1(\u_cordic/y[13] [16]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n186_1_1 ),
	.COUT(\u_cordic/[13].U/n185_1_1 ),
	.SUM(\u_cordic/[13].U/n185_2 )
);
defparam \u_cordic/[13].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n184_1_s  (
	.I0(\u_cordic/x[13] [10]),
	.I1(\u_cordic/y[13] [16]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n185_1_1 ),
	.COUT(\u_cordic/[13].U/n184_1_1 ),
	.SUM(\u_cordic/[13].U/n184_2 )
);
defparam \u_cordic/[13].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n183_1_s  (
	.I0(\u_cordic/x[13] [11]),
	.I1(\u_cordic/y[13] [16]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n184_1_1 ),
	.COUT(\u_cordic/[13].U/n183_1_1 ),
	.SUM(\u_cordic/[13].U/n183_2 )
);
defparam \u_cordic/[13].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n182_1_s  (
	.I0(\u_cordic/x[13] [12]),
	.I1(\u_cordic/y[13] [16]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n183_1_1 ),
	.COUT(\u_cordic/[13].U/n182_1_1 ),
	.SUM(\u_cordic/[13].U/n182_2 )
);
defparam \u_cordic/[13].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n181_1_s  (
	.I0(\u_cordic/x[13] [13]),
	.I1(\u_cordic/y[13] [16]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n182_1_1 ),
	.COUT(\u_cordic/[13].U/n181_1_1 ),
	.SUM(\u_cordic/[13].U/n181_2 )
);
defparam \u_cordic/[13].U/n181_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n180_1_s  (
	.I0(\u_cordic/x[13] [14]),
	.I1(\u_cordic/y[13] [16]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n181_1_1 ),
	.COUT(\u_cordic/[13].U/n180_1_1 ),
	.SUM(\u_cordic/[13].U/n180_2 )
);
defparam \u_cordic/[13].U/n180_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n179_1_s  (
	.I0(\u_cordic/x[13] [15]),
	.I1(\u_cordic/y[13] [16]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n180_1_1 ),
	.COUT(\u_cordic/[13].U/n179_1_1 ),
	.SUM(\u_cordic/[13].U/n179_2 )
);
defparam \u_cordic/[13].U/n179_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n178_1_s  (
	.I0(\u_cordic/x[13] [16]),
	.I1(\u_cordic/y[13] [16]),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n179_1_1 ),
	.COUT(\u_cordic/[13].U/n178_1_0_COUT ),
	.SUM(\u_cordic/[13].U/n178_2 )
);
defparam \u_cordic/[13].U/n178_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n226_1_s  (
	.I0(\u_cordic/z[13] [2]),
	.I1(VCC),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/z[13][16]_1_5 ),
	.COUT(\u_cordic/[13].U/n226_1_1 ),
	.SUM(\u_cordic/[13].U/n226_2 )
);
defparam \u_cordic/[13].U/n226_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n225_1_s  (
	.I0(\u_cordic/z[13] [3]),
	.I1(GND),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n226_1_1 ),
	.COUT(\u_cordic/[13].U/n225_1_1 ),
	.SUM(\u_cordic/[13].U/n225_2 )
);
defparam \u_cordic/[13].U/n225_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n224_1_s  (
	.I0(\u_cordic/z[13] [4]),
	.I1(GND),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n225_1_1 ),
	.COUT(\u_cordic/[13].U/n224_1_1 ),
	.SUM(\u_cordic/[13].U/n224_2 )
);
defparam \u_cordic/[13].U/n224_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n223_1_s  (
	.I0(\u_cordic/z[13] [5]),
	.I1(GND),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n224_1_1 ),
	.COUT(\u_cordic/[13].U/n223_1_1 ),
	.SUM(\u_cordic/[13].U/n223_2 )
);
defparam \u_cordic/[13].U/n223_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n222_1_s  (
	.I0(\u_cordic/z[13] [6]),
	.I1(GND),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n223_1_1 ),
	.COUT(\u_cordic/[13].U/n222_1_1 ),
	.SUM(\u_cordic/[13].U/n222_2 )
);
defparam \u_cordic/[13].U/n222_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n221_1_s  (
	.I0(\u_cordic/z[13] [7]),
	.I1(GND),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n222_1_1 ),
	.COUT(\u_cordic/[13].U/n221_1_1 ),
	.SUM(\u_cordic/[13].U/n221_2 )
);
defparam \u_cordic/[13].U/n221_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n220_1_s  (
	.I0(\u_cordic/z[13] [8]),
	.I1(GND),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n221_1_1 ),
	.COUT(\u_cordic/[13].U/n220_1_1 ),
	.SUM(\u_cordic/[13].U/n220_2 )
);
defparam \u_cordic/[13].U/n220_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n219_1_s  (
	.I0(\u_cordic/z[13] [9]),
	.I1(GND),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n220_1_1 ),
	.COUT(\u_cordic/[13].U/n219_1_1 ),
	.SUM(\u_cordic/[13].U/n219_2 )
);
defparam \u_cordic/[13].U/n219_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n218_1_s  (
	.I0(\u_cordic/z[13] [10]),
	.I1(GND),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n219_1_1 ),
	.COUT(\u_cordic/[13].U/n218_1_1 ),
	.SUM(\u_cordic/[13].U/n218_2 )
);
defparam \u_cordic/[13].U/n218_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n217_1_s  (
	.I0(\u_cordic/z[13] [11]),
	.I1(GND),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n218_1_1 ),
	.COUT(\u_cordic/[13].U/n217_1_1 ),
	.SUM(\u_cordic/[13].U/n217_2 )
);
defparam \u_cordic/[13].U/n217_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n216_1_s  (
	.I0(\u_cordic/z[13] [12]),
	.I1(GND),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n217_1_1 ),
	.COUT(\u_cordic/[13].U/n216_1_1 ),
	.SUM(\u_cordic/[13].U/n216_2 )
);
defparam \u_cordic/[13].U/n216_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n215_1_s  (
	.I0(\u_cordic/z[13] [13]),
	.I1(GND),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n216_1_1 ),
	.COUT(\u_cordic/[13].U/n215_1_1 ),
	.SUM(\u_cordic/[13].U/n215_2 )
);
defparam \u_cordic/[13].U/n215_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n214_1_s  (
	.I0(\u_cordic/z[13] [14]),
	.I1(GND),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n215_1_1 ),
	.COUT(\u_cordic/[13].U/n214_1_1 ),
	.SUM(\u_cordic/[13].U/n214_2 )
);
defparam \u_cordic/[13].U/n214_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n213_1_s  (
	.I0(\u_cordic/z[13] [15]),
	.I1(GND),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n214_1_1 ),
	.COUT(\u_cordic/[13].U/n213_1_1 ),
	.SUM(\u_cordic/[13].U/n213_2 )
);
defparam \u_cordic/[13].U/n213_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n212_1_s  (
	.I0(\u_cordic/z[13] [16]),
	.I1(GND),
	.I3(\u_cordic/z[13] [16]),
	.CIN(\u_cordic/[13].U/n213_1_1 ),
	.COUT(\u_cordic/[13].U/n212_1_0_COUT ),
	.SUM(\u_cordic/[13].U/n212_2 )
);
defparam \u_cordic/[13].U/n212_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n211_1_s  (
	.I0(\u_cordic/y[13] [0]),
	.I1(\u_cordic/x[13] [13]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/z[13] [16]),
	.COUT(\u_cordic/[13].U/n211_1_1 ),
	.SUM(\u_cordic/[13].U/n211_2 )
);
defparam \u_cordic/[13].U/n211_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n210_1_s  (
	.I0(\u_cordic/y[13] [1]),
	.I1(\u_cordic/x[13] [14]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n211_1_1 ),
	.COUT(\u_cordic/[13].U/n210_1_1 ),
	.SUM(\u_cordic/[13].U/n210_2 )
);
defparam \u_cordic/[13].U/n210_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n209_1_s  (
	.I0(\u_cordic/y[13] [2]),
	.I1(\u_cordic/x[13] [15]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n210_1_1 ),
	.COUT(\u_cordic/[13].U/n209_1_1 ),
	.SUM(\u_cordic/[13].U/n209_2 )
);
defparam \u_cordic/[13].U/n209_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n208_1_s  (
	.I0(\u_cordic/y[13] [3]),
	.I1(\u_cordic/x[13] [16]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n209_1_1 ),
	.COUT(\u_cordic/[13].U/n208_1_1 ),
	.SUM(\u_cordic/[13].U/n208_2 )
);
defparam \u_cordic/[13].U/n208_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n207_1_s  (
	.I0(\u_cordic/y[13] [4]),
	.I1(\u_cordic/x[13] [16]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n208_1_1 ),
	.COUT(\u_cordic/[13].U/n207_1_1 ),
	.SUM(\u_cordic/[13].U/n207_2 )
);
defparam \u_cordic/[13].U/n207_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n206_1_s  (
	.I0(\u_cordic/y[13] [5]),
	.I1(\u_cordic/x[13] [16]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n207_1_1 ),
	.COUT(\u_cordic/[13].U/n206_1_1 ),
	.SUM(\u_cordic/[13].U/n206_2 )
);
defparam \u_cordic/[13].U/n206_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n205_1_s  (
	.I0(\u_cordic/y[13] [6]),
	.I1(\u_cordic/x[13] [16]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n206_1_1 ),
	.COUT(\u_cordic/[13].U/n205_1_1 ),
	.SUM(\u_cordic/[13].U/n205_2 )
);
defparam \u_cordic/[13].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n204_1_s  (
	.I0(\u_cordic/y[13] [7]),
	.I1(\u_cordic/x[13] [16]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n205_1_1 ),
	.COUT(\u_cordic/[13].U/n204_1_1 ),
	.SUM(\u_cordic/[13].U/n204_2 )
);
defparam \u_cordic/[13].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n203_1_s  (
	.I0(\u_cordic/y[13] [8]),
	.I1(\u_cordic/x[13] [16]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n204_1_1 ),
	.COUT(\u_cordic/[13].U/n203_1_1 ),
	.SUM(\u_cordic/[13].U/n203_2 )
);
defparam \u_cordic/[13].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n202_1_s  (
	.I0(\u_cordic/y[13] [9]),
	.I1(\u_cordic/x[13] [16]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n203_1_1 ),
	.COUT(\u_cordic/[13].U/n202_1_1 ),
	.SUM(\u_cordic/[13].U/n202_2 )
);
defparam \u_cordic/[13].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n201_1_s  (
	.I0(\u_cordic/y[13] [10]),
	.I1(\u_cordic/x[13] [16]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n202_1_1 ),
	.COUT(\u_cordic/[13].U/n201_1_1 ),
	.SUM(\u_cordic/[13].U/n201_2 )
);
defparam \u_cordic/[13].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n200_1_s  (
	.I0(\u_cordic/y[13] [11]),
	.I1(\u_cordic/x[13] [16]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n201_1_1 ),
	.COUT(\u_cordic/[13].U/n200_1_1 ),
	.SUM(\u_cordic/[13].U/n200_2 )
);
defparam \u_cordic/[13].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n199_1_s  (
	.I0(\u_cordic/y[13] [12]),
	.I1(\u_cordic/x[13] [16]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n200_1_1 ),
	.COUT(\u_cordic/[13].U/n199_1_1 ),
	.SUM(\u_cordic/[13].U/n199_2 )
);
defparam \u_cordic/[13].U/n199_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n198_1_s  (
	.I0(\u_cordic/y[13] [13]),
	.I1(\u_cordic/x[13] [16]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n199_1_1 ),
	.COUT(\u_cordic/[13].U/n198_1_1 ),
	.SUM(\u_cordic/[13].U/n198_2 )
);
defparam \u_cordic/[13].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n197_1_s  (
	.I0(\u_cordic/y[13] [14]),
	.I1(\u_cordic/x[13] [16]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n198_1_1 ),
	.COUT(\u_cordic/[13].U/n197_1_1 ),
	.SUM(\u_cordic/[13].U/n197_2 )
);
defparam \u_cordic/[13].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n196_1_s  (
	.I0(\u_cordic/y[13] [15]),
	.I1(\u_cordic/x[13] [16]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n197_1_1 ),
	.COUT(\u_cordic/[13].U/n196_1_1 ),
	.SUM(\u_cordic/[13].U/n196_2 )
);
defparam \u_cordic/[13].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[13].U/n195_1_s  (
	.I0(\u_cordic/y[13] [16]),
	.I1(\u_cordic/x[13] [16]),
	.I3(\u_cordic/z[13][16]_1_5 ),
	.CIN(\u_cordic/[13].U/n196_1_1 ),
	.COUT(\u_cordic/[13].U/n195_1_0_COUT ),
	.SUM(\u_cordic/[13].U/n195_2 )
);
defparam \u_cordic/[13].U/n195_1_s .ALU_MODE=2;
LUT1 \u_cordic/[13].U/z[14][16]_1_s3  (
	.I0(\u_cordic/z[14] [16]),
	.F(\u_cordic/z[14][16]_1_5 )
);
defparam \u_cordic/[13].U/z[14][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[14].U/x_1_15_s0  (
	.D(\u_cordic/[14].U/n181_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [15])
);
defparam \u_cordic/[14].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_14_s0  (
	.D(\u_cordic/[14].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [14])
);
defparam \u_cordic/[14].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_13_s0  (
	.D(\u_cordic/[14].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [13])
);
defparam \u_cordic/[14].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_12_s0  (
	.D(\u_cordic/[14].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [12])
);
defparam \u_cordic/[14].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_11_s0  (
	.D(\u_cordic/[14].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [11])
);
defparam \u_cordic/[14].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_10_s0  (
	.D(\u_cordic/[14].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [10])
);
defparam \u_cordic/[14].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_9_s0  (
	.D(\u_cordic/[14].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [9])
);
defparam \u_cordic/[14].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_8_s0  (
	.D(\u_cordic/[14].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [8])
);
defparam \u_cordic/[14].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_7_s0  (
	.D(\u_cordic/[14].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [7])
);
defparam \u_cordic/[14].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_6_s0  (
	.D(\u_cordic/[14].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [6])
);
defparam \u_cordic/[14].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_5_s0  (
	.D(\u_cordic/[14].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [5])
);
defparam \u_cordic/[14].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_4_s0  (
	.D(\u_cordic/[14].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [4])
);
defparam \u_cordic/[14].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_3_s0  (
	.D(\u_cordic/[14].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [3])
);
defparam \u_cordic/[14].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_2_s0  (
	.D(\u_cordic/[14].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [2])
);
defparam \u_cordic/[14].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_1_s0  (
	.D(\u_cordic/[14].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [1])
);
defparam \u_cordic/[14].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_0_s0  (
	.D(\u_cordic/[14].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [0])
);
defparam \u_cordic/[14].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_16_s0  (
	.D(\u_cordic/[14].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [16])
);
defparam \u_cordic/[14].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_15_s0  (
	.D(\u_cordic/[14].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [15])
);
defparam \u_cordic/[14].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_14_s0  (
	.D(\u_cordic/[14].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [14])
);
defparam \u_cordic/[14].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_13_s0  (
	.D(\u_cordic/[14].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [13])
);
defparam \u_cordic/[14].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_12_s0  (
	.D(\u_cordic/[14].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [12])
);
defparam \u_cordic/[14].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_11_s0  (
	.D(\u_cordic/[14].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [11])
);
defparam \u_cordic/[14].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_10_s0  (
	.D(\u_cordic/[14].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [10])
);
defparam \u_cordic/[14].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_9_s0  (
	.D(\u_cordic/[14].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [9])
);
defparam \u_cordic/[14].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_8_s0  (
	.D(\u_cordic/[14].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [8])
);
defparam \u_cordic/[14].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_7_s0  (
	.D(\u_cordic/[14].U/n206_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [7])
);
defparam \u_cordic/[14].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_6_s0  (
	.D(\u_cordic/[14].U/n207_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [6])
);
defparam \u_cordic/[14].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_5_s0  (
	.D(\u_cordic/[14].U/n208_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [5])
);
defparam \u_cordic/[14].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_4_s0  (
	.D(\u_cordic/[14].U/n209_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [4])
);
defparam \u_cordic/[14].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_3_s0  (
	.D(\u_cordic/[14].U/n210_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [3])
);
defparam \u_cordic/[14].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_2_s0  (
	.D(\u_cordic/[14].U/n211_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [2])
);
defparam \u_cordic/[14].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_1_s0  (
	.D(\u_cordic/[14].U/n212_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [1])
);
defparam \u_cordic/[14].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/y_1_0_s0  (
	.D(\u_cordic/[14].U/n213_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/y[15] [0])
);
defparam \u_cordic/[14].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_16_s0  (
	.D(\u_cordic/[14].U/n214_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [16])
);
defparam \u_cordic/[14].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_15_s0  (
	.D(\u_cordic/[14].U/n215_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [15])
);
defparam \u_cordic/[14].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_14_s0  (
	.D(\u_cordic/[14].U/n216_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [14])
);
defparam \u_cordic/[14].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_13_s0  (
	.D(\u_cordic/[14].U/n217_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [13])
);
defparam \u_cordic/[14].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_12_s0  (
	.D(\u_cordic/[14].U/n218_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [12])
);
defparam \u_cordic/[14].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_11_s0  (
	.D(\u_cordic/[14].U/n219_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [11])
);
defparam \u_cordic/[14].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_10_s0  (
	.D(\u_cordic/[14].U/n220_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [10])
);
defparam \u_cordic/[14].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_9_s0  (
	.D(\u_cordic/[14].U/n221_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [9])
);
defparam \u_cordic/[14].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_8_s0  (
	.D(\u_cordic/[14].U/n222_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [8])
);
defparam \u_cordic/[14].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_7_s0  (
	.D(\u_cordic/[14].U/n223_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [7])
);
defparam \u_cordic/[14].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_6_s0  (
	.D(\u_cordic/[14].U/n224_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [6])
);
defparam \u_cordic/[14].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_5_s0  (
	.D(\u_cordic/[14].U/n225_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [5])
);
defparam \u_cordic/[14].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_4_s0  (
	.D(\u_cordic/[14].U/n226_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [4])
);
defparam \u_cordic/[14].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_3_s0  (
	.D(\u_cordic/[14].U/n227_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [3])
);
defparam \u_cordic/[14].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_2_s0  (
	.D(\u_cordic/[14].U/n228_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [2])
);
defparam \u_cordic/[14].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_1_s0  (
	.D(\u_cordic/[14].U/n229_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [1])
);
defparam \u_cordic/[14].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/z_1_0_s0  (
	.D(\u_cordic/z[14] [0]),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/z[15] [0])
);
defparam \u_cordic/[14].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[14].U/x_1_16_s0  (
	.D(\u_cordic/[14].U/n180_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(\u_cordic/x[15] [16])
);
defparam \u_cordic/[14].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[14].U/n196_1_s  (
	.I0(\u_cordic/x[14] [0]),
	.I1(\u_cordic/y[14] [14]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/z[14][16]_1_5 ),
	.COUT(\u_cordic/[14].U/n196_1_1 ),
	.SUM(\u_cordic/[14].U/n196_2 )
);
defparam \u_cordic/[14].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n195_1_s  (
	.I0(\u_cordic/x[14] [1]),
	.I1(\u_cordic/y[14] [15]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n196_1_1 ),
	.COUT(\u_cordic/[14].U/n195_1_1 ),
	.SUM(\u_cordic/[14].U/n195_2 )
);
defparam \u_cordic/[14].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n194_1_s  (
	.I0(\u_cordic/x[14] [2]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n195_1_1 ),
	.COUT(\u_cordic/[14].U/n194_1_1 ),
	.SUM(\u_cordic/[14].U/n194_2 )
);
defparam \u_cordic/[14].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n193_1_s  (
	.I0(\u_cordic/x[14] [3]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n194_1_1 ),
	.COUT(\u_cordic/[14].U/n193_1_1 ),
	.SUM(\u_cordic/[14].U/n193_2 )
);
defparam \u_cordic/[14].U/n193_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n192_1_s  (
	.I0(\u_cordic/x[14] [4]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n193_1_1 ),
	.COUT(\u_cordic/[14].U/n192_1_1 ),
	.SUM(\u_cordic/[14].U/n192_2 )
);
defparam \u_cordic/[14].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n191_1_s  (
	.I0(\u_cordic/x[14] [5]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n192_1_1 ),
	.COUT(\u_cordic/[14].U/n191_1_1 ),
	.SUM(\u_cordic/[14].U/n191_2 )
);
defparam \u_cordic/[14].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n190_1_s  (
	.I0(\u_cordic/x[14] [6]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n191_1_1 ),
	.COUT(\u_cordic/[14].U/n190_1_1 ),
	.SUM(\u_cordic/[14].U/n190_2 )
);
defparam \u_cordic/[14].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n189_1_s  (
	.I0(\u_cordic/x[14] [7]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n190_1_1 ),
	.COUT(\u_cordic/[14].U/n189_1_1 ),
	.SUM(\u_cordic/[14].U/n189_2 )
);
defparam \u_cordic/[14].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n188_1_s  (
	.I0(\u_cordic/x[14] [8]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n189_1_1 ),
	.COUT(\u_cordic/[14].U/n188_1_1 ),
	.SUM(\u_cordic/[14].U/n188_2 )
);
defparam \u_cordic/[14].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n187_1_s  (
	.I0(\u_cordic/x[14] [9]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n188_1_1 ),
	.COUT(\u_cordic/[14].U/n187_1_1 ),
	.SUM(\u_cordic/[14].U/n187_2 )
);
defparam \u_cordic/[14].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n186_1_s  (
	.I0(\u_cordic/x[14] [10]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n187_1_1 ),
	.COUT(\u_cordic/[14].U/n186_1_1 ),
	.SUM(\u_cordic/[14].U/n186_2 )
);
defparam \u_cordic/[14].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n185_1_s  (
	.I0(\u_cordic/x[14] [11]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n186_1_1 ),
	.COUT(\u_cordic/[14].U/n185_1_1 ),
	.SUM(\u_cordic/[14].U/n185_2 )
);
defparam \u_cordic/[14].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n184_1_s  (
	.I0(\u_cordic/x[14] [12]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n185_1_1 ),
	.COUT(\u_cordic/[14].U/n184_1_1 ),
	.SUM(\u_cordic/[14].U/n184_2 )
);
defparam \u_cordic/[14].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n183_1_s  (
	.I0(\u_cordic/x[14] [13]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n184_1_1 ),
	.COUT(\u_cordic/[14].U/n183_1_1 ),
	.SUM(\u_cordic/[14].U/n183_2 )
);
defparam \u_cordic/[14].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n182_1_s  (
	.I0(\u_cordic/x[14] [14]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n183_1_1 ),
	.COUT(\u_cordic/[14].U/n182_1_1 ),
	.SUM(\u_cordic/[14].U/n182_2 )
);
defparam \u_cordic/[14].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n181_1_s  (
	.I0(\u_cordic/x[14] [15]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n182_1_1 ),
	.COUT(\u_cordic/[14].U/n181_1_1 ),
	.SUM(\u_cordic/[14].U/n181_2 )
);
defparam \u_cordic/[14].U/n181_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n180_1_s  (
	.I0(\u_cordic/x[14] [16]),
	.I1(\u_cordic/y[14] [16]),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n181_1_1 ),
	.COUT(\u_cordic/[14].U/n180_1_0_COUT ),
	.SUM(\u_cordic/[14].U/n180_2 )
);
defparam \u_cordic/[14].U/n180_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n229_1_s  (
	.I0(\u_cordic/z[14] [1]),
	.I1(VCC),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/z[14][16]_1_5 ),
	.COUT(\u_cordic/[14].U/n229_1_1 ),
	.SUM(\u_cordic/[14].U/n229_2 )
);
defparam \u_cordic/[14].U/n229_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n228_1_s  (
	.I0(\u_cordic/z[14] [2]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n229_1_1 ),
	.COUT(\u_cordic/[14].U/n228_1_1 ),
	.SUM(\u_cordic/[14].U/n228_2 )
);
defparam \u_cordic/[14].U/n228_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n227_1_s  (
	.I0(\u_cordic/z[14] [3]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n228_1_1 ),
	.COUT(\u_cordic/[14].U/n227_1_1 ),
	.SUM(\u_cordic/[14].U/n227_2 )
);
defparam \u_cordic/[14].U/n227_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n226_1_s  (
	.I0(\u_cordic/z[14] [4]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n227_1_1 ),
	.COUT(\u_cordic/[14].U/n226_1_1 ),
	.SUM(\u_cordic/[14].U/n226_2 )
);
defparam \u_cordic/[14].U/n226_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n225_1_s  (
	.I0(\u_cordic/z[14] [5]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n226_1_1 ),
	.COUT(\u_cordic/[14].U/n225_1_1 ),
	.SUM(\u_cordic/[14].U/n225_2 )
);
defparam \u_cordic/[14].U/n225_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n224_1_s  (
	.I0(\u_cordic/z[14] [6]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n225_1_1 ),
	.COUT(\u_cordic/[14].U/n224_1_1 ),
	.SUM(\u_cordic/[14].U/n224_2 )
);
defparam \u_cordic/[14].U/n224_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n223_1_s  (
	.I0(\u_cordic/z[14] [7]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n224_1_1 ),
	.COUT(\u_cordic/[14].U/n223_1_1 ),
	.SUM(\u_cordic/[14].U/n223_2 )
);
defparam \u_cordic/[14].U/n223_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n222_1_s  (
	.I0(\u_cordic/z[14] [8]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n223_1_1 ),
	.COUT(\u_cordic/[14].U/n222_1_1 ),
	.SUM(\u_cordic/[14].U/n222_2 )
);
defparam \u_cordic/[14].U/n222_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n221_1_s  (
	.I0(\u_cordic/z[14] [9]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n222_1_1 ),
	.COUT(\u_cordic/[14].U/n221_1_1 ),
	.SUM(\u_cordic/[14].U/n221_2 )
);
defparam \u_cordic/[14].U/n221_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n220_1_s  (
	.I0(\u_cordic/z[14] [10]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n221_1_1 ),
	.COUT(\u_cordic/[14].U/n220_1_1 ),
	.SUM(\u_cordic/[14].U/n220_2 )
);
defparam \u_cordic/[14].U/n220_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n219_1_s  (
	.I0(\u_cordic/z[14] [11]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n220_1_1 ),
	.COUT(\u_cordic/[14].U/n219_1_1 ),
	.SUM(\u_cordic/[14].U/n219_2 )
);
defparam \u_cordic/[14].U/n219_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n218_1_s  (
	.I0(\u_cordic/z[14] [12]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n219_1_1 ),
	.COUT(\u_cordic/[14].U/n218_1_1 ),
	.SUM(\u_cordic/[14].U/n218_2 )
);
defparam \u_cordic/[14].U/n218_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n217_1_s  (
	.I0(\u_cordic/z[14] [13]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n218_1_1 ),
	.COUT(\u_cordic/[14].U/n217_1_1 ),
	.SUM(\u_cordic/[14].U/n217_2 )
);
defparam \u_cordic/[14].U/n217_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n216_1_s  (
	.I0(\u_cordic/z[14] [14]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n217_1_1 ),
	.COUT(\u_cordic/[14].U/n216_1_1 ),
	.SUM(\u_cordic/[14].U/n216_2 )
);
defparam \u_cordic/[14].U/n216_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n215_1_s  (
	.I0(\u_cordic/z[14] [15]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n216_1_1 ),
	.COUT(\u_cordic/[14].U/n215_1_1 ),
	.SUM(\u_cordic/[14].U/n215_2 )
);
defparam \u_cordic/[14].U/n215_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n214_1_s  (
	.I0(\u_cordic/z[14] [16]),
	.I1(GND),
	.I3(\u_cordic/z[14] [16]),
	.CIN(\u_cordic/[14].U/n215_1_1 ),
	.COUT(\u_cordic/[14].U/n214_1_0_COUT ),
	.SUM(\u_cordic/[14].U/n214_2 )
);
defparam \u_cordic/[14].U/n214_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n213_1_s  (
	.I0(\u_cordic/y[14] [0]),
	.I1(\u_cordic/x[14] [14]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/z[14] [16]),
	.COUT(\u_cordic/[14].U/n213_1_1 ),
	.SUM(\u_cordic/[14].U/n213_2 )
);
defparam \u_cordic/[14].U/n213_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n212_1_s  (
	.I0(\u_cordic/y[14] [1]),
	.I1(\u_cordic/x[14] [15]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n213_1_1 ),
	.COUT(\u_cordic/[14].U/n212_1_1 ),
	.SUM(\u_cordic/[14].U/n212_2 )
);
defparam \u_cordic/[14].U/n212_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n211_1_s  (
	.I0(\u_cordic/y[14] [2]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n212_1_1 ),
	.COUT(\u_cordic/[14].U/n211_1_1 ),
	.SUM(\u_cordic/[14].U/n211_2 )
);
defparam \u_cordic/[14].U/n211_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n210_1_s  (
	.I0(\u_cordic/y[14] [3]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n211_1_1 ),
	.COUT(\u_cordic/[14].U/n210_1_1 ),
	.SUM(\u_cordic/[14].U/n210_2 )
);
defparam \u_cordic/[14].U/n210_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n209_1_s  (
	.I0(\u_cordic/y[14] [4]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n210_1_1 ),
	.COUT(\u_cordic/[14].U/n209_1_1 ),
	.SUM(\u_cordic/[14].U/n209_2 )
);
defparam \u_cordic/[14].U/n209_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n208_1_s  (
	.I0(\u_cordic/y[14] [5]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n209_1_1 ),
	.COUT(\u_cordic/[14].U/n208_1_1 ),
	.SUM(\u_cordic/[14].U/n208_2 )
);
defparam \u_cordic/[14].U/n208_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n207_1_s  (
	.I0(\u_cordic/y[14] [6]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n208_1_1 ),
	.COUT(\u_cordic/[14].U/n207_1_1 ),
	.SUM(\u_cordic/[14].U/n207_2 )
);
defparam \u_cordic/[14].U/n207_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n206_1_s  (
	.I0(\u_cordic/y[14] [7]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n207_1_1 ),
	.COUT(\u_cordic/[14].U/n206_1_1 ),
	.SUM(\u_cordic/[14].U/n206_2 )
);
defparam \u_cordic/[14].U/n206_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n205_1_s  (
	.I0(\u_cordic/y[14] [8]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n206_1_1 ),
	.COUT(\u_cordic/[14].U/n205_1_1 ),
	.SUM(\u_cordic/[14].U/n205_2 )
);
defparam \u_cordic/[14].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n204_1_s  (
	.I0(\u_cordic/y[14] [9]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n205_1_1 ),
	.COUT(\u_cordic/[14].U/n204_1_1 ),
	.SUM(\u_cordic/[14].U/n204_2 )
);
defparam \u_cordic/[14].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n203_1_s  (
	.I0(\u_cordic/y[14] [10]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n204_1_1 ),
	.COUT(\u_cordic/[14].U/n203_1_1 ),
	.SUM(\u_cordic/[14].U/n203_2 )
);
defparam \u_cordic/[14].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n202_1_s  (
	.I0(\u_cordic/y[14] [11]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n203_1_1 ),
	.COUT(\u_cordic/[14].U/n202_1_1 ),
	.SUM(\u_cordic/[14].U/n202_2 )
);
defparam \u_cordic/[14].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n201_1_s  (
	.I0(\u_cordic/y[14] [12]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n202_1_1 ),
	.COUT(\u_cordic/[14].U/n201_1_1 ),
	.SUM(\u_cordic/[14].U/n201_2 )
);
defparam \u_cordic/[14].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n200_1_s  (
	.I0(\u_cordic/y[14] [13]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n201_1_1 ),
	.COUT(\u_cordic/[14].U/n200_1_1 ),
	.SUM(\u_cordic/[14].U/n200_2 )
);
defparam \u_cordic/[14].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n199_1_s  (
	.I0(\u_cordic/y[14] [14]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n200_1_1 ),
	.COUT(\u_cordic/[14].U/n199_1_1 ),
	.SUM(\u_cordic/[14].U/n199_2 )
);
defparam \u_cordic/[14].U/n199_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n198_1_s  (
	.I0(\u_cordic/y[14] [15]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n199_1_1 ),
	.COUT(\u_cordic/[14].U/n198_1_1 ),
	.SUM(\u_cordic/[14].U/n198_2 )
);
defparam \u_cordic/[14].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[14].U/n197_1_s  (
	.I0(\u_cordic/y[14] [16]),
	.I1(\u_cordic/x[14] [16]),
	.I3(\u_cordic/z[14][16]_1_5 ),
	.CIN(\u_cordic/[14].U/n198_1_1 ),
	.COUT(\u_cordic/[14].U/n197_1_0_COUT ),
	.SUM(\u_cordic/[14].U/n197_2 )
);
defparam \u_cordic/[14].U/n197_1_s .ALU_MODE=2;
LUT1 \u_cordic/[14].U/z[15][16]_1_s3  (
	.I0(\u_cordic/z[15] [16]),
	.F(\u_cordic/z[15][16]_1_5 )
);
defparam \u_cordic/[14].U/z[15][16]_1_s3 .INIT=2'h1;
DFFR \u_cordic/[15].U/x_1_15_s0  (
	.D(\u_cordic/[15].U/n183_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[15])
);
defparam \u_cordic/[15].U/x_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_14_s0  (
	.D(\u_cordic/[15].U/n184_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[14])
);
defparam \u_cordic/[15].U/x_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_13_s0  (
	.D(\u_cordic/[15].U/n185_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[13])
);
defparam \u_cordic/[15].U/x_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_12_s0  (
	.D(\u_cordic/[15].U/n186_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[12])
);
defparam \u_cordic/[15].U/x_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_11_s0  (
	.D(\u_cordic/[15].U/n187_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[11])
);
defparam \u_cordic/[15].U/x_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_10_s0  (
	.D(\u_cordic/[15].U/n188_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[10])
);
defparam \u_cordic/[15].U/x_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_9_s0  (
	.D(\u_cordic/[15].U/n189_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[9])
);
defparam \u_cordic/[15].U/x_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_8_s0  (
	.D(\u_cordic/[15].U/n190_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[8])
);
defparam \u_cordic/[15].U/x_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_7_s0  (
	.D(\u_cordic/[15].U/n191_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[7])
);
defparam \u_cordic/[15].U/x_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_6_s0  (
	.D(\u_cordic/[15].U/n192_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[6])
);
defparam \u_cordic/[15].U/x_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_5_s0  (
	.D(\u_cordic/[15].U/n193_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[5])
);
defparam \u_cordic/[15].U/x_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_4_s0  (
	.D(\u_cordic/[15].U/n194_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[4])
);
defparam \u_cordic/[15].U/x_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_3_s0  (
	.D(\u_cordic/[15].U/n195_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[3])
);
defparam \u_cordic/[15].U/x_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_2_s0  (
	.D(\u_cordic/[15].U/n196_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[2])
);
defparam \u_cordic/[15].U/x_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_1_s0  (
	.D(\u_cordic/[15].U/n197_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[1])
);
defparam \u_cordic/[15].U/x_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_0_s0  (
	.D(\u_cordic/[15].U/n198_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[0])
);
defparam \u_cordic/[15].U/x_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_16_s0  (
	.D(\u_cordic/[15].U/n199_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[16])
);
defparam \u_cordic/[15].U/y_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_15_s0  (
	.D(\u_cordic/[15].U/n200_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[15])
);
defparam \u_cordic/[15].U/y_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_14_s0  (
	.D(\u_cordic/[15].U/n201_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[14])
);
defparam \u_cordic/[15].U/y_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_13_s0  (
	.D(\u_cordic/[15].U/n202_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[13])
);
defparam \u_cordic/[15].U/y_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_12_s0  (
	.D(\u_cordic/[15].U/n203_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[12])
);
defparam \u_cordic/[15].U/y_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_11_s0  (
	.D(\u_cordic/[15].U/n204_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[11])
);
defparam \u_cordic/[15].U/y_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_10_s0  (
	.D(\u_cordic/[15].U/n205_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[10])
);
defparam \u_cordic/[15].U/y_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_9_s0  (
	.D(\u_cordic/[15].U/n206_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[9])
);
defparam \u_cordic/[15].U/y_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_8_s0  (
	.D(\u_cordic/[15].U/n207_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[8])
);
defparam \u_cordic/[15].U/y_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_7_s0  (
	.D(\u_cordic/[15].U/n208_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[7])
);
defparam \u_cordic/[15].U/y_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_6_s0  (
	.D(\u_cordic/[15].U/n209_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[6])
);
defparam \u_cordic/[15].U/y_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_5_s0  (
	.D(\u_cordic/[15].U/n210_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[5])
);
defparam \u_cordic/[15].U/y_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_4_s0  (
	.D(\u_cordic/[15].U/n211_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[4])
);
defparam \u_cordic/[15].U/y_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_3_s0  (
	.D(\u_cordic/[15].U/n212_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[3])
);
defparam \u_cordic/[15].U/y_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_2_s0  (
	.D(\u_cordic/[15].U/n213_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[2])
);
defparam \u_cordic/[15].U/y_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_1_s0  (
	.D(\u_cordic/[15].U/n214_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[1])
);
defparam \u_cordic/[15].U/y_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/y_1_0_s0  (
	.D(\u_cordic/[15].U/n215_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(y_o[0])
);
defparam \u_cordic/[15].U/y_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_16_s0  (
	.D(\u_cordic/[15].U/n216_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[16])
);
defparam \u_cordic/[15].U/z_1_16_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_15_s0  (
	.D(\u_cordic/[15].U/n217_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[15])
);
defparam \u_cordic/[15].U/z_1_15_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_14_s0  (
	.D(\u_cordic/[15].U/n218_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[14])
);
defparam \u_cordic/[15].U/z_1_14_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_13_s0  (
	.D(\u_cordic/[15].U/n219_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[13])
);
defparam \u_cordic/[15].U/z_1_13_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_12_s0  (
	.D(\u_cordic/[15].U/n220_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[12])
);
defparam \u_cordic/[15].U/z_1_12_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_11_s0  (
	.D(\u_cordic/[15].U/n221_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[11])
);
defparam \u_cordic/[15].U/z_1_11_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_10_s0  (
	.D(\u_cordic/[15].U/n222_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[10])
);
defparam \u_cordic/[15].U/z_1_10_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_9_s0  (
	.D(\u_cordic/[15].U/n223_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[9])
);
defparam \u_cordic/[15].U/z_1_9_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_8_s0  (
	.D(\u_cordic/[15].U/n224_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[8])
);
defparam \u_cordic/[15].U/z_1_8_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_7_s0  (
	.D(\u_cordic/[15].U/n225_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[7])
);
defparam \u_cordic/[15].U/z_1_7_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_6_s0  (
	.D(\u_cordic/[15].U/n226_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[6])
);
defparam \u_cordic/[15].U/z_1_6_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_5_s0  (
	.D(\u_cordic/[15].U/n227_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[5])
);
defparam \u_cordic/[15].U/z_1_5_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_4_s0  (
	.D(\u_cordic/[15].U/n228_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[4])
);
defparam \u_cordic/[15].U/z_1_4_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_3_s0  (
	.D(\u_cordic/[15].U/n229_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[3])
);
defparam \u_cordic/[15].U/z_1_3_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_2_s0  (
	.D(\u_cordic/[15].U/n230_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[2])
);
defparam \u_cordic/[15].U/z_1_2_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_1_s0  (
	.D(\u_cordic/[15].U/n231_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[1])
);
defparam \u_cordic/[15].U/z_1_1_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/z_1_0_s0  (
	.D(\u_cordic/[15].U/n232_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(theta_o[0])
);
defparam \u_cordic/[15].U/z_1_0_s0 .INIT=1'b0;
DFFR \u_cordic/[15].U/x_1_16_s0  (
	.D(\u_cordic/[15].U/n182_2 ),
	.CLK(clk),
	.RESET(rst),
	.Q(x_o[16])
);
defparam \u_cordic/[15].U/x_1_16_s0 .INIT=1'b0;
ALU \u_cordic/[15].U/n198_1_s  (
	.I0(\u_cordic/x[15] [0]),
	.I1(\u_cordic/y[15] [15]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/z[15][16]_1_5 ),
	.COUT(\u_cordic/[15].U/n198_1_1 ),
	.SUM(\u_cordic/[15].U/n198_2 )
);
defparam \u_cordic/[15].U/n198_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n197_1_s  (
	.I0(\u_cordic/x[15] [1]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n198_1_1 ),
	.COUT(\u_cordic/[15].U/n197_1_1 ),
	.SUM(\u_cordic/[15].U/n197_2 )
);
defparam \u_cordic/[15].U/n197_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n196_1_s  (
	.I0(\u_cordic/x[15] [2]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n197_1_1 ),
	.COUT(\u_cordic/[15].U/n196_1_1 ),
	.SUM(\u_cordic/[15].U/n196_2 )
);
defparam \u_cordic/[15].U/n196_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n195_1_s  (
	.I0(\u_cordic/x[15] [3]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n196_1_1 ),
	.COUT(\u_cordic/[15].U/n195_1_1 ),
	.SUM(\u_cordic/[15].U/n195_2 )
);
defparam \u_cordic/[15].U/n195_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n194_1_s  (
	.I0(\u_cordic/x[15] [4]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n195_1_1 ),
	.COUT(\u_cordic/[15].U/n194_1_1 ),
	.SUM(\u_cordic/[15].U/n194_2 )
);
defparam \u_cordic/[15].U/n194_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n193_1_s  (
	.I0(\u_cordic/x[15] [5]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n194_1_1 ),
	.COUT(\u_cordic/[15].U/n193_1_1 ),
	.SUM(\u_cordic/[15].U/n193_2 )
);
defparam \u_cordic/[15].U/n193_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n192_1_s  (
	.I0(\u_cordic/x[15] [6]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n193_1_1 ),
	.COUT(\u_cordic/[15].U/n192_1_1 ),
	.SUM(\u_cordic/[15].U/n192_2 )
);
defparam \u_cordic/[15].U/n192_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n191_1_s  (
	.I0(\u_cordic/x[15] [7]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n192_1_1 ),
	.COUT(\u_cordic/[15].U/n191_1_1 ),
	.SUM(\u_cordic/[15].U/n191_2 )
);
defparam \u_cordic/[15].U/n191_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n190_1_s  (
	.I0(\u_cordic/x[15] [8]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n191_1_1 ),
	.COUT(\u_cordic/[15].U/n190_1_1 ),
	.SUM(\u_cordic/[15].U/n190_2 )
);
defparam \u_cordic/[15].U/n190_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n189_1_s  (
	.I0(\u_cordic/x[15] [9]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n190_1_1 ),
	.COUT(\u_cordic/[15].U/n189_1_1 ),
	.SUM(\u_cordic/[15].U/n189_2 )
);
defparam \u_cordic/[15].U/n189_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n188_1_s  (
	.I0(\u_cordic/x[15] [10]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n189_1_1 ),
	.COUT(\u_cordic/[15].U/n188_1_1 ),
	.SUM(\u_cordic/[15].U/n188_2 )
);
defparam \u_cordic/[15].U/n188_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n187_1_s  (
	.I0(\u_cordic/x[15] [11]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n188_1_1 ),
	.COUT(\u_cordic/[15].U/n187_1_1 ),
	.SUM(\u_cordic/[15].U/n187_2 )
);
defparam \u_cordic/[15].U/n187_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n186_1_s  (
	.I0(\u_cordic/x[15] [12]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n187_1_1 ),
	.COUT(\u_cordic/[15].U/n186_1_1 ),
	.SUM(\u_cordic/[15].U/n186_2 )
);
defparam \u_cordic/[15].U/n186_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n185_1_s  (
	.I0(\u_cordic/x[15] [13]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n186_1_1 ),
	.COUT(\u_cordic/[15].U/n185_1_1 ),
	.SUM(\u_cordic/[15].U/n185_2 )
);
defparam \u_cordic/[15].U/n185_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n184_1_s  (
	.I0(\u_cordic/x[15] [14]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n185_1_1 ),
	.COUT(\u_cordic/[15].U/n184_1_1 ),
	.SUM(\u_cordic/[15].U/n184_2 )
);
defparam \u_cordic/[15].U/n184_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n183_1_s  (
	.I0(\u_cordic/x[15] [15]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n184_1_1 ),
	.COUT(\u_cordic/[15].U/n183_1_1 ),
	.SUM(\u_cordic/[15].U/n183_2 )
);
defparam \u_cordic/[15].U/n183_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n182_1_s  (
	.I0(\u_cordic/x[15] [16]),
	.I1(\u_cordic/y[15] [16]),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n183_1_1 ),
	.COUT(\u_cordic/[15].U/n182_1_0_COUT ),
	.SUM(\u_cordic/[15].U/n182_2 )
);
defparam \u_cordic/[15].U/n182_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n232_1_s  (
	.I0(\u_cordic/z[15] [0]),
	.I1(VCC),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/z[15][16]_1_5 ),
	.COUT(\u_cordic/[15].U/n232_1_1 ),
	.SUM(\u_cordic/[15].U/n232_2 )
);
defparam \u_cordic/[15].U/n232_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n231_1_s  (
	.I0(\u_cordic/z[15] [1]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n232_1_1 ),
	.COUT(\u_cordic/[15].U/n231_1_1 ),
	.SUM(\u_cordic/[15].U/n231_2 )
);
defparam \u_cordic/[15].U/n231_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n230_1_s  (
	.I0(\u_cordic/z[15] [2]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n231_1_1 ),
	.COUT(\u_cordic/[15].U/n230_1_1 ),
	.SUM(\u_cordic/[15].U/n230_2 )
);
defparam \u_cordic/[15].U/n230_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n229_1_s  (
	.I0(\u_cordic/z[15] [3]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n230_1_1 ),
	.COUT(\u_cordic/[15].U/n229_1_1 ),
	.SUM(\u_cordic/[15].U/n229_2 )
);
defparam \u_cordic/[15].U/n229_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n228_1_s  (
	.I0(\u_cordic/z[15] [4]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n229_1_1 ),
	.COUT(\u_cordic/[15].U/n228_1_1 ),
	.SUM(\u_cordic/[15].U/n228_2 )
);
defparam \u_cordic/[15].U/n228_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n227_1_s  (
	.I0(\u_cordic/z[15] [5]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n228_1_1 ),
	.COUT(\u_cordic/[15].U/n227_1_1 ),
	.SUM(\u_cordic/[15].U/n227_2 )
);
defparam \u_cordic/[15].U/n227_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n226_1_s  (
	.I0(\u_cordic/z[15] [6]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n227_1_1 ),
	.COUT(\u_cordic/[15].U/n226_1_1 ),
	.SUM(\u_cordic/[15].U/n226_2 )
);
defparam \u_cordic/[15].U/n226_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n225_1_s  (
	.I0(\u_cordic/z[15] [7]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n226_1_1 ),
	.COUT(\u_cordic/[15].U/n225_1_1 ),
	.SUM(\u_cordic/[15].U/n225_2 )
);
defparam \u_cordic/[15].U/n225_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n224_1_s  (
	.I0(\u_cordic/z[15] [8]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n225_1_1 ),
	.COUT(\u_cordic/[15].U/n224_1_1 ),
	.SUM(\u_cordic/[15].U/n224_2 )
);
defparam \u_cordic/[15].U/n224_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n223_1_s  (
	.I0(\u_cordic/z[15] [9]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n224_1_1 ),
	.COUT(\u_cordic/[15].U/n223_1_1 ),
	.SUM(\u_cordic/[15].U/n223_2 )
);
defparam \u_cordic/[15].U/n223_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n222_1_s  (
	.I0(\u_cordic/z[15] [10]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n223_1_1 ),
	.COUT(\u_cordic/[15].U/n222_1_1 ),
	.SUM(\u_cordic/[15].U/n222_2 )
);
defparam \u_cordic/[15].U/n222_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n221_1_s  (
	.I0(\u_cordic/z[15] [11]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n222_1_1 ),
	.COUT(\u_cordic/[15].U/n221_1_1 ),
	.SUM(\u_cordic/[15].U/n221_2 )
);
defparam \u_cordic/[15].U/n221_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n220_1_s  (
	.I0(\u_cordic/z[15] [12]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n221_1_1 ),
	.COUT(\u_cordic/[15].U/n220_1_1 ),
	.SUM(\u_cordic/[15].U/n220_2 )
);
defparam \u_cordic/[15].U/n220_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n219_1_s  (
	.I0(\u_cordic/z[15] [13]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n220_1_1 ),
	.COUT(\u_cordic/[15].U/n219_1_1 ),
	.SUM(\u_cordic/[15].U/n219_2 )
);
defparam \u_cordic/[15].U/n219_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n218_1_s  (
	.I0(\u_cordic/z[15] [14]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n219_1_1 ),
	.COUT(\u_cordic/[15].U/n218_1_1 ),
	.SUM(\u_cordic/[15].U/n218_2 )
);
defparam \u_cordic/[15].U/n218_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n217_1_s  (
	.I0(\u_cordic/z[15] [15]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n218_1_1 ),
	.COUT(\u_cordic/[15].U/n217_1_1 ),
	.SUM(\u_cordic/[15].U/n217_2 )
);
defparam \u_cordic/[15].U/n217_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n216_1_s  (
	.I0(\u_cordic/z[15] [16]),
	.I1(GND),
	.I3(\u_cordic/z[15] [16]),
	.CIN(\u_cordic/[15].U/n217_1_1 ),
	.COUT(\u_cordic/[15].U/n216_1_0_COUT ),
	.SUM(\u_cordic/[15].U/n216_2 )
);
defparam \u_cordic/[15].U/n216_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n215_1_s  (
	.I0(\u_cordic/y[15] [0]),
	.I1(\u_cordic/x[15] [15]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/z[15] [16]),
	.COUT(\u_cordic/[15].U/n215_1_1 ),
	.SUM(\u_cordic/[15].U/n215_2 )
);
defparam \u_cordic/[15].U/n215_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n214_1_s  (
	.I0(\u_cordic/y[15] [1]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n215_1_1 ),
	.COUT(\u_cordic/[15].U/n214_1_1 ),
	.SUM(\u_cordic/[15].U/n214_2 )
);
defparam \u_cordic/[15].U/n214_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n213_1_s  (
	.I0(\u_cordic/y[15] [2]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n214_1_1 ),
	.COUT(\u_cordic/[15].U/n213_1_1 ),
	.SUM(\u_cordic/[15].U/n213_2 )
);
defparam \u_cordic/[15].U/n213_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n212_1_s  (
	.I0(\u_cordic/y[15] [3]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n213_1_1 ),
	.COUT(\u_cordic/[15].U/n212_1_1 ),
	.SUM(\u_cordic/[15].U/n212_2 )
);
defparam \u_cordic/[15].U/n212_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n211_1_s  (
	.I0(\u_cordic/y[15] [4]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n212_1_1 ),
	.COUT(\u_cordic/[15].U/n211_1_1 ),
	.SUM(\u_cordic/[15].U/n211_2 )
);
defparam \u_cordic/[15].U/n211_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n210_1_s  (
	.I0(\u_cordic/y[15] [5]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n211_1_1 ),
	.COUT(\u_cordic/[15].U/n210_1_1 ),
	.SUM(\u_cordic/[15].U/n210_2 )
);
defparam \u_cordic/[15].U/n210_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n209_1_s  (
	.I0(\u_cordic/y[15] [6]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n210_1_1 ),
	.COUT(\u_cordic/[15].U/n209_1_1 ),
	.SUM(\u_cordic/[15].U/n209_2 )
);
defparam \u_cordic/[15].U/n209_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n208_1_s  (
	.I0(\u_cordic/y[15] [7]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n209_1_1 ),
	.COUT(\u_cordic/[15].U/n208_1_1 ),
	.SUM(\u_cordic/[15].U/n208_2 )
);
defparam \u_cordic/[15].U/n208_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n207_1_s  (
	.I0(\u_cordic/y[15] [8]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n208_1_1 ),
	.COUT(\u_cordic/[15].U/n207_1_1 ),
	.SUM(\u_cordic/[15].U/n207_2 )
);
defparam \u_cordic/[15].U/n207_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n206_1_s  (
	.I0(\u_cordic/y[15] [9]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n207_1_1 ),
	.COUT(\u_cordic/[15].U/n206_1_1 ),
	.SUM(\u_cordic/[15].U/n206_2 )
);
defparam \u_cordic/[15].U/n206_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n205_1_s  (
	.I0(\u_cordic/y[15] [10]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n206_1_1 ),
	.COUT(\u_cordic/[15].U/n205_1_1 ),
	.SUM(\u_cordic/[15].U/n205_2 )
);
defparam \u_cordic/[15].U/n205_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n204_1_s  (
	.I0(\u_cordic/y[15] [11]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n205_1_1 ),
	.COUT(\u_cordic/[15].U/n204_1_1 ),
	.SUM(\u_cordic/[15].U/n204_2 )
);
defparam \u_cordic/[15].U/n204_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n203_1_s  (
	.I0(\u_cordic/y[15] [12]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n204_1_1 ),
	.COUT(\u_cordic/[15].U/n203_1_1 ),
	.SUM(\u_cordic/[15].U/n203_2 )
);
defparam \u_cordic/[15].U/n203_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n202_1_s  (
	.I0(\u_cordic/y[15] [13]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n203_1_1 ),
	.COUT(\u_cordic/[15].U/n202_1_1 ),
	.SUM(\u_cordic/[15].U/n202_2 )
);
defparam \u_cordic/[15].U/n202_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n201_1_s  (
	.I0(\u_cordic/y[15] [14]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n202_1_1 ),
	.COUT(\u_cordic/[15].U/n201_1_1 ),
	.SUM(\u_cordic/[15].U/n201_2 )
);
defparam \u_cordic/[15].U/n201_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n200_1_s  (
	.I0(\u_cordic/y[15] [15]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n201_1_1 ),
	.COUT(\u_cordic/[15].U/n200_1_1 ),
	.SUM(\u_cordic/[15].U/n200_2 )
);
defparam \u_cordic/[15].U/n200_1_s .ALU_MODE=2;
ALU \u_cordic/[15].U/n199_1_s  (
	.I0(\u_cordic/y[15] [16]),
	.I1(\u_cordic/x[15] [16]),
	.I3(\u_cordic/z[15][16]_1_5 ),
	.CIN(\u_cordic/[15].U/n200_1_1 ),
	.COUT(\u_cordic/[15].U/n199_1_0_COUT ),
	.SUM(\u_cordic/[15].U/n199_2 )
);
defparam \u_cordic/[15].U/n199_1_s .ALU_MODE=2;
endmodule
