<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
<title>synthesis Report</title>
<style type="text/css">
body { font-family: Verdana, Arial, sans-serif; font-size: 12px; }
div#main_wrapper{ width: 100%; }
div#content { margin-left: 350px; margin-right: 30px; }
div#catalog_wrapper {position: fixed; top: 30px; width: 350px; float: left; }
div#catalog ul { list-style-type: none; }
div#catalog li { text-align: left; list-style-type:circle; color: #0084ff; margin-top: 3px; margin-bottom: 3px; }
div#catalog a { display:inline-block; text-decoration: none; color: #0084ff; font-weight: bold; padding: 3px; }
div#catalog a:visited { color: #0084ff; }
div#catalog a:hover { color: #fff; background: #0084ff; }
hr { margin-top: 30px; margin-bottom: 30px; }
h1, h3 { text-align: center; }
h1 {margin-top: 50px; }
table, th, td { border: 1px solid #aaa; }
table { border-collapse:collapse; margin-top: 10px; margin-bottom: 20px; width: 100%; }
th, td { padding: 5px 5px 5px 5px; }
th { color: #fff; font-weight: bold; background-color: #0084ff; }
table.summary_table td.label { width: 24%; min-width: 200px; background-color: #dee8f4; }
table.detail_table td.label { min-width: 100px; width: 8%;}
</style>
</head>
<body>
<div id="main_wrapper">
<div id="catalog_wrapper">
<div id="catalog">
<ul>
<li><a href="#about" style=" font-size: 16px;">Synthesis Messages</a></li>
<li><a href="#summary" style=" font-size: 16px;">Synthesis Details</a></li>
<li><a href="#resource" style=" font-size: 16px;">Resource</a>
<ul>
<li><a href="#usage" style=" font-size: 14px;">Resource Usage Summary</a></li>
<li><a href="#utilization" style=" font-size: 14px;">Resource Utilization Summary</a></li>
</ul>
</li>
<li><a href="#timing" style=" font-size: 16px;">Timing</a>
<ul>
<li><a href="#clock" style=" font-size: 14px;">Clock Summary</a></li>
<li><a href="#performance" style=" font-size: 14px;">Max Frequency Summary</a></li>
<li><a href="#detail timing" style=" font-size: 14px;">Detail Timing Paths Informations</a></li>
</ul>
</li>
</ul>
</div><!-- catalog -->
</div><!-- catalog_wrapper -->
<div id="content">
<h1><a name="about">Synthesis Messages</a></h1>
<table class="summary_table">
<tr>
<td class="label">Report Title</td>
<td>GowinSynthesis Report</td>
</tr>
<tr>
<td class="label">Design File</td>
<td>G:\Gowin\Gowin_V1.9.11.03_Education_x64_win\Gowin\Gowin_V1.9.11.03_Education_x64\IDE\ipcore\CORDIC\data\cordic.v<br>
G:\Gowin\Gowin_V1.9.11.03_Education_x64_win\Gowin\Gowin_V1.9.11.03_Education_x64\IDE\ipcore\CORDIC\data\cordic_wrap.v<br>
</td>
</tr>
<tr>
<td class="label">GowinSynthesis Constraints File</td>
<td>---</td>
</tr>
<tr>
<td class="label">Tool Version</td>
<td>V1.9.11.03 Education</td>
</tr>
<tr>
<td class="label">Part Number</td>
<td>GW2A-LV18PG256C8/I7</td>
</tr>
<tr>
<td class="label">Device</td>
<td>GW2A-18</td>
</tr>
<tr>
<td class="label">Device Version</td>
<td>C</td>
</tr>
<tr>
<td class="label">Created Time</td>
<td>Wed Jul 30 11:11:27 2025
</td>
</tr>
<tr>
<td class="label">Legal Announcement</td>
<td>Copyright (C)2014-2025 Gowin Semiconductor Corporation. ALL rights reserved.</td>
</tr>
</table>
<h1><a name="summary">Synthesis Details</a></h1>
<table class="summary_table">
<tr>
<td class="label">Top Level Module</td>
<td>CORDIC_Top</td>
</tr>
<tr>
<td class="label">Synthesis Process</td>
<td>Running parser:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.125s, Elapsed time = 0h 0m 0.136s, Peak memory usage = 46.316MB<br/>Running netlist conversion:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.028s, Peak memory usage = 46.316MB<br/>Running device independent optimization:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 0: CPU time = 0h 0m 0.078s, Elapsed time = 0h 0m 0.084s, Peak memory usage = 46.316MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 1: CPU time = 0h 0m 0.046s, Elapsed time = 0h 0m 0.046s, Peak memory usage = 46.316MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Optimizing Phase 2: CPU time = 0h 0m 0.14s, Elapsed time = 0h 0m 0.138s, Peak memory usage = 46.316MB<br/>Running inference:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 0: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.007s, Peak memory usage = 46.316MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 1: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.005s, Peak memory usage = 46.316MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 2: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.004s, Peak memory usage = 46.316MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Inferring Phase 3: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.016s, Peak memory usage = 46.316MB<br/>Running technical mapping:<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 0: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.012s, Peak memory usage = 46.316MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 1: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.006s, Peak memory usage = 46.316MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 2: CPU time = 0h 0m 0s, Elapsed time = 0h 0m 0.004s, Peak memory usage = 46.316MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 3: CPU time = 0h 0m 0.046s, Elapsed time = 0h 0m 0.073s, Peak memory usage = 66.984MB<br/>&nbsp;&nbsp;&nbsp;&nbsp;Tech-Mapping Phase 4: CPU time = 0h 0m 0.015s, Elapsed time = 0h 0m 0.009s, Peak memory usage = 66.984MB<br/>Generate output files:<br/>&nbsp;&nbsp;&nbsp;&nbsp;CPU time = 0h 0m 0.046s, Elapsed time = 0h 0m 0.047s, Peak memory usage = 66.984MB<br/></td>
</tr>
<tr>
<td class="label">Total Time and Memory Usage</td>
<td>CPU time = 0h 0m 0.556s, Elapsed time = 0h 0m 0.615s, Peak memory usage = 66.984MB</td>
</tr>
</table>
<h1><a name="resource">Resource</a></h1>
<h2><a name="usage">Resource Usage Summary</a></h2>
<table class="summary_table">
<tr>
<td class="label"><b>Resource</b></td>
<td><b>Usage</b></td>
</tr>
<tr>
<td class="label"><b>I/O Port </b></td>
<td>104</td>
</tr>
<tr>
<td class="label"><b>I/O Buf </b></td>
<td>104</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspIBUF</td>
<td>53</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspOBUF</td>
<td>51</td>
</tr>
<tr>
<td class="label"><b>Register </b></td>
<td>816</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspDFFR</td>
<td>816</td>
</tr>
<tr>
<td class="label"><b>ALU </b></td>
<td>758</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspALU</td>
<td>758</td>
</tr>
<tr>
<td class="label"><b>INV </b></td>
<td>16</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp&nbsp&nbspINV</td>
<td>16</td>
</tr>
</table>
<h2><a name="utilization">Resource Utilization Summary</a></h2>
<table class="summary_table">
<tr>
<td class="label"><b>Resource</b></td>
<td><b>Usage</b></td>
<td><b>Utilization</b></td>
</tr>
<tr>
<td class="label">Logic</td>
<td>774(16 LUT, 758 ALU) / 20736</td>
<td>4%</td>
</tr>
<tr>
<td class="label">Register</td>
<td>816 / 16173</td>
<td>6%</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp--Register as Latch</td>
<td>0 / 16173</td>
<td>0%</td>
</tr>
<tr>
<td class="label">&nbsp&nbsp--Register as FF</td>
<td>816 / 16173</td>
<td>6%</td>
</tr>
<tr>
<td class="label">BSRAM</td>
<td>0 / 46</td>
<td>0%</td>
</tr>
</table>
<h1><a name="timing">Timing</a></h1>
<h2><a name="clock">Clock Summary:</a></h2>
<table class="summary_table">
<tr>
<th>NO.</th>
<th>Clock Name</th>
<th>Type</th>
<th>Period</th>
<th>Frequency(MHz)</th>
<th>Rise</th>
<th>Fall</th>
<th>Source</th>
<th>Master</th>
<th>Object</th>
</tr>
<tr>
<td>1</td>
<td>clk</td>
<td>Base</td>
<td>10.000</td>
<td>100.000</td>
<td>0.000</td>
<td>5.000</td>
<td> </td>
<td> </td>
<td>clk_ibuf/I </td>
</tr>
</table>
<h2><a name="performance">Max Frequency Summary:</a></h2>
<table class="summary_table">
<tr>
<th>NO.</th>
<th>Clock Name</th>
<th>Constraint</th>
<th>Actual Fmax</th>
<th>Logic Level</th>
<th>Entity</th>
</tr>
<tr>
<td>1</td>
<td>clk</td>
<td>100.000(MHz)</td>
<td>279.720(MHz)</td>
<td>5</td>
<td>TOP</td>
</tr>
</table>
<h2><a name="detail timing">Detail Timing Paths Information</a></h2>
<h3>Path&nbsp1</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>6.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>3.900</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_cordic/[14].U/z_1_16_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_cordic/[15].U/y_1_16_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>816</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_cordic/[14].U/z_1_16_s0/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>37</td>
<td>u_cordic/[14].U/z_1_16_s0/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/z[15][16]_1_s3/I0</td>
</tr>
<tr>
<td>1.583</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>19</td>
<td>u_cordic/[14].U/z[15][16]_1_s3/F</td>
</tr>
<tr>
<td>2.057</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n215_1_s/I3</td>
</tr>
<tr>
<td>2.428</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n215_1_s/COUT</td>
</tr>
<tr>
<td>2.428</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n214_1_s/CIN</td>
</tr>
<tr>
<td>2.463</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n214_1_s/COUT</td>
</tr>
<tr>
<td>2.463</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n213_1_s/CIN</td>
</tr>
<tr>
<td>2.498</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n213_1_s/COUT</td>
</tr>
<tr>
<td>2.498</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n212_1_s/CIN</td>
</tr>
<tr>
<td>2.534</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n212_1_s/COUT</td>
</tr>
<tr>
<td>2.534</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n211_1_s/CIN</td>
</tr>
<tr>
<td>2.569</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n211_1_s/COUT</td>
</tr>
<tr>
<td>2.569</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n210_1_s/CIN</td>
</tr>
<tr>
<td>2.604</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n210_1_s/COUT</td>
</tr>
<tr>
<td>2.604</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n209_1_s/CIN</td>
</tr>
<tr>
<td>2.639</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n209_1_s/COUT</td>
</tr>
<tr>
<td>2.639</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n208_1_s/CIN</td>
</tr>
<tr>
<td>2.674</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n208_1_s/COUT</td>
</tr>
<tr>
<td>2.674</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n207_1_s/CIN</td>
</tr>
<tr>
<td>2.710</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n207_1_s/COUT</td>
</tr>
<tr>
<td>2.710</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n206_1_s/CIN</td>
</tr>
<tr>
<td>2.745</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n206_1_s/COUT</td>
</tr>
<tr>
<td>2.745</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n205_1_s/CIN</td>
</tr>
<tr>
<td>2.780</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n205_1_s/COUT</td>
</tr>
<tr>
<td>2.780</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n204_1_s/CIN</td>
</tr>
<tr>
<td>2.815</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n204_1_s/COUT</td>
</tr>
<tr>
<td>2.815</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n203_1_s/CIN</td>
</tr>
<tr>
<td>2.850</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n203_1_s/COUT</td>
</tr>
<tr>
<td>2.850</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n202_1_s/CIN</td>
</tr>
<tr>
<td>2.886</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n202_1_s/COUT</td>
</tr>
<tr>
<td>2.886</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n201_1_s/CIN</td>
</tr>
<tr>
<td>2.921</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n201_1_s/COUT</td>
</tr>
<tr>
<td>2.921</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n200_1_s/CIN</td>
</tr>
<tr>
<td>2.956</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n200_1_s/COUT</td>
</tr>
<tr>
<td>2.956</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[15].U/n199_1_s/CIN</td>
</tr>
<tr>
<td>3.426</td>
<td>0.470</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/n199_1_s/SUM</td>
</tr>
<tr>
<td>3.900</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[15].U/y_1_16_s0/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>816</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_cordic/[15].U/y_1_16_s0/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_cordic/[15].U/y_1_16_s0</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 1.886, 53.277%; route: 1.422, 40.169%; tC2Q: 0.232, 6.554%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp2</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>6.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>3.900</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_cordic/[13].U/z_1_16_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_cordic/[14].U/y_1_16_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>816</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_cordic/[13].U/z_1_16_s0/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>36</td>
<td>u_cordic/[13].U/z_1_16_s0/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/z[14][16]_1_s3/I0</td>
</tr>
<tr>
<td>1.583</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>19</td>
<td>u_cordic/[13].U/z[14][16]_1_s3/F</td>
</tr>
<tr>
<td>2.057</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n213_1_s/I3</td>
</tr>
<tr>
<td>2.428</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n213_1_s/COUT</td>
</tr>
<tr>
<td>2.428</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n212_1_s/CIN</td>
</tr>
<tr>
<td>2.463</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n212_1_s/COUT</td>
</tr>
<tr>
<td>2.463</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n211_1_s/CIN</td>
</tr>
<tr>
<td>2.498</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n211_1_s/COUT</td>
</tr>
<tr>
<td>2.498</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n210_1_s/CIN</td>
</tr>
<tr>
<td>2.534</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n210_1_s/COUT</td>
</tr>
<tr>
<td>2.534</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n209_1_s/CIN</td>
</tr>
<tr>
<td>2.569</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n209_1_s/COUT</td>
</tr>
<tr>
<td>2.569</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n208_1_s/CIN</td>
</tr>
<tr>
<td>2.604</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n208_1_s/COUT</td>
</tr>
<tr>
<td>2.604</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n207_1_s/CIN</td>
</tr>
<tr>
<td>2.639</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n207_1_s/COUT</td>
</tr>
<tr>
<td>2.639</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n206_1_s/CIN</td>
</tr>
<tr>
<td>2.674</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n206_1_s/COUT</td>
</tr>
<tr>
<td>2.674</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n205_1_s/CIN</td>
</tr>
<tr>
<td>2.710</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n205_1_s/COUT</td>
</tr>
<tr>
<td>2.710</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n204_1_s/CIN</td>
</tr>
<tr>
<td>2.745</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n204_1_s/COUT</td>
</tr>
<tr>
<td>2.745</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n203_1_s/CIN</td>
</tr>
<tr>
<td>2.780</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n203_1_s/COUT</td>
</tr>
<tr>
<td>2.780</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n202_1_s/CIN</td>
</tr>
<tr>
<td>2.815</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n202_1_s/COUT</td>
</tr>
<tr>
<td>2.815</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n201_1_s/CIN</td>
</tr>
<tr>
<td>2.850</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n201_1_s/COUT</td>
</tr>
<tr>
<td>2.850</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n200_1_s/CIN</td>
</tr>
<tr>
<td>2.886</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n200_1_s/COUT</td>
</tr>
<tr>
<td>2.886</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n199_1_s/CIN</td>
</tr>
<tr>
<td>2.921</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n199_1_s/COUT</td>
</tr>
<tr>
<td>2.921</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n198_1_s/CIN</td>
</tr>
<tr>
<td>2.956</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n198_1_s/COUT</td>
</tr>
<tr>
<td>2.956</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[14].U/n197_1_s/CIN</td>
</tr>
<tr>
<td>3.426</td>
<td>0.470</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/n197_1_s/SUM</td>
</tr>
<tr>
<td>3.900</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[14].U/y_1_16_s0/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>816</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_cordic/[14].U/y_1_16_s0/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_cordic/[14].U/y_1_16_s0</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 1.886, 53.277%; route: 1.422, 40.169%; tC2Q: 0.232, 6.554%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp3</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>6.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>3.900</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_cordic/[12].U/z_1_16_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_cordic/[13].U/y_1_16_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>816</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_cordic/[12].U/z_1_16_s0/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>35</td>
<td>u_cordic/[12].U/z_1_16_s0/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/z[13][16]_1_s3/I0</td>
</tr>
<tr>
<td>1.583</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>19</td>
<td>u_cordic/[12].U/z[13][16]_1_s3/F</td>
</tr>
<tr>
<td>2.057</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n211_1_s/I3</td>
</tr>
<tr>
<td>2.428</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n211_1_s/COUT</td>
</tr>
<tr>
<td>2.428</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n210_1_s/CIN</td>
</tr>
<tr>
<td>2.463</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n210_1_s/COUT</td>
</tr>
<tr>
<td>2.463</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n209_1_s/CIN</td>
</tr>
<tr>
<td>2.498</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n209_1_s/COUT</td>
</tr>
<tr>
<td>2.498</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n208_1_s/CIN</td>
</tr>
<tr>
<td>2.534</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n208_1_s/COUT</td>
</tr>
<tr>
<td>2.534</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n207_1_s/CIN</td>
</tr>
<tr>
<td>2.569</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n207_1_s/COUT</td>
</tr>
<tr>
<td>2.569</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n206_1_s/CIN</td>
</tr>
<tr>
<td>2.604</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n206_1_s/COUT</td>
</tr>
<tr>
<td>2.604</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n205_1_s/CIN</td>
</tr>
<tr>
<td>2.639</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n205_1_s/COUT</td>
</tr>
<tr>
<td>2.639</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n204_1_s/CIN</td>
</tr>
<tr>
<td>2.674</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n204_1_s/COUT</td>
</tr>
<tr>
<td>2.674</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n203_1_s/CIN</td>
</tr>
<tr>
<td>2.710</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n203_1_s/COUT</td>
</tr>
<tr>
<td>2.710</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n202_1_s/CIN</td>
</tr>
<tr>
<td>2.745</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n202_1_s/COUT</td>
</tr>
<tr>
<td>2.745</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n201_1_s/CIN</td>
</tr>
<tr>
<td>2.780</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n201_1_s/COUT</td>
</tr>
<tr>
<td>2.780</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n200_1_s/CIN</td>
</tr>
<tr>
<td>2.815</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n200_1_s/COUT</td>
</tr>
<tr>
<td>2.815</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n199_1_s/CIN</td>
</tr>
<tr>
<td>2.850</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n199_1_s/COUT</td>
</tr>
<tr>
<td>2.850</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n198_1_s/CIN</td>
</tr>
<tr>
<td>2.886</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n198_1_s/COUT</td>
</tr>
<tr>
<td>2.886</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n197_1_s/CIN</td>
</tr>
<tr>
<td>2.921</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n197_1_s/COUT</td>
</tr>
<tr>
<td>2.921</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n196_1_s/CIN</td>
</tr>
<tr>
<td>2.956</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n196_1_s/COUT</td>
</tr>
<tr>
<td>2.956</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[13].U/n195_1_s/CIN</td>
</tr>
<tr>
<td>3.426</td>
<td>0.470</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/n195_1_s/SUM</td>
</tr>
<tr>
<td>3.900</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[13].U/y_1_16_s0/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>816</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_cordic/[13].U/y_1_16_s0/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_cordic/[13].U/y_1_16_s0</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 1.886, 53.277%; route: 1.422, 40.169%; tC2Q: 0.232, 6.554%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp4</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>6.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>3.900</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_cordic/[11].U/z_1_16_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_cordic/[12].U/y_1_16_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>816</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_cordic/[11].U/z_1_16_s0/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>34</td>
<td>u_cordic/[11].U/z_1_16_s0/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/z[12][16]_1_s3/I0</td>
</tr>
<tr>
<td>1.583</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>19</td>
<td>u_cordic/[11].U/z[12][16]_1_s3/F</td>
</tr>
<tr>
<td>2.057</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n209_1_s/I3</td>
</tr>
<tr>
<td>2.428</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n209_1_s/COUT</td>
</tr>
<tr>
<td>2.428</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n208_1_s/CIN</td>
</tr>
<tr>
<td>2.463</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n208_1_s/COUT</td>
</tr>
<tr>
<td>2.463</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n207_1_s/CIN</td>
</tr>
<tr>
<td>2.498</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n207_1_s/COUT</td>
</tr>
<tr>
<td>2.498</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n206_1_s/CIN</td>
</tr>
<tr>
<td>2.534</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n206_1_s/COUT</td>
</tr>
<tr>
<td>2.534</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n205_1_s/CIN</td>
</tr>
<tr>
<td>2.569</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n205_1_s/COUT</td>
</tr>
<tr>
<td>2.569</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n204_1_s/CIN</td>
</tr>
<tr>
<td>2.604</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n204_1_s/COUT</td>
</tr>
<tr>
<td>2.604</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n203_1_s/CIN</td>
</tr>
<tr>
<td>2.639</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n203_1_s/COUT</td>
</tr>
<tr>
<td>2.639</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n202_1_s/CIN</td>
</tr>
<tr>
<td>2.674</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n202_1_s/COUT</td>
</tr>
<tr>
<td>2.674</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n201_1_s/CIN</td>
</tr>
<tr>
<td>2.710</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n201_1_s/COUT</td>
</tr>
<tr>
<td>2.710</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n200_1_s/CIN</td>
</tr>
<tr>
<td>2.745</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n200_1_s/COUT</td>
</tr>
<tr>
<td>2.745</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n199_1_s/CIN</td>
</tr>
<tr>
<td>2.780</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n199_1_s/COUT</td>
</tr>
<tr>
<td>2.780</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n198_1_s/CIN</td>
</tr>
<tr>
<td>2.815</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n198_1_s/COUT</td>
</tr>
<tr>
<td>2.815</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n197_1_s/CIN</td>
</tr>
<tr>
<td>2.850</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n197_1_s/COUT</td>
</tr>
<tr>
<td>2.850</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n196_1_s/CIN</td>
</tr>
<tr>
<td>2.886</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n196_1_s/COUT</td>
</tr>
<tr>
<td>2.886</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n195_1_s/CIN</td>
</tr>
<tr>
<td>2.921</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n195_1_s/COUT</td>
</tr>
<tr>
<td>2.921</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n194_1_s/CIN</td>
</tr>
<tr>
<td>2.956</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n194_1_s/COUT</td>
</tr>
<tr>
<td>2.956</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[12].U/n193_1_s/CIN</td>
</tr>
<tr>
<td>3.426</td>
<td>0.470</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/n193_1_s/SUM</td>
</tr>
<tr>
<td>3.900</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[12].U/y_1_16_s0/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>816</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_cordic/[12].U/y_1_16_s0/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_cordic/[12].U/y_1_16_s0</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 1.886, 53.277%; route: 1.422, 40.169%; tC2Q: 0.232, 6.554%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
<h3>Path&nbsp5</h3>
<b>Path Summary:</b></br>
<table class="summary_table">
<tr>
<td class="label">Slack</td>
<td>6.425</td>
</tr>
<tr>
<td class="label">Data Arrival Time</td>
<td>3.900</td>
</tr>
<tr>
<td class="label">Data Required Time</td>
<td>10.325</td>
</tr>
<tr>
<td class="label">From</td>
<td>u_cordic/[10].U/z_1_16_s0</td>
</tr>
<tr>
<td class="label">To</td>
<td>u_cordic/[11].U/y_1_16_s0</td>
</tr>
<tr>
<td class="label">Launch Clk</td>
<td>clk[R]</td>
</tr>
<tr>
<td class="label">Latch Clk</td>
<td>clk[R]</td>
</tr>
</table>
<b>Data Arrival Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>0.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>816</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>0.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_cordic/[10].U/z_1_16_s0/CLK</td>
</tr>
<tr>
<td>0.592</td>
<td>0.232</td>
<td>tC2Q</td>
<td>RF</td>
<td>33</td>
<td>u_cordic/[10].U/z_1_16_s0/Q</td>
</tr>
<tr>
<td>1.066</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[10].U/z[11][16]_1_s3/I0</td>
</tr>
<tr>
<td>1.583</td>
<td>0.517</td>
<td>tINS</td>
<td>FF</td>
<td>19</td>
<td>u_cordic/[10].U/z[11][16]_1_s3/F</td>
</tr>
<tr>
<td>2.057</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n207_1_s/I3</td>
</tr>
<tr>
<td>2.428</td>
<td>0.371</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n207_1_s/COUT</td>
</tr>
<tr>
<td>2.428</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n206_1_s/CIN</td>
</tr>
<tr>
<td>2.463</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n206_1_s/COUT</td>
</tr>
<tr>
<td>2.463</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n205_1_s/CIN</td>
</tr>
<tr>
<td>2.498</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n205_1_s/COUT</td>
</tr>
<tr>
<td>2.498</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n204_1_s/CIN</td>
</tr>
<tr>
<td>2.534</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n204_1_s/COUT</td>
</tr>
<tr>
<td>2.534</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n203_1_s/CIN</td>
</tr>
<tr>
<td>2.569</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n203_1_s/COUT</td>
</tr>
<tr>
<td>2.569</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n202_1_s/CIN</td>
</tr>
<tr>
<td>2.604</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n202_1_s/COUT</td>
</tr>
<tr>
<td>2.604</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n201_1_s/CIN</td>
</tr>
<tr>
<td>2.639</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n201_1_s/COUT</td>
</tr>
<tr>
<td>2.639</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n200_1_s/CIN</td>
</tr>
<tr>
<td>2.674</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n200_1_s/COUT</td>
</tr>
<tr>
<td>2.674</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n199_1_s/CIN</td>
</tr>
<tr>
<td>2.710</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n199_1_s/COUT</td>
</tr>
<tr>
<td>2.710</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n198_1_s/CIN</td>
</tr>
<tr>
<td>2.745</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n198_1_s/COUT</td>
</tr>
<tr>
<td>2.745</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n197_1_s/CIN</td>
</tr>
<tr>
<td>2.780</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n197_1_s/COUT</td>
</tr>
<tr>
<td>2.780</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n196_1_s/CIN</td>
</tr>
<tr>
<td>2.815</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n196_1_s/COUT</td>
</tr>
<tr>
<td>2.815</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n195_1_s/CIN</td>
</tr>
<tr>
<td>2.850</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n195_1_s/COUT</td>
</tr>
<tr>
<td>2.850</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n194_1_s/CIN</td>
</tr>
<tr>
<td>2.886</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n194_1_s/COUT</td>
</tr>
<tr>
<td>2.886</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n193_1_s/CIN</td>
</tr>
<tr>
<td>2.921</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n193_1_s/COUT</td>
</tr>
<tr>
<td>2.921</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n192_1_s/CIN</td>
</tr>
<tr>
<td>2.956</td>
<td>0.035</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n192_1_s/COUT</td>
</tr>
<tr>
<td>2.956</td>
<td>0.000</td>
<td>tNET</td>
<td>FF</td>
<td>2</td>
<td>u_cordic/[11].U/n191_1_s/CIN</td>
</tr>
<tr>
<td>3.426</td>
<td>0.470</td>
<td>tINS</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/n191_1_s/SUM</td>
</tr>
<tr>
<td>3.900</td>
<td>0.474</td>
<td>tNET</td>
<td>FF</td>
<td>1</td>
<td>u_cordic/[11].U/y_1_16_s0/D</td>
</tr>
</table>
<b>Data Required Path:</b>
<table class="summary_table">
<tr>
<th>AT</th>
<th>DELAY</th>
<th>TYPE</th>
<th>RF</th>
<th>FANOUT</th>
<th>NODE</th>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td> </td>
<td> </td>
<td> </td>
<td>clk</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tCL</td>
<td>RR</td>
<td>1</td>
<td>clk_ibuf/I</td>
</tr>
<tr>
<td>10.000</td>
<td>0.000</td>
<td>tINS</td>
<td>RR</td>
<td>816</td>
<td>clk_ibuf/O</td>
</tr>
<tr>
<td>10.360</td>
<td>0.360</td>
<td>tNET</td>
<td>RR</td>
<td>1</td>
<td>u_cordic/[11].U/y_1_16_s0/CLK</td>
</tr>
<tr>
<td>10.325</td>
<td>-0.035</td>
<td>tSu</td>
<td> </td>
<td>1</td>
<td>u_cordic/[11].U/y_1_16_s0</td>
</tr>
</table>
<b>Path Statistics:</b>
<table class="summary_table">
<tr>
<td class="label">Clock Skew:</td>
<td>0.000</td>
</tr>
<tr>
<td class="label">Setup Relationship:</td>
<td>10.000</td>
</tr>
<tr>
<td class="label">Logic Level:</td>
<td>5</td>
</tr>
<tr>
<td class="label">Arrival Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
<tr>
<td class="label">Arrival Data Path Delay:</td><td> cell: 1.886, 53.277%; route: 1.422, 40.169%; tC2Q: 0.232, 6.554%</td></tr>
<tr>
<td class="label">Required Clock Path Delay:</td><td> cell: 0.000, 0.000%; route: 0.360, 100.000%</td></tr>
</table>
<br/>
</div><!-- content -->
</div><!-- main_wrapper -->
</body>
</html>
